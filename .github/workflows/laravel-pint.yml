name: Check & fix styling

on: [push]

permissions:
    contents: write

jobs:
  laravel-pint:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ github.head_ref }}

      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: '8.4'
          tools: composer:v2
          coverage: none

      - name: Install dependencies
        run: composer install --no-interaction --no-progress

      - name: Fix code style issues
        run: vendor/bin/pint

      - name: Commit changes
        uses: stefanzweifel/git-auto-commit-action@v5
        with:
          commit_message: Fix styling
