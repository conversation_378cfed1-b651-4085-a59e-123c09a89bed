*.log
.DS_Store
.env
.env.backup
.env.production
.phpactor.json
.phpunit.result.cache
/.fleet
/.idea
/.nova
/.phpunit.cache
/.vscode
/.zed
/auth.json
/node_modules
/public/build
/public/hot
/public/storage
/storage/*.key
/storage/pail
/vendor
/public/sitemap.xml
Thumbs.db
/bower_components
/.vagrant
Homestead.json
Homestead.yaml
npm-debug.log
sftp-config.json
.phpstorm.meta.php
_ide_helper_models.php
/storage/app/backup
/storage/cache_keys.json

__MACOSX
/.phpintel
/.php_cs.cache
*vendor
*.zip
storage*

public/vendor/core

_ide_helper.php
yarn.lock
bun.lockb
/.sass-cache

public/mix-manifest.json

public/themes
public/docs
resources/docs

/storage/.license
/storage/fonts
/storage/framework/*.txt
/storage/installing
/storage/installed

/lang/*
!/lang/en
!/lang/vi

/log

storage/framework/laravel-excel/
/.scribe
/.env.testing

storage/framework/maintenance.php
.yarn
.pnp.*
sitemaps

build
