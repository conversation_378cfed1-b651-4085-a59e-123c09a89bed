stages:
  - build
  - test
  - deploy

build:
  stage: build
  tags:
    - csl-176
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
  before_script:
    - git --version
    - php -v
    - composer -v
    - node -v
    - npm -v
  script:
    - cd $HOME_PATH
    - git pull
    - composer install --no-dev
    - php artisan optimize:clear
    - php artisan optimize
    - php artisan filament:optimize-clear
    - php artisan filament:optimize
    - php artisan migrate --force
    - php artisan queue:restart
    - npm install
    - npm run build

analyse:
  stage: test
  image: composer:latest
  tags:
    - csl-88176
  before_script:
    - php -v
    - composer --version
    - if [ ! -d "$CI_PROJECT_DIR/.git" ]; then
        git clone "$CI_REPOSITORY_URL" "$CI_PROJECT_DIR";
      fi
    - echo "Running in branch:" $CI_COMMIT_REF_NAME
    - composer clear-cache
    - composer config --global discard-changes true
    - rm -rf build
  script:
    - composer install --prefer-dist --no-progress
    - composer a
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
