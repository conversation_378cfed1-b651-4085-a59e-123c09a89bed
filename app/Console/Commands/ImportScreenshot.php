<?php

namespace App\Console\Commands;

use App\Models\Media;
use App\Models\Template;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;

class ImportScreenshot extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:import-screenshot';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Import screenshot to template';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $files = Storage::disk('local')->files('screenshot');

        foreach ($files as $filePath) {
            $filename = basename($filePath, '.jpeg');
            $demoUrl = 'https://'.$filename;

            /** @var null|Template $template */
            $template = Template::where('demo_url', $demoUrl)->first();

            if ($template) {
                $templateId = $template->getKey();
                $this->info("Adding media to template ID: {$templateId} ({$demoUrl})");

                $absolutePath = Storage::disk('local')->path($filePath);
                $template
                    ->addMedia($absolutePath)
                    ->toMediaCollection(Media::COLLECTION_TEMPLATE_SCREENSHOT);
            } else {
                $this->error("No template found for: {$demoUrl}");
            }
        }

        return Command::SUCCESS;
    }
}
