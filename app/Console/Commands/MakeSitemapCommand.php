<?php

namespace App\Console\Commands;

use App\Services\SitemapService;
use Illuminate\Console\Command;

class MakeSitemapCommand extends Command
{
    protected $signature = 'app:make-sitemap {--clean : <PERSON><PERSON><PERSON> tất cả file sitemap cũ trước khi tạo mới}';

    protected $description = 'Generate sitemap for the website';

    public function handle(): int
    {
        $this->info('🚀 Starting sitemap generation...');
        $startTime = microtime(true);

        try {
            $generatorService = new SitemapService;

            // Xóa sitemap cũ nếu có option --clean
            if ($this->option('clean')) {
                $generatorService->cleanOldSitemaps();
                $this->info('🧹 Cleaned up old sitemap files');
            }

            // Tạo sitemap mới
            $generatorService->generate();

            $executionTime = round(microtime(true) - $startTime, 2);
            $this->info("✅ Sitemap generated successfully in {$executionTime}s");
            $this->info('🔗 Symlink: '.public_path('sitemaps'));

            return Command::SUCCESS;
        } catch (\Exception $e) {
            $this->error('❌ Error generating sitemap: '.$e->getMessage());
            $this->error($e->getTraceAsString());

            return Command::FAILURE;
        }
    }
}
