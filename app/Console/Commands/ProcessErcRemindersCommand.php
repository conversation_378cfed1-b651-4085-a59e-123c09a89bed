<?php

namespace App\Console\Commands;

use App\Services\BusinessVerificationService;
use Illuminate\Console\Command;

class ProcessErcRemindersCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'erc:process-reminders';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Process pending ERC verification reminders';

    /**
     * Execute the console command.
     */
    public function handle(BusinessVerificationService $verificationService): int
    {
        $this->info('Processing ERC reminders...');

        try {
            $verificationService->processPendingReminders();
            $this->info('ERC reminders processed successfully.');
            return self::SUCCESS;
        } catch (\Exception $e) {
            $this->error('Failed to process ERC reminders: ' . $e->getMessage());
            return self::FAILURE;
        }
    }
}
