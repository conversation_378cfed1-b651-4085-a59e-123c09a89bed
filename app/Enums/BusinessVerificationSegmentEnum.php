<?php

namespace App\Enums;

enum BusinessVerificationSegmentEnum: string
{
    case DEFAULT = 'default';
    case DEMO = 'demo';
    case PURCHASED = 'purchased';
    
    public static function values(): array
    {
        return array_column(self::cases(), 'value');
    }
    
    public function label(): string
    {
        return match($this) {
            self::DEFAULT => __('enums.business_verification_segment.default'),
            self::DEMO => __('enums.business_verification_segment.demo'),
            self::PURCHASED => __('enums.business_verification_segment.purchased'),
        };
    }
    
    /**
     * Get the number of days to wait before sending reminder email.
     */
    public function getReminderDays(): int
    {
        return match($this) {
            self::PURCHASED => 1,
            self::DEMO => 2,
            self::DEFAULT => 3,
        };
    }
    
    /**
     * Get the priority level (higher number = higher priority).
     */
    public function getPriority(): int
    {
        return match($this) {
            self::PURCHASED => 3,
            self::DEMO => 2,
            self::DEFAULT => 1,
        };
    }
    
    /**
     * Check if this segment has higher priority than another.
     */
    public function hasHigherPriorityThan(self $other): bool
    {
        return $this->getPriority() > $other->getPriority();
    }
    
    /**
     * Get the most appropriate segment based on customer data.
     */
    public static function determineSegment(int $customerId): self
    {
        // Check if customer has active contracts (PURCHASED segment)
        $hasActiveContract = \App\Models\Contract::where('customer_id', $customerId)
            ->whereNotIn('status', [\App\Enums\ContractStatusEnum::CANCELLED->value])
            ->exists();
            
        if ($hasActiveContract) {
            return self::PURCHASED;
        }
        
        // Check if customer has demo websites (DEMO segment)
        $hasDemoWebsite = \App\Models\Website::whereHas('customerUser', function ($query) use ($customerId) {
            $query->where('customer_id', $customerId);
        })->exists();
        
        if ($hasDemoWebsite) {
            return self::DEMO;
        }
        
        return self::DEFAULT;
    }
}
