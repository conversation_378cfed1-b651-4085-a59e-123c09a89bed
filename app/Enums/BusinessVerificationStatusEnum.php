<?php

namespace App\Enums;

enum BusinessVerificationStatusEnum: string
{
    case UNSUBMITTED = 'unsubmitted';
    case SUBMITTED = 'submitted';
    case IN_REVIEW = 'in_review';
    case VERIFIED = 'verified';
    case REJECTED = 'rejected';
    
    public static function values(): array
    {
        return array_column(self::cases(), 'value');
    }
    
    public function label(): string
    {
        return match($this) {
            self::UNSUBMITTED => __('enums.business_verification_status.unsubmitted'),
            self::SUBMITTED => __('enums.business_verification_status.submitted'),
            self::IN_REVIEW => __('enums.business_verification_status.in_review'),
            self::VERIFIED => __('enums.business_verification_status.verified'),
            self::REJECTED => __('enums.business_verification_status.rejected'),
        };
    }
    
    /**
     * Get the badge class for the status.
     */
    public function badgeClass(): string
    {
        return match($this) {
            self::UNSUBMITTED => 'bg-gray-100 text-gray-800',
            self::SUBMITTED => 'bg-yellow-100 text-yellow-800',
            self::IN_REVIEW => 'bg-blue-100 text-blue-800',
            self::VERIFIED => 'bg-green-100 text-green-800',
            self::REJECTED => 'bg-red-100 text-red-800',
        };
    }
    
    /**
     * Check if the status allows file upload.
     */
    public function canUploadFile(): bool
    {
        return match($this) {
            self::UNSUBMITTED, self::REJECTED => true,
            default => false,
        };
    }
    
    /**
     * Check if the status allows admin review.
     */
    public function canBeReviewed(): bool
    {
        return match($this) {
            self::SUBMITTED, self::IN_REVIEW => true,
            default => false,
        };
    }
    
    /**
     * Check if the status is final (no more changes allowed).
     */
    public function isFinal(): bool
    {
        return $this === self::VERIFIED;
    }
    
    /**
     * Check if the status requires reminder emails.
     */
    public function needsReminder(): bool
    {
        return $this === self::UNSUBMITTED;
    }
    
    /**
     * Get the next possible statuses.
     * 
     * @return array<self>
     */
    public function getNextPossibleStatuses(): array
    {
        return match($this) {
            self::UNSUBMITTED => [self::SUBMITTED],
            self::SUBMITTED => [self::IN_REVIEW, self::VERIFIED, self::REJECTED],
            self::IN_REVIEW => [self::VERIFIED, self::REJECTED],
            self::VERIFIED => [],
            self::REJECTED => [self::SUBMITTED], // Allow resubmit
        };
    }
    
    /**
     * Check if a transition to the target status is valid.
     */
    public function canTransitionTo(self $targetStatus): bool
    {
        return in_array($targetStatus, $this->getNextPossibleStatuses());
    }
}
