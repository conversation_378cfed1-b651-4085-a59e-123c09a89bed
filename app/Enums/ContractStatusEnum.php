<?php

namespace App\Enums;

use Illuminate\Support\Carbon;

enum ContractStatusEnum: string
{
    case DRAFT = 'draft';
    case PENDING = 'pending';
    case ACTIVE = 'active';
    case EXPIRED = 'expired';
    case TERMINATED = 'terminated';
    case RENEWED = 'renewed';
    case CANCELLED = 'cancelled';
    
    public static function values(): array
    {
        return array_column(self::cases(), 'value');
    }
    
    public function label(): string
    {
        return match($this) {
            self::DRAFT => __('enums.contract_status.draft'),
            self::PENDING => __('enums.contract_status.pending'),
            self::ACTIVE => __('enums.contract_status.active'),
            self::EXPIRED => __('enums.contract_status.expired'),
            self::TERMINATED => __('enums.contract_status.terminated'),
            self::RENEWED => __('enums.contract_status.renewed'),
            self::CANCELLED => __('enums.contract_status.cancelled'),
        };
    }
    
    /**
     * Get the badge class for the status.
     */
    public function badgeClass(): string
    {
        return match($this) {
            self::DRAFT => 'bg-gray-100 text-gray-800',
            self::PENDING => 'bg-yellow-100 text-yellow-800',
            self::ACTIVE => 'bg-green-100 text-green-800',
            self::EXPIRED => 'bg-red-100 text-red-800',
            self::TERMINATED => 'bg-red-100 text-red-800',
            self::RENEWED => 'bg-blue-100 text-blue-800',
            self::CANCELLED => 'bg-gray-100 text-gray-800',
        };
    }
    
    /**
     * Check if the status is considered active.
     */
    public function isActive(): bool
    {
        return $this === self::ACTIVE;
    }
    
    /**
     * Check if the status is considered pending.
     */
    public function isPending(): bool
    {
        return $this === self::PENDING;
    }
    
    /**
     * Check if the status is considered expired.
     */
    public function isExpired(): bool
    {
        return $this === self::EXPIRED;
    }
    
    /**
     * Check if the status allows editing.
     */
    public function isEditable(): bool
    {
        return match($this) {
            self::DRAFT, self::PENDING => true,
            default => false,
        };
    }
    
    /**
     * Check if the status allows signing.
     */
    public function canBeSigned(): bool
    {
        return $this === self::DRAFT || $this === self::PENDING;
    }
    
    /**
     * Check if the status allows renewal.
     */
    public function canBeRenewed(): bool
    {
        return match($this) {
            self::ACTIVE, self::EXPIRED, self::RENEWED => true,
            default => false,
        };
    }
    
    /**
     * Check if the status allows termination.
     */
    public function canBeTerminated(): bool
    {
        return match($this) {
            self::ACTIVE, self::PENDING, self::RENEWED => true,
            default => false,
        };
    }
    
    /**
     * Check if the status allows cancellation.
     */
    public function canBeCancelled(): bool
    {
        return $this !== self::CANCELLED && $this !== self::TERMINATED;
    }
    
    /**
     * Get the next possible statuses.
     * 
     * @return array<self>
     */
    public function getNextPossibleStatuses(): array
    {
        return match($this) {
            self::DRAFT => [self::PENDING, self::ACTIVE, self::CANCELLED],
            self::PENDING => [self::ACTIVE, self::CANCELLED],
            self::ACTIVE => [self::EXPIRED, self::RENEWED, self::TERMINATED],
            self::EXPIRED => [self::RENEWED],
            self::RENEWED => [self::ACTIVE, self::EXPIRED, self::TERMINATED],
            self::TERMINATED, self::CANCELLED => [],
        };
    }
    
    /**
     * Check if a transition to the target status is valid.
     */
    public function canTransitionTo(self $targetStatus): bool
    {
        return in_array($targetStatus, $this->getNextPossibleStatuses());
    }
}
