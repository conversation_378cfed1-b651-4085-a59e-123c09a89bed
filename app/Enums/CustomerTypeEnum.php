<?php

namespace App\Enums;

use Illuminate\Support\Collection;

enum CustomerTypeEnum: string
{
    case INDIVIDUAL = 'individual';
    case BUSINESS = 'business';
    case ENTERPRISE = 'enterprise';
    case NON_PROFIT = 'non_profit';
    case GOVERNMENT = 'government';
    case EDUCATION = 'education';

    /**
     * Get the human-readable label for the enum value.
     */
    public function label(): string
    {
        return match($this) {
            self::INDIVIDUAL => __('enums.customer_type.individual'),
            self::BUSINESS => __('enums.customer_type.business'),
            self::ENTERPRISE => __('enums.customer_type.enterprise'),
            self::NON_PROFIT => __('enums.customer_type.non_profit'),
            self::GOVERNMENT => __('enums.customer_type.government'),
            self::EDUCATION => __('enums.customer_type.education'),
        };
    }

    /**
     * Get all values as an array for select/option dropdowns.
     */
    public static function toSelectArray(): array
    {
        return collect(self::cases())
            ->mapWithKeys(fn (self $type) => [$type->value => $type->label()])
            ->toArray();
    }

    /**
     * Get all values as a collection.
     */
    public static function collect(): Collection
    {
        return collect(self::cases());
    }

    /**
     * Check if the current type is a business type (business or enterprise).
     */
    public function isBusinessType(): bool
    {
        return in_array($this, [self::BUSINESS, self::ENTERPRISE]);
    }

    /**
     * Check if the current type is an organization (not individual).
     */
    public function isOrganization(): bool
    {
        return $this !== self::INDIVIDUAL;
    }
}
