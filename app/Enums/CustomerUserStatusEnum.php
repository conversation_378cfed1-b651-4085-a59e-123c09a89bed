<?php

namespace App\Enums;

enum CustomerUserStatusEnum: string
{
    case ACTIVE = 'active';
    case INACTIVE = 'inactive';

    /**
     * Get the label for the enum value.
     *
     * @return null|array<string, string>|string
     */
    public function label(): array|string|null
    {
        return match ($this) {
            self::ACTIVE => __('messages.status.active'),
            self::INACTIVE => __('messages.status.inactive'),
        };
    }
}
