<?php

namespace App\Filament\Resources;

use App\Enums\BusinessVerificationStatusEnum;
use App\Filament\Resources\BusinessVerificationResource\Pages;
use App\Models\BusinessVerification;
use App\Services\BusinessVerificationService;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Storage;

class BusinessVerificationResource extends Resource
{
    protected static ?string $model = BusinessVerification::class;

    protected static ?string $navigationIcon = 'heroicon-o-shield-check';

    protected static ?string $navigationLabel = 'Business Verifications';

    protected static ?string $modelLabel = 'Business Verification';

    protected static ?string $pluralModelLabel = 'Business Verifications';

    protected static ?int $navigationSort = 3;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Customer Information')
                    ->schema([
                        Forms\Components\TextInput::make('customer.business_name')
                            ->label('Business Name')
                            ->disabled(),
                        Forms\Components\TextInput::make('customer.tax_code')
                            ->label('Tax Code')
                            ->disabled(),
                        Forms\Components\TextInput::make('customer.email')
                            ->label('Email')
                            ->disabled(),
                    ])
                    ->columns(3),

                Forms\Components\Section::make('Verification Status')
                    ->schema([
                        Forms\Components\Select::make('status')
                            ->options(BusinessVerificationStatusEnum::class)
                            ->required()
                            ->live(),
                        Forms\Components\Select::make('segment')
                            ->options([
                                'default' => 'Default',
                                'demo' => 'Demo',
                                'purchased' => 'Purchased',
                            ])
                            ->disabled(),
                        Forms\Components\DateTimePicker::make('submitted_at')
                            ->disabled(),
                        Forms\Components\DateTimePicker::make('verified_at')
                            ->disabled(),
                        Forms\Components\DateTimePicker::make('rejected_at')
                            ->disabled(),
                    ])
                    ->columns(2),

                Forms\Components\Section::make('File Information')
                    ->schema([
                        Forms\Components\TextInput::make('erc_file_name')
                            ->label('File Name')
                            ->disabled(),
                        Forms\Components\TextInput::make('erc_file_size')
                            ->label('File Size (bytes)')
                            ->disabled(),
                        Forms\Components\TextInput::make('erc_file_hash')
                            ->label('File Hash')
                            ->disabled(),
                    ])
                    ->columns(3)
                    ->visible(fn ($record) => $record && $record->hasFile()),

                Forms\Components\Section::make('Review Information')
                    ->schema([
                        Forms\Components\Select::make('reviewed_by')
                            ->relationship('reviewer', 'name')
                            ->disabled(),
                        Forms\Components\Textarea::make('rejection_reason')
                            ->label('Rejection Reason')
                            ->visible(fn (Forms\Get $get) => $get('status') === BusinessVerificationStatusEnum::REJECTED->value)
                            ->required(fn (Forms\Get $get) => $get('status') === BusinessVerificationStatusEnum::REJECTED->value),
                        Forms\Components\Textarea::make('admin_notes')
                            ->label('Admin Notes')
                            ->columnSpanFull(),
                    ])
                    ->columns(2),
            ]);
    }

    /**
     * @throws \Exception
     */
    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->label('ID')
                    ->sortable(),
                Tables\Columns\TextColumn::make('customer.business_name')
                    ->label('Business Name')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('customer.tax_code')
                    ->label('Tax Code')
                    ->searchable(),
                Tables\Columns\BadgeColumn::make('status')
                    ->colors([
                        'secondary' => BusinessVerificationStatusEnum::UNSUBMITTED->value,
                        'warning' => BusinessVerificationStatusEnum::SUBMITTED->value,
                        'primary' => BusinessVerificationStatusEnum::IN_REVIEW->value,
                        'success' => BusinessVerificationStatusEnum::VERIFIED->value,
                        'danger' => BusinessVerificationStatusEnum::REJECTED->value,
                    ]),
                Tables\Columns\BadgeColumn::make('segment')
                    ->colors([
                        'secondary' => 'default',
                        'info' => 'demo',
                        'success' => 'purchased',
                    ]),
                Tables\Columns\IconColumn::make('has_file')
                    ->label('File')
                    ->boolean()
                    ->getStateUsing(fn ($record) => $record->hasFile()),
                Tables\Columns\TextColumn::make('submitted_at')
                    ->label('Submitted')
                    ->dateTime()
                    ->sortable(),
                Tables\Columns\TextColumn::make('verified_at')
                    ->label('Verified')
                    ->dateTime()
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->label('Created')
                    ->dateTime()
                    ->sortable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('status')
                    ->options(BusinessVerificationStatusEnum::class),
                Tables\Filters\SelectFilter::make('segment')
                    ->options([
                        'default' => 'Default',
                        'demo' => 'Demo',
                        'purchased' => 'Purchased',
                    ]),
                Tables\Filters\Filter::make('has_file')
                    ->query(fn (Builder $query): Builder => $query->whereNotNull('erc_file_path')),
                Tables\Filters\Filter::make('pending_review')
                    ->query(fn (Builder $query): Builder => $query->pendingReview()),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\Action::make('download_file')
                    ->label('Download')
                    ->icon('heroicon-o-arrow-down-tray')
                    ->visible(fn ($record) => $record->hasFile())
                    ->action(function ($record) {
                        return response()->download(
                            storage_path('app/private/' . $record->erc_file_path),
                            $record->erc_file_name
                        );
                    }),
                Tables\Actions\Action::make('approve')
                    ->label('Approve')
                    ->icon('heroicon-o-check')
                    ->color('success')
                    ->visible(fn ($record) => $record->status->canBeReviewed())
                    ->requiresConfirmation()
                    ->form([
                        Forms\Components\Textarea::make('admin_notes')
                            ->label('Admin Notes (Optional)'),
                    ])
                    ->action(function ($record, array $data) {
                        app(BusinessVerificationService::class)->markAsVerified(
                            $record,
                            auth()->id(),
                            $data['admin_notes'] ?? null
                        );
                    }),
                Tables\Actions\Action::make('reject')
                    ->label('Reject')
                    ->icon('heroicon-o-x-mark')
                    ->color('danger')
                    ->visible(fn ($record) => $record->status->canBeReviewed())
                    ->requiresConfirmation()
                    ->form([
                        Forms\Components\Textarea::make('rejection_reason')
                            ->label('Rejection Reason')
                            ->required(),
                        Forms\Components\Textarea::make('admin_notes')
                            ->label('Admin Notes (Optional)'),
                    ])
                    ->action(function ($record, array $data) {
                        app(BusinessVerificationService::class)->markAsRejected(
                            $record,
                            auth()->id(),
                            $data['rejection_reason'],
                            $data['admin_notes'] ?? null
                        );
                    }),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('created_at', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListBusinessVerifications::route('/'),
            'create' => Pages\CreateBusinessVerification::route('/create'),
            'view' => Pages\ViewBusinessVerification::route('/{record}'),
            'edit' => Pages\EditBusinessVerification::route('/{record}/edit'),
        ];
    }

    public static function getNavigationBadge(): ?string
    {
        return static::getModel()::pendingReview()->count();
    }
}
