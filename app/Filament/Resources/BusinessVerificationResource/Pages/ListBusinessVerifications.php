<?php

namespace App\Filament\Resources\BusinessVerificationResource\Pages;

use App\Filament\Resources\BusinessVerificationResource;
use App\Services\BusinessVerificationService;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Filament\Resources\Components\Tab;
use Illuminate\Database\Eloquent\Builder;

class ListBusinessVerifications extends ListRecords
{
    protected static string $resource = BusinessVerificationResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('statistics')
                ->label('Statistics')
                ->icon('heroicon-o-chart-bar')
                ->modalContent(function () {
                    $stats = app(BusinessVerificationService::class)->getStatistics();
                    return view('filament.business-verification.statistics', compact('stats'));
                }),
        ];
    }

    public function getTabs(): array
    {
        return [
            'all' => Tab::make('All'),
            'pending_review' => Tab::make('Pending Review')
                ->modifyQueryUsing(fn (Builder $query) => $query->pendingReview())
                ->badge(fn () => $this->getModel()::pendingReview()->count()),
            'unsubmitted' => Tab::make('Unsubmitted')
                ->modifyQueryUsing(fn (Builder $query) => $query->where('status', 'unsubmitted')),
            'verified' => Tab::make('Verified')
                ->modifyQueryUsing(fn (Builder $query) => $query->where('status', 'verified')),
            'rejected' => Tab::make('Rejected')
                ->modifyQueryUsing(fn (Builder $query) => $query->where('status', 'rejected')),
        ];
    }
}
