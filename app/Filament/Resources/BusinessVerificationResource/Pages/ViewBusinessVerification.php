<?php

namespace App\Filament\Resources\BusinessVerificationResource\Pages;

use App\Filament\Resources\BusinessVerificationResource;
use App\Services\BusinessVerificationService;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;
use Filament\Forms;

class ViewBusinessVerification extends ViewRecord
{
    protected static string $resource = BusinessVerificationResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make(),
            Actions\Action::make('download_file')
                ->label('Download File')
                ->icon('heroicon-o-arrow-down-tray')
                ->visible(fn () => $this->record->hasFile())
                ->action(function () {
                    return response()->download(
                        storage_path('app/private/' . $this->record->erc_file_path),
                        $this->record->erc_file_name
                    );
                }),
            Actions\Action::make('approve')
                ->label('Approve')
                ->icon('heroicon-o-check')
                ->color('success')
                ->visible(fn () => $this->record->status->canBeReviewed())
                ->requiresConfirmation()
                ->form([
                    Forms\Components\Textarea::make('admin_notes')
                        ->label('Admin Notes (Optional)'),
                ])
                ->action(function (array $data) {
                    app(BusinessVerificationService::class)->markAsVerified(
                        $this->record,
                        auth()->id(),
                        $data['admin_notes'] ?? null
                    );
                    $this->redirect($this->getResource()::getUrl('index'));
                }),
            Actions\Action::make('reject')
                ->label('Reject')
                ->icon('heroicon-o-x-mark')
                ->color('danger')
                ->visible(fn () => $this->record->status->canBeReviewed())
                ->requiresConfirmation()
                ->form([
                    Forms\Components\Textarea::make('rejection_reason')
                        ->label('Rejection Reason')
                        ->required(),
                    Forms\Components\Textarea::make('admin_notes')
                        ->label('Admin Notes (Optional)'),
                ])
                ->action(function (array $data) {
                    app(BusinessVerificationService::class)->markAsRejected(
                        $this->record,
                        auth()->id(),
                        $data['rejection_reason'],
                        $data['admin_notes'] ?? null
                    );
                    $this->redirect($this->getResource()::getUrl('index'));
                }),
        ];
    }
}
