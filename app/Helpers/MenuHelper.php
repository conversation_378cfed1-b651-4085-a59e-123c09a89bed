<?php

namespace App\Helpers;

use App\Models\Template;

class MenuHelper
{
    /**
     * Update templates in the menu structure.
     *
     * @return array<int, array<string, mixed>>
     */
    public static function updateTemplatesInMenu(): array
    {
        $menus = [
            [
                'title' => 'Thông tin',
                'route' => null,
                'url' => null,
                'type' => 'dropdown',
                'is_popular' => false,
                'icon' => 'info-circle',
                'svg' => '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>',
                'subtitle' => 'Thông tin chung',
                'style' => 'dropdown',
                'layout' => 'default',
                'items' => [
                    [
                        'title' => 'Về chúng tôi',
                        'route' => 'pages.show',
                        'url' => null,
                        'params' => ['slug' => 've-chung-toi'],
                        'subtitle' => 'Giới thiệu về CSlant',
                        'icon' => 'info-circle',
                        'svg' => '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>',
                        'is_popular' => false,
                        'type' => 'single',
                        'style' => 'item',
                        'layout' => null,
                    ],
                    [
                        'title' => 'Dự án đã thực hiện',
                        'route' => 'pages.show',
                        'url' => null,
                        'params' => ['slug' => 'du-an-da-thuc-hien'],
                        'subtitle' => 'Các dự án tiêu biểu',
                        'icon' => 'collection',
                        'svg' => '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>',
                        'is_popular' => false,
                        'type' => 'single',
                        'style' => 'item',
                        'layout' => null,
                    ],
                    [
                        'title' => 'Liên hệ',
                        'route' => 'pages.show',
                        'url' => null,
                        'params' => ['slug' => 'lien-he'],
                        'subtitle' => 'Thông tin liên hệ',
                        'icon' => 'mail',
                        'svg' => '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>',
                        'is_popular' => false,
                        'type' => 'single',
                        'style' => 'item',
                        'layout' => null,
                    ],
                    [
                        'title' => 'Quy trình làm việc',
                        'route' => 'pages.show',
                        'url' => null,
                        'params' => ['slug' => 'quy-trinh-lam-viec'],
                        'subtitle' => 'Các bước thực hiện dự án',
                        'icon' => 'document-text',
                        'svg' => '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>',
                        'is_popular' => false,
                        'type' => 'single',
                        'style' => 'item',
                        'layout' => null,
                    ],
                    [
                        'title' => 'Hỏi đáp (FAQ)',
                        'route' => 'pages.show',
                        'url' => null,
                        'params' => ['slug' => 'hoi-dap'],
                        'subtitle' => 'Câu hỏi thường gặp',
                        'icon' => 'question-mark-circle',
                        'svg' => '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>',
                        'is_popular' => false,
                        'type' => 'single',
                        'style' => 'item',
                        'layout' => null,
                    ],
                ],
            ],
            [
                'title' => 'Dịch vụ Thiết kế Website',
                'route' => null,
                'url' => null,
                'type' => 'dropdown',
                'is_popular' => false,
                'icon' => 'globe-alt',
                'svg' => '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9"></path>',
                'subtitle' => 'Các gói dịch vụ website',
                'style' => 'dropdown',
                'layout' => 'default',
                'items' => [
                    [
                        'title' => 'Gói Express Web',
                        'route' => 'pages.show',
                        'url' => null,
                        'params' => ['slug' => 'dich-vu/express-web'],
                        'subtitle' => 'Website nhanh, tiết kiệm',
                        'icon' => 'lightning-bolt',
                        'svg' => '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>',
                        'is_popular' => false,
                        'type' => 'single',
                        'style' => 'item',
                        'layout' => null,
                    ],
                    [
                        'title' => 'Gói Pro Web',
                        'route' => 'pages.show',
                        'url' => null,
                        'params' => ['slug' => 'dich-vu/pro-web'],
                        'subtitle' => 'Chuyên nghiệp, tối ưu',
                        'icon' => 'shield-check',
                        'svg' => '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>',
                        'is_popular' => true,
                        'type' => 'single',
                        'style' => 'item-popular',
                        'layout' => null,
                    ],
                    [
                        'title' => 'Gói E-Commerce',
                        'route' => 'pages.show',
                        'url' => null,
                        'params' => ['slug' => 'dich-vu/e-commerce'],
                        'subtitle' => 'Bán hàng đa kênh',
                        'icon' => 'shopping-bag',
                        'svg' => '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"></path>',
                        'is_popular' => false,
                        'type' => 'single',
                        'style' => 'item',
                        'layout' => null,
                    ],
                    [
                        'title' => 'Gói Website Care Plan',
                        'route' => 'pages.show',
                        'url' => null,
                        'params' => ['slug' => 'dich-vu/website-care-plan'],
                        'subtitle' => 'Bảo trì, nâng cấp',
                        'icon' => 'shield-check',
                        'svg' => '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>',
                        'is_popular' => false,
                        'type' => 'single',
                        'style' => 'item',
                        'layout' => null,
                    ],
                    [
                        'title' => 'Gói Growth SEO',
                        'route' => 'pages.show',
                        'url' => null,
                        'params' => ['slug' => 'dich-vu/growth-seo'],
                        'subtitle' => 'Tăng trưởng bền vững',
                        'icon' => 'trending-up',
                        'svg' => '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>',
                        'is_popular' => false,
                        'type' => 'single',
                        'style' => 'item',
                        'layout' => null,
                    ],
                ],
            ],
            [
                'title' => 'Mẫu Website',
                'route' => 'templates.index',
                'url' => null,
                'type' => 'mega_menu',
                'is_popular' => false,
                'icon' => 'template',
                'svg' => '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 5a1 1 0 011-1h14a1 1 0 011 1v2a1 1 0 01-1 1H5a1 1 0 01-1-1V5zM4 13a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H5a1 1 0 01-1-1v-6zM16 13a1 1 0 011-1h2a1 1 0 011 1v6a1 1 0 01-1 1h-2a1 1 0 01-1-1v-6z"></path>',
                'subtitle' => 'Thư viện mẫu website',
                'style' => 'mega',
                'layout' => '3-cols',
                'items' => [
                    [
                        'group' => 'Doanh nghiệp - Dịch vụ',
                        'style' => 'column',
                        'items' => [],
                    ],
                ],
            ],
            [
                'title' => 'Tư vấn',
                'route' => null,
                'url' => null,
                'type' => 'dropdown',
                'is_popular' => false,
                'icon' => 'chat-alt-2',
                'svg' => '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8h2a2 2 0 012 2v6a2 2 0 01-2 2h-2v4l-4-4H9a1.994 1.994 0 01-1.414-.586m0 0L11 14h4a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2v4l.586-.586z"></path>',
                'subtitle' => 'Tư vấn giải pháp',
                'style' => 'dropdown',
                'class' => 'w-84',
                'layout' => 'default',
                'items' => [
                    [
                        'title' => 'Tư vấn thiết kế website',
                        'route' => 'consultation.index',
                        'url' => null,
                        'params' => ['category_slug' => 'thiet-ke-website'],
                        'subtitle' => 'Giải pháp website chuyên nghiệp',
                        'icon' => 'desktop-computer',
                        'svg' => '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>',
                        'is_popular' => false,
                        'type' => 'single',
                        'style' => 'item',
                        'layout' => null,
                    ],
                    [
                        'title' => 'Tư vấn SEO & Marketing',
                        'route' => 'consultation.index',
                        'url' => null,
                        'params' => ['category_slug' => 'seo-marketing'],
                        'subtitle' => 'Tối ưu hóa và quảng bá trực tuyến',
                        //                    'icon' => 'speakerphone',
                        'icon' => 'search',
                        'svg' => '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5.882V19.24a1.76 1.76 0 01-3.417.592l-2.147-6.15M18 13a3 3 0 100-6M5.436 13.683A4.001 4.001 0 017 6h1.832c4.1 0 7.625-1.234 9.168-3v14c-1.543-1.766-5.067-3-9.168-3H7a3.988 3.988 0 01-1.564-.317z"></path>',
                        'is_popular' => false,
                        'type' => 'single',
                        'style' => 'item',
                        'layout' => null,
                    ],
                    [
                        'title' => 'Tư vấn Lập trình phần mềm',
                        'route' => 'consultation.index',
                        'url' => null,
                        'params' => ['category_slug' => 'lap-trinh-phan-mem'],
                        'subtitle' => 'Giải pháp phần mềm tùy chỉnh',
                        'icon' => 'shopping-cart',
                        'svg' => '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z"></path>',
                        'is_popular' => false,
                        'type' => 'single',
                        'style' => 'item',
                        'layout' => null,
                    ],
                ],
            ],
        ];

        foreach ($menus as &$menu) {
            if ($menu['route'] === 'templates.index') {
                $categories = [
                    [
                        'id' => 1,
                        'title' => 'Doanh nghiệp - Dịch vụ',
                        'icon' => 'building',
                        'svg' => '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4">',
                        'limit' => 7,
                        'column_id' => 1,
                    ],
                    [
                        'id' => 2,
                        'title' => 'Thương mại điện tử - Bán hàng',
                        'icon' => 'shopping-bag',
                        'svg' => '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z">',
                        'limit' => 3,
                        'column_id' => 2,
                    ],
                    [
                        'id' => 3,
                        'title' => 'Blog - Tạp chí - Tin tức',
                        'icon' => 'newspaper',
                        'svg' => '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z">',
                        'limit' => 3,
                        'column_id' => 2,
                    ],
                    [
                        'id' => 4,
                        'title' => 'Ngách kiếm tiền - Marketing',
                        'icon' => 'chart-bar',
                        'svg' => '<path d="M12 14l9-5-9-5-9 5 9 5z"></path><path d="M12 14l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z"></path><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 14l9-5-9-5-9 5 9 5zm0 0l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14zm-4 6v-7.5l4-2.222">',
                        'limit' => 2,
                        'column_id' => 3,
                    ],
                    [
                        'id' => 5,
                        'title' => 'Giải trí - Cộng đồng - Cá nhân',
                        'icon' => 'users',
                        'svg' => '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4">',
                        'limit' => 4,
                        'column_id' => 3,
                    ],
                ];

                $menu['type'] = 'mega_menu';
                $menu['items'] = [];

                foreach ($categories as $category) {
                    /** @var \Illuminate\Database\Eloquent\Collection<int, Template> $templates */
                    $templates = Template::query()
                        ->where('category_id', $category['id'])
                        ->limit($category['limit'])
                        ->get();

                    $templateItems = [];
                    /** @var Template $template */
                    foreach ($templates as $template) {
                        $templateItems[] = [
                            'title' => $template->name,
                            'route' => 'templates.show',
                            'params' => ['slug' => $template->slug],
                            'is_popular' => false,
                            'type' => 'single',
                            'style' => 'item',
                            'url' => null,
                        ];
                    }

                    $menu['items'][] = [
                        'group' => $category['title'],
                        'icon' => $category['icon'],
                        'svg' => $category['svg'],
                        'items' => $templateItems,
                        'column_id' => $category['column_id'],
                    ];
                }

                break;
            }
        }

        return $menus;
    }
}
