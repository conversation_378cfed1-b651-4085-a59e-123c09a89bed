<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\Mail\CheckEmailRequest;
use App\Http\Requests\Website\WebsiteCheckSubdomainRequest;
use App\Models\CustomerUser;
use App\Models\Website;
use Illuminate\Http\JsonResponse;

class TrialWebsiteApiController extends Controller
{
    /**
     * Check if email exists in the database.
     */
    public function checkEmail(CheckEmailRequest $request): JsonResponse
    {
        $exists = CustomerUser::where('email', $request->validated('email'))->exists();

        return $this->success([
            'exists' => $exists,
            'message' => $exists
                ? __('messages.api.email_exists')
                : __('messages.api.email_available'),
        ]);
    }

    /**
     * Check if subdomain exists in the database.
     */
    public function checkSubdomain(WebsiteCheckSubdomainRequest $request): JsonResponse
    {
        $subdomain = $request->validated('subdomain');
        $fullDomain = "{$subdomain}.identifynation.com";
        $exists = Website::where('domain', $fullDomain)->exists();

        return $this->success([
            'exists' => $exists,
            'message' => $exists
                ? __('messages.api.subdomain_exists')
                : __('messages.api.subdomain_available'),
            'full_domain' => $fullDomain,
            'available' => ! $exists,
        ]);
    }
}
