<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;

class EmailVerificationController extends Controller
{
    /**
     * Resend the email verification notification.
     */
    public function resend(Request $request): RedirectResponse
    {
        $request->user('customer_user')->sendEmailVerificationNotification();

        return back()->with('status', __('messages.email.verification_resent'));
    }
}
