<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\CustomerUser;
use App\Rules\EmailStrictRule;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Password;

class ForgotPasswordController extends Controller
{
    /**
     * Display the form to request a password reset link.
     */
    public function showLinkRequestForm(): \Illuminate\Contracts\View\View
    {
        return view('pages.auth.passwords.email');
    }

    /**
     * Send a password reset link to the given user.
     */
    public function sendResetLinkEmail(Request $request): \Illuminate\Http\RedirectResponse
    {
        $request->validate([
            'email' => ['required', new EmailStrictRule, 'exists:customer_users,email'],
        ]);

        // Check if user exists and is active
        /** @var null|CustomerUser $user */
        $user = CustomerUser::where('email', $request->input('email'))->first();

        if (! $user) {
            return back()->withErrors(['email' => __('messages.auth.account_not_found')]);
        }

        if ($user->status->value !== 'active') {
            return back()->withErrors(['email' => __('messages.auth.account_disabled')]);
        }

        $response = Password::broker('customer_users')->sendResetLink(
            $request->only('email')
        );

        return $response === Password::ResetLinkSent
            ? back()->with(['status' => __($response)])
            : back()->withErrors(['email' => __($response)]);
    }
}
