<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Validation\ValidationException;

class LoginController extends Controller
{
    /**
     * Show the login form
     */
    public function showLoginForm(): \Illuminate\Contracts\View\View
    {
        return view('pages.auth.login-form');
    }

    /**
     * Handle an authentication attempt.
     *
     * @return RedirectResponse
     *
     * @throws \Illuminate\Validation\ValidationException
     */
    /**
     * The number of minutes to throttle for.
     *
     * @var int
     */
    protected $decayMinutes;

    /**
     * The number of attempts allowed.
     *
     * @var int
     */
    protected $maxAttempts;

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->maxAttempts = (int) config('auth.throttling')['max_attempts'];
        $this->decayMinutes = (int) config('auth.throttling')['decay_minutes'];
    }

    /**
     * Handle an authentication attempt.
     */
    public function login(Request $request): RedirectResponse
    {
        $request->validate([
            'email' => 'required|email',
            'password' => 'required|string',
        ]);

        // Throttle login attempts
        $throttleKey = $this->throttleKey($request);
        $throttleConfig = config('auth.throttling');

        if ($throttleConfig['enabled'] && RateLimiter::tooManyAttempts($throttleKey, $this->maxAttempts)) {
            $seconds = RateLimiter::availableIn($throttleKey);
            $this->logThrottledAttempt($request, $throttleKey);

            throw ValidationException::withMessages([
                'email' => [
                    __('auth.throttle', [
                        'seconds' => $seconds,
                        'minutes' => ceil($seconds / 60),
                    ]),
                ],
            ])->status(429);
        }

        // Add small delay to prevent timing attacks
        if ($throttleConfig['enabled']) {
            usleep(100 * 1000); // 100 milliseconds
        }

        $credentials = $request->only('email', 'password');
        $remember = $request->filled('remember');

        if ($this->guard()->attempt($credentials, $remember)) {
            // Clear login attempts on successful login
            RateLimiter::clear($throttleKey);

            $request->session()->regenerate();

            return redirect()->intended(route('home'));
        }

        // Increment login attempts with exponential backoff if enabled
        if ($throttleConfig['enabled']) {
            $this->incrementLoginAttempts($request, $throttleKey, $throttleConfig);
        }

        throw ValidationException::withMessages([
            'email' => __('auth.failed'),
        ]);
    }

    /**
     * Get the throttle key for the given request.
     *
     * @return string
     */
    protected function throttleKey(Request $request)
    {
        // Normalize email using mb_strtolower with UTF-8 support
        $email = mb_strtolower($request->input('email'), 'UTF-8');

        // For international email addresses, we'll use the full Unicode email
        // This ensures we don't accidentally block legitimate international emails
        $email = trim($email);

        // For logging purposes, we'll also create a sanitized version
        $sanitizedEmail = filter_var($email, FILTER_SANITIZE_EMAIL);

        // Log the throttle key creation for debugging
        if ($email !== $sanitizedEmail) {
            Log::debug('Throttle key created with international email', [
                'original_email' => $email,
                'sanitized_email' => $sanitizedEmail,
                'ip' => $request->ip(),
            ]);
        }

        // Use the original email for the throttle key to ensure proper handling
        // of international characters
        return $email.'|'.$request->ip();
    }

    /**
     * Increment the login attempts for the user.
     */
    /**
     * @param  array{use_exponential_backoff: bool, backoff_multiplier: int, max_decay_minutes: int}  $config
     */
    protected function incrementLoginAttempts(Request $request, string $throttleKey, array $config): void
    {
        $attempts = RateLimiter::attempts($throttleKey) + 1;
        $backoffMinutes = $this->decayMinutes;

        if ($config['use_exponential_backoff'] && $attempts > 1) {
            // Calculate exponential backoff: decay_minutes * (multiplier ^ (attempts - 1))
            $backoffMinutes = min(
                $this->decayMinutes * pow($config['backoff_multiplier'], $attempts - 1),
                $config['max_decay_minutes']
            );

            RateLimiter::hit($throttleKey, $backoffMinutes * 60);
        } else {
            RateLimiter::hit($throttleKey, $this->decayMinutes * 60);
        }

        // Log the failed attempt with detailed information
        $logContext = [
            'email' => $request->input('email'),
            'ip' => $request->ip(),
            'attempts' => $attempts,
            'backoff_minutes' => $backoffMinutes,
            'throttle_key' => $throttleKey,
            'user_agent' => $request->userAgent(),
        ];

        if ($config['use_exponential_backoff'] && $attempts > 1) {
            $logContext['backoff_type'] = 'exponential';
            $logContext['backoff_multiplier'] = $config['backoff_multiplier'];
            $logContext['max_decay_minutes'] = $config['max_decay_minutes'];
            Log::warning('Failed login attempt with exponential backoff', $logContext);
        } else {
            $logContext['backoff_type'] = 'fixed';
            Log::warning('Failed login attempt', $logContext);
        }
    }

    /**
     * Log a throttled login attempt.
     *
     * @return void
     */
    protected function logThrottledAttempt(Request $request, string $throttleKey)
    {
        Log::warning('Login throttled', [
            'email' => $request->input('email'),
            'ip' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'throttle_key' => $throttleKey,
            'available_in' => RateLimiter::availableIn($throttleKey).' seconds',
        ]);
    }

    /**
     * Log the user out of the application.
     *
     * @return RedirectResponse
     */
    public function logout(Request $request)
    {
        $this->guard()->logout();

        $request->session()->invalidate();
        $request->session()->regenerateToken();

        return redirect('/');
    }
}
