<?php

namespace App\Http\Controllers\Auth;

use App\Enums\CustomerTypeEnum;
use App\Http\Controllers\Controller;
use App\Http\Requests\Auth\AuthRegisterRequest;
use App\Models\Customer;
use App\Models\CustomerUser;
use Illuminate\Auth\Events\Registered;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;

class RegisterController extends Controller
{
    /**
     * Display the registration view.
     */
    public function create(): \Illuminate\Contracts\View\View
    {
        return view('auth.register');
    }

    /**
     * @throws \Throwable
     */
    public function store(AuthRegisterRequest $request): RedirectResponse
    {
        $validated = $request->validated();

        // Use DB transaction to ensure atomicity
        $customerUser = DB::transaction(function () use ($validated) {
            /** @var Customer $customer */
            $customer = Customer::create([
                'account_type' => $validated['account_type'],
                'contact_person' => $validated['contact_person'],
                'business_name' => $validated['account_type'] === CustomerTypeEnum::BUSINESS->value
                    ? $validated['business_name']
                    : $validated['contact_person'],
                'email' => $validated['email'],
                'phone' => $validated['phone'],
            ]);

            // Create CustomerUser linked to Customer
            /** @var CustomerUser $customerUser */
            $customerUser = CustomerUser::create([
                'customer_id' => $customer->id,
                // Use contact_person as name for CustomerUser
                'name' => $validated['contact_person'],
                'email' => $validated['email'],
                'password' => Hash::make($validated['password']),
                'is_owner' => true, // First user is always owner
            ]);

            // Fire Registered event
            event(new Registered($customerUser));

            // Fire CustomerRegistered event for business verification
            event(new \App\Events\CustomerRegistered($customer));

            return $customerUser;
        });

        // Log the customer user in after registration
        $this->guard()->login($customerUser);

        return redirect()
            ->route('home')
            ->with('status', __('messages.auth.registration_successful'));
    }
}
