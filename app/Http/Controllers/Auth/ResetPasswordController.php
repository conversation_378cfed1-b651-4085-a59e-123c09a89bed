<?php

namespace App\Http\Controllers\Auth;

use App\Http\Requests\Auth\AuthResetPasswordRequest;
use Illuminate\Auth\Events\PasswordReset;
use Illuminate\Contracts\View\View;
use Illuminate\Http\Request;
use Illum<PERSON>\Routing\Controller as BaseController;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Password;
use Illuminate\Support\Str;

/**
 * Controller responsible for handling password reset requests.
 * Extends Laravel's BaseController.
 */
class ResetPasswordController extends BaseController
{
    /**
     * The path to redirect to after password reset.
     */
    protected string $redirectTo;

    /**
     * The broker to be used during password reset.
     */
    protected string $broker = 'customer_users';

    /**
     * Create a new controller instance.
     * Applies 'guest' middleware and sets the redirect path after reset.
     */
    public function __construct()
    {
        $this->middleware('guest:customer_user');
        $this->redirectTo = config('auth.passwords.customers.redirect', '/dang-nhap');
    }

    /**
     * Display the password reset form.
     */
    public function showResetForm(Request $request, ?string $token = null): View
    {
        return view('pages.auth.passwords.reset', [
            'token' => $token,
            'email' => $request->input('email'),
        ]);
    }

    /**
     * Handle a password reset request.
     * Validates input, attempts to reset the password, and redirects accordingly.
     *
     * @return \Illuminate\Http\RedirectResponse
     */
    public function reset(AuthResetPasswordRequest $request)
    {
        // Attempt to reset the user's password using the provided credentials and token
        $status = Password::broker($this->broker)->reset(
            $request->only('email', 'password', 'password_confirmation', 'token'),
            function ($customer) use ($request) {
                $customer->forceFill([
                    'password' => Hash::make($request->validated('password')),
                    'remember_token' => Str::random(60),
                ])->save();

                event(new PasswordReset($customer));
            }
        );

        // Redirect based on whether the password reset was successful
        return $status === Password::PASSWORD_RESET
            ? redirect($this->redirectTo)->with('status', __($status))
            : back()->withInput(['email' => $request->validated('email')])
                ->withErrors(['email' => __($status)]);
    }
}
