<?php

namespace App\Http\Controllers;

use App\Http\Requests\BusinessVerificationUploadRequest;
use App\Models\BusinessVerification;
use App\Models\CustomerUser;
use App\Services\BusinessVerificationService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\View\View;

class BusinessVerificationController extends Controller
{
    public function __construct(
        private BusinessVerificationService $verificationService
    ) {}

    /**
     * Show the business verification status page.
     */
    public function status(): View
    {
        /** @var CustomerUser $user */
        $user = Auth::guard('customer_user')->user();
        $customer = $user->customer;
        $verification = $customer->getOrCreateBusinessVerification();

        return view('business-verification.status', compact('verification', 'customer'));
    }

    /**
     * Show the ERC upload form.
     */
    public function upload(): View
    {
        /** @var CustomerUser $user */
        $user = Auth::guard('customer_user')->user();
        $customer = $user->customer;
        $verification = $customer->getOrCreateBusinessVerification();

        if (!$verification->status->canUploadFile()) {
            return redirect()->route('business-verification.status')
                ->with('error', __('messages.erc.cannot_upload_in_current_status'));
        }

        return view('business-verification.upload', compact('verification', 'customer'));
    }

    /**
     * Handle ERC file upload.
     *
     * @throws \Throwable
     */
    public function store(BusinessVerificationUploadRequest $request): JsonResponse
    {
        try {
            /** @var CustomerUser $user */
            $user = Auth::guard('customer_user')->user();
            $customer = $user->customer;

            /** @var BusinessVerification $verification */
            $verification = $customer->getOrCreateBusinessVerification();

            if (!$verification->status->canUploadFile()) {
                return response()->json([
                    'success' => false,
                    'message' => __('messages.erc.cannot_upload_in_current_status'),
                ], 400);
            }

            $file = $request->file('erc_file');
            
            // Validate file
            $validationErrors = $this->verificationService->validateErcFile($file);
            if (!empty($validationErrors)) {
                return response()->json([
                    'success' => false,
                    'message' => __('messages.erc.validation_failed'),
                    'errors' => $validationErrors,
                ], 422);
            }

            // Upload file and mark as submitted
            $this->verificationService->uploadErcFile($verification, $file);

            return response()->json([
                'success' => true,
                'message' => __('messages.erc.upload_success'),
                'verification_id' => $verification->id,
                'status' => $verification->status->value,
                'redirect_url' => route('business-verification.status'),
            ]);

        } catch (\Exception $e) {
            \Log::error('ERC upload failed', [
                'user_id' => Auth::id(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return response()->json([
                'success' => false,
                'message' => __('messages.erc.upload_failed'),
            ], 500);
        }
    }

    /**
     * Get verification status via API.
     */
    public function getStatus(): JsonResponse
    {
        /** @var CustomerUser $user */
        $user = Auth::guard('customer_user')->user();
        $customer = $user->customer;

        /** @var BusinessVerification $verification */
        $verification = $customer->getOrCreateBusinessVerification();

        return response()->json([
            'verification_id' => $verification->id,
            'status' => $verification->status->value,
            'status_label' => $verification->status->label(),
            'segment' => $verification->segment->value,
            'segment_label' => $verification->segment->label(),
            'can_upload' => $verification->status->canUploadFile(),
            'has_file' => $verification->hasFile(),
            'file_name' => $verification->erc_file_name,
            'submitted_at' => $verification->submitted_at?->toISOString(),
            'verified_at' => $verification->verified_at?->toISOString(),
            'rejected_at' => $verification->rejected_at?->toISOString(),
            'rejection_reason' => $verification->rejection_reason,
            'days_since_creation' => $verification->getDaysSinceCreation(),
        ]);
    }

    /**
     * Download ERC file (for customer to view their own file).
     */
    public function download(): \Symfony\Component\HttpFoundation\BinaryFileResponse
    {
        /** @var CustomerUser $user */
        $user = Auth::guard('customer_user')->user();
        $customer = $user->customer;

        /** @var BusinessVerification $verification */
        $verification = $customer->getOrCreateBusinessVerification();

        if (!$verification->hasFile()) {
            abort(404, __('messages.erc.file_not_found'));
        }

        return response()->download(
            storage_path('app/private/' . $verification->erc_file_path),
            $verification->erc_file_name
        );
    }

    /**
     * Delete uploaded ERC file (allow reupload).
     */
    public function delete(): JsonResponse
    {
        try {
            /** @var CustomerUser $user */
            $user = Auth::guard('customer_user')->user();
            $customer = $user->customer;

            /** @var BusinessVerification $verification */
            $verification = $customer->getOrCreateBusinessVerification();

            if (!$verification->status->canUploadFile()) {
                return response()->json([
                    'success' => false,
                    'message' => __('messages.erc.cannot_delete_in_current_status'),
                ], 400);
            }

            // Delete file from storage
            if ($verification->erc_file_path && \Storage::disk('private')->exists($verification->erc_file_path)) {
                \Storage::disk('private')->delete($verification->erc_file_path);
            }

            // Clear file information
            $verification->update([
                'erc_file_path' => null,
                'erc_file_name' => null,
                'erc_file_hash' => null,
                'erc_file_size' => null,
                'erc_file_mime_type' => null,
                'erc_info' => null,
            ]);

            return response()->json([
                'success' => true,
                'message' => __('messages.erc.file_deleted'),
            ]);

        } catch (\Exception $e) {
            \Log::error('ERC file deletion failed', [
                'user_id' => Auth::id(),
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'success' => false,
                'message' => __('messages.erc.delete_failed'),
            ], 500);
        }
    }
}
