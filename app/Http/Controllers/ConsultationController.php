<?php

namespace App\Http\Controllers;

use App\Models\Post;
use App\Models\PostCategory;
use Illuminate\Contracts\View\View;
use Symfony\Component\HttpFoundation\Response as ResponseAlias;

class ConsultationController extends Controller
{
    /**
     * Display a listing of the consultation posts for a specific category.
     *
     * @param  string  $slug  The slug of the post category
     */
    public function index(string $slug): View
    {
        /** @var PostCategory $category */
        $category = PostCategory::where('slug', $slug)->firstOrFail();
        $posts = $category->posts()->published()->orderBy('published_at', 'desc')->paginate(12);

        return view('pages.consultation.index', compact('category', 'posts'));
    }

    /**
     * Display the specified consultation post.
     *
     * @param  string  $category_slug  The slug of the post category
     * @param  string  $detail_slug  The slug of the post
     */
    public function detail(string $category_slug, string $detail_slug): View
    {
        /** @var Post $post */
        $post = Post::where('slug', $detail_slug)
            ->with('category')
            ->firstOrFail();

        // Ensure the post belongs to the specified category
        if ($post->category->slug !== $category_slug) {
            abort(ResponseAlias::HTTP_NOT_FOUND);
        }

        $relatedPosts = $post->category->posts()
            ->published()
            ->where('id', '!=', $post->id)
            ->orderBy('published_at', 'desc')
            ->limit(3)
            ->get();

        return view('pages.consultation.detail', compact('post', 'relatedPosts'));
    }
}
