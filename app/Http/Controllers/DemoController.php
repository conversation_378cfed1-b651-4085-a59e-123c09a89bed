<?php

namespace App\Http\Controllers;

use App\Models\Template;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\Request;

class DemoController extends Controller
{
    /**
     * Redirect to the demo page based on the demo URL.
     *
     * @throws ModelNotFoundException
     */
    public function index(Request $request): \Illuminate\Http\RedirectResponse
    {
        $request->validate([
            'demo_url' => 'required|string',
        ]);

        $demoUrl = $request->input('demo_url');
        $template = Template::query()->where('demo_url', $demoUrl)->firstOrFail();
        $slug = $template->getAttribute('slug');

        // Determine the correct route based on current route name
        $routeName = str_contains((string) $request->route()->getName(), '.vi.') ? 'demo.vi.show' : 'demo.show';

        return redirect()->route($routeName, $slug);
    }

    /**
     * Display the specified demo template.
     *
     * @param  string  $slug  The slug of the template
     *
     * @throws ModelNotFoundException
     */
    public function show(string $slug): \Illuminate\Contracts\View\View
    {
        /** @var Template $template */
        $template = Template::where('slug', $slug)->firstOrFail();

        return view('pages.demo.show', [
            'template' => $template,
        ]);
    }
}
