<?php

namespace App\Http\Controllers;

use App\Models\PostCategory;
use App\Models\Template;
use Illuminate\Contracts\View\View;
use Illuminate\Support\Collection;

class HomeController extends Controller
{
    /**
     * Display the home page.
     */
    public function index(): View
    {
        // Get featured templates with their categories
        $featuredTemplates = Template::with('category')
            ->where('is_featured', true)
            ->inRandomOrder()
            ->limit(8) // Limit to 8 featured templates
            ->get();

        // Get latest templates (most recent first)
        $newTemplates = Template::with('category')
            ->latest()
            ->limit(8) // Limit to 8 latest templates
            ->get();

        // Define main categories with their slugs and routes
        $categories = [
            [
                'title' => __('messages.services.web_design_consultation'),
                'slug' => 'thiet-ke-website',
                'view_more_url' => route('consultation.index', ['category_slug' => 'thiet-ke-website']),
            ],
            [
                'title' => __('messages.services.seo_marketing_consultation'),
                'slug' => 'seo-marketing',
                'view_more_url' => route('consultation.index', ['category_slug' => 'seo-marketing']),
            ],
            [
                'title' => __('messages.services.software_development_consultation'),
                'slug' => 'lap-trinh-phan-mem',
                'view_more_url' => route('consultation.index', ['category_slug' => 'lap-trinh-phan-mem']),
            ],
        ];

        /** @var Collection<int, array{title: string, slug: string, view_more_url: string, posts: mixed, category: mixed}> $blogColumns */
        $blogColumns = collect($categories)->map(function (array $cat) {
            /** @var null|PostCategory $category */
            $category = PostCategory::query()->where('slug', $cat['slug'])->active()->first();

            $posts = $category
                ? $category->posts()->published()->latest('published_at')->limit(5)->get()
                : collect();

            return [
                'title' => $cat['title'],
                'slug' => $cat['slug'],
                'view_more_url' => $cat['view_more_url'],
                'posts' => $posts,
                'category' => $category,
            ];
        });

        return view('pages.home', [
            'featuredTemplates' => $featuredTemplates,
            'newTemplates' => $newTemplates,
            // Filter out any null values from the collection
            'blogColumns' => $blogColumns->filter()->all(),
        ]);
    }
}
