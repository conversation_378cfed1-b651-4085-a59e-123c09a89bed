<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreLeadRequest;
use App\Models\Lead;
use Illuminate\Contracts\View\View;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class LeadController extends Controller
{
    /**
     * Display the lead registration form.
     */
    public function showRegisterPage(): View
    {
        return view('pages.lead.register');
    }

    /**
     * @throws \Throwable
     */
    public function store(StoreLeadRequest $request): JsonResponse
    {
        try {
            $lead = DB::transaction(function () use ($request) {
                return Lead::create($request->validated());
            });

            return $this->success(
                data: $lead,
                message: __('messages.api.lead_created'),
                statusCode: 201
            );
        } catch (\Exception $e) {
            Log::error('Failed to create lead: '.$e->getMessage(), [
                'exception' => $e,
            ]);

            return $this->error(
                message: __('messages.errors.failed_to_create_lead'),
                statusCode: 500,
                errorCode: 'lead_creation_failed'
            );
        }
    }
}
