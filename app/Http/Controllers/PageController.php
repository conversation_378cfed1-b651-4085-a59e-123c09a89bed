<?php

namespace App\Http\Controllers;

use Illuminate\Support\Facades\View;

class PageController extends Controller
{
    /**
     * Display the specified page.
     *
     * @param  string  $slug  The slug of the page to display
     *
     * @throws \Symfony\Component\HttpKernel\Exception\NotFoundHttpException
     */
    public function showPage(string $slug): \Illuminate\Contracts\View\View
    {
        $template = 'pages.landing.'.$slug;

        if (! View::exists($template)) {
            abort(404, __('messages.errors.page_not_found'));
        }

        return view($template);
    }
}
