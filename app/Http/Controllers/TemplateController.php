<?php

namespace App\Http\Controllers;

use App\Models\Template;
use App\Models\TemplateCategory;
use Illuminate\Contracts\View\View;
use Illuminate\Http\Request;
use Illuminate\Support\Collection;

class TemplateController extends Controller
{
    /**
     * Display a listing of the templates.
     */
    public function index(Request $request): View
    {
        $query = Template::with('category')
            ->when($request->filled('search'), function ($q) use ($request) {
                $search = '%'.$request->input('search').'%';

                return $q->where('name', 'like', $search)
                    ->orWhere('description', 'like', $search);
            })
            ->when($request->filled('category') || $request->route('category_slug'), function ($q) use ($request) {
                $categorySlug = $request->route('category_slug') ?? $request->input('category');

                return $q->whereHas('category', function ($query) use ($categorySlug) {
                    $query->where('slug', $categorySlug);
                });
            })
            ->when($request->filled('feature'), function ($q) use ($request) {
                return $q->whereJsonContains('features', $request->input('feature'));
            });

        // Handle sorting
        switch ($request->get('sort', 'newest')) {
            case 'popular':
                $query->orderBy('view_count', 'desc');

                break;
            case 'featured':
                $query->where('is_featured', true);
                // no break
            default:
                $query->latest();
        }

        $templates = $query->paginate(12);
        $categories = TemplateCategory::where('is_active', true)->withCount('templates')->get();

        // Get all unique features for filter
        /** @var Collection<int, string> $allFeatures */
        $allFeatures = Template::query()
            ->whereNotNull('features')
            ->pluck('features')
            ->filter()
            ->flatMap(fn ($features) => is_array($features) ? $features : [])
            ->filter()
            ->unique()
            ->values();

        return view('pages.templates.index', [
            'templates' => $templates,
            'categories' => $categories,
            'allFeatures' => $allFeatures,
            'currentFilters' => [
                'search' => $request->input('search'),
                'category' => $request->input('category'),
                'feature' => $request->input('feature'),
                'sort' => $request->input('sort', 'newest'),
            ],
        ]);
    }

    /**
     * Display the specified template.
     *
     * @throws \Illuminate\Database\Eloquent\ModelNotFoundException
     */
    public function show(string $slug): View
    {
        /** @var Template $template */
        $template = Template::query()->with('category')
            ->where('slug', $slug)
            ->firstOrFail();

        // Increment view count
        $template->incrementViewCount();

        // Get related templates from the same category
        $relatedTemplates = Template::where('category_id', $template->category_id)
            ->where('id', '!=', $template->id)
            ->inRandomOrder()
            ->limit(8)
            ->get();

        // Get random templates for "You may also like" section
        $randomTemplates = Template::where('id', '!=', $template->id)
            ->inRandomOrder()
            ->limit(8)
            ->get();

        return view('pages.templates.show', [
            'template' => $template,
            'relatedTemplates' => $relatedTemplates,
            'randomTemplates' => $randomTemplates,
        ]);
    }
}
