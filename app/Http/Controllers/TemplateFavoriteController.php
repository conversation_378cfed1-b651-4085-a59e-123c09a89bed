<?php

namespace App\Http\Controllers;

use App\Http\Traits\ApiResponse;
use App\Models\CustomerUser;
use App\Models\Template;
use App\Models\TemplateFavorite;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\View\View;

class TemplateFavoriteController extends Controller
{
    use ApiResponse;

    /**
     * @throws \Throwable
     */
    public function toggle(Template $template): JsonResponse
    {
        try {
            $user = $this->guard()->user();

            if (! $user) {
                return $this->unauthorized(__('messages.auth.login_required'));
            }

            DB::beginTransaction();

            // Check for existing favorite including soft-deleted ones
            $favorite = TemplateFavorite::withTrashed()
                ->where('customer_user_id', $user->id)
                ->where('template_id', $template->id)
                ->first();

            if (! $favorite instanceof TemplateFavorite) {
                // Create new favorite if none exists
                TemplateFavorite::create([
                    'customer_user_id' => $user->id,
                    'template_id' => $template->id,
                ]);
                $isFavorited = true;
                $message = __('messages.template.added_to_favorites');
            } elseif ($favorite->trashed()) {
                // Restore if soft-deleted
                $favorite->restore();
                $isFavorited = true;
                $message = __('messages.template.added_to_favorites');
            } else {
                // Soft delete if exists and not deleted
                $favorite->delete();
                $isFavorited = false;
                $message = __('messages.template.removed_from_favorites');
            }
            DB::commit();

            return $this->success(
                data: [
                    'is_favorited' => $isFavorited,
                    'favorite_count' => $template->favorite_count,
                ],
                message: $message
            );

        } catch (\Exception $e) {
            Log::error('Failed to toggle template favorite', [
                'user_id' => Auth::id(),
                'template_id' => $template->id,
                'error' => $e->getMessage(),
            ]);
            DB::rollBack();

            return $this->error(
                message: __('messages.template.favorite_failed'),
                statusCode: 500
            );
        }
    }

    /**
     * Show user's favorite templates page.
     */
    public function index(): View
    {
        /** @var CustomerUser $customerUser */
        $customerUser = $this->guard()->user();

        if (! $customerUser) {
            abort(401);
        }

        $favoriteTemplates = $customerUser->favoritedTemplates()
            ->with(['category', 'templateFavorites'])
            ->orderBy('template_favorites.created_at', 'desc')
            ->paginate(12);

        return view('pages.user-dashboard.favorites', compact('favoriteTemplates'));
    }

    /**
     * @throws \Throwable
     */
    public function destroy(Template $template): JsonResponse
    {
        try {
            /** @var CustomerUser $customerUser */
            $customerUser = $this->guard()->user();

            if (! $customerUser) {
                return response()->json([
                    'success' => false,
                    'message' => __('messages.auth.login_required'),
                ], 401);
            }

            DB::beginTransaction();
            $deleted = TemplateFavorite::where([
                'customer_user_id' => $customerUser->id,
                'template_id' => $template->id,
            ])->delete();
            DB::commit();

            if ($deleted) {
                return $this->success(
                    message: __('messages.template.removed_from_favorites')
                );
            }

            return $this->error(
                message: __('messages.template.not_in_favorites'),
                statusCode: 404
            );

        } catch (\Exception $e) {
            Log::error('Failed to remove template from favorites', [
                'user_id' => Auth::id(),
                'template_id' => $template->id,
                'error' => $e->getMessage(),
            ]);
            DB::rollBack();

            return $this->error(
                message: __('messages.template.favorite_failed'),
                statusCode: 500
            );
        }
    }
}
