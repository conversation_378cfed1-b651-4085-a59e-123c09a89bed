<?php

namespace App\Http\Controllers;

use App\Http\Requests\Website\WebsiteRegisterRequest;
use App\Jobs\DeployTrial14DaysWebsiteJob;
use App\Models\CustomerUser;
use App\Models\Role;
use App\Models\Website;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class WebsiteController extends Controller
{
    /**
     * Register a new website and user.
     *
     * @throws \Throwable
     */
    public function register(WebsiteRegisterRequest $request): JsonResponse
    {
        try {
            DB::beginTransaction();

            /** @var CustomerUser $user */
            $user = CustomerUser::create([
                'name' => $request->input('name'),
                'email' => $request->input('email'),
                'password' => bcrypt(Str::random(12)),
            ]);

            $user->assignRole(Role::CUSTOMER);

            /** @var Website $website */
            $website = Website::create([
                'customer_user_id' => $user->getKey(),
                'name' => $request->input('name'),
                'phone' => $request->input('phone'),
                'company' => $request->input('company'),
                'purpose' => $request->input('purpose'),
                'domain' => $request->input('domain'),
                'package' => $request->input('package'),
                'detail' => $request->input('details'),
                'status' => Website::STATUS_PENDING,
                'template_id' => $request->input('template_id'),
                'template_name' => $request->input('template_name'),
                'admin_username' => 'cslant',
                'admin_password' => Str::random(12),
                'customer_username' => $request->input('email'),
                'customer_password' => Str::random(12),
            ]);

            DeployTrial14DaysWebsiteJob::dispatch($website)->onQueue('deployments');

            DB::commit();

            $this->guard()->login($user);

            return $this->success(
                data: [
                    'user_id' => $user->getKey(),
                    'website_id' => $website->getKey(),
                ],
                message: __('messages.website.registration_successful'),
                statusCode: 201
            );
        } catch (\Exception $e) {
            DB::rollBack();

            Log::error('Website registration failed: '.$e->getMessage(), [
                'exception' => $e,
                'input' => $request->except(['_token', 'password']),
            ]);

            return $this->error(
                message: __('messages.errors.website_registration_failed'),
                statusCode: 500,
                errorCode: 'website_registration_failed',
                errors: config('app.debug') ? [
                    'exception' => $e->getMessage(),
                    'file' => $e->getFile().':'.$e->getLine(),
                ] : null
            );
        }
    }
}
