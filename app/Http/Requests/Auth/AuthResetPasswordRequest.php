<?php

namespace App\Http\Requests\Auth;

use App\Rules\EmailStrictRule;
use Illuminate\Foundation\Http\FormRequest;

class AuthResetPasswordRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, array<mixed>|\Illuminate\Contracts\Validation\ValidationRule|string>
     */
    public function rules(): array
    {
        return [
            'token' => 'required',
            'email' => ['required', new EmailStrictRule, 'exists:customer_users,email'],
            'password' => 'required|confirmed|min:8',
            'password_confirmation' => 'required',
        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'password.required' => __('messages.validation.password_required'),
            'password.confirmed' => __('messages.validation.password_confirmed'),
            'password.min' => __('messages.validation.password_min'),
            'password_confirmation.required' => __('messages.validation.password_confirmed'),
            'email.required' => __('messages.validation.email_required'),
            'email.*' => __('messages.validation.email_strict'),
            'email.exists' => __('messages.validation.email_exists'),
        ];
    }
}
