<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class BusinessVerificationUploadRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth('customer_user')->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'erc_file' => [
                'required',
                'file',
                'max:10240', // 10MB in KB
                'mimes:jpg,jpeg,png,pdf',
                'mimetypes:image/jpeg,image/png,application/pdf',
            ],
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'erc_file.required' => __('validation.erc.file_required'),
            'erc_file.file' => __('validation.erc.file_invalid'),
            'erc_file.max' => __('validation.erc.file_too_large'),
            'erc_file.mimes' => __('validation.erc.file_invalid_format'),
            'erc_file.mimetypes' => __('validation.erc.file_invalid_mime'),
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return [
            'erc_file' => __('validation.attributes.erc_file'),
        ];
    }
}
