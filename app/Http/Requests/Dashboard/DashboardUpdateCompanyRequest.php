<?php

namespace App\Http\Requests\Dashboard;

use App\Rules\EmailStrictRule;
use Illuminate\Foundation\Http\FormRequest;

class DashboardUpdateCompanyRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    /**
     * @return array<string, list<mixed>|string>
     */
    public function rules(): array
    {
        /** @var \App\Models\CustomerUser $customerUser */
        $customerUser = $this->user();

        return [
            'company_name' => 'required|string|max:255',
            'company_tax' => 'nullable|string|max:100',
            'company_contact' => 'nullable|string|max:255',
            'company_phone' => 'nullable|string|max:50',
            'company_email' => ['required', 'max:255', 'unique:customers,email,'.$customerUser->customer_id, new EmailStrictRule],
            'company_address' => 'nullable|string|max:255',
            'company_city' => 'nullable|string|max:100',
            'company_country' => 'nullable|string|max:100',
            'company_website' => 'nullable|string|max:255',
        ];
    }

    /**
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'company_name.required' => __('messages.validation.company_name_required'),
            'company_name.max' => __('messages.validation.company_name_max'),
            'company_tax.max' => __('messages.validation.company_tax_max'),
            'company_contact.max' => __('messages.validation.company_contact_max'),
            'company_phone.max' => __('messages.validation.company_phone_max'),
            'company_email.required' => __('messages.validation.company_email_required'),
            'company_email.max' => __('messages.validation.company_email_max'),
            'company_email.unique' => __('messages.validation.company_email_unique'),
            'company_email.*' => __('messages.validation.email_strict'),
            'company_address.max' => __('messages.validation.company_address_max'),
            'company_city.max' => __('messages.validation.company_city_max'),
            'company_country.max' => __('messages.validation.company_country_max'),
            'company_website.max' => __('messages.validation.company_website_max'),
        ];
    }
}
