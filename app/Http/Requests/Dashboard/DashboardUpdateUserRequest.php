<?php

namespace App\Http\Requests\Dashboard;

use App\Rules\EmailStrictRule;
use Illuminate\Foundation\Http\FormRequest;

class DashboardUpdateUserRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * @return array<string, list<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => 'required|string|max:255',
            'email' => ['required', 'max:255', new EmailStrictRule, 'unique:customer_users,email,'.$this->user()->id],
            'password' => 'nullable|string|min:6|confirmed',
        ];
    }

    /**
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'name.required' => __('messages.validation.name_required'),
            'email.required' => __('messages.validation.email_required'),
            'email.*' => __('messages.validation.email_strict'),
            'email.unique' => __('messages.validation.email_unique'),
            'password.required' => __('messages.validation.password_required'),
            'password.min' => __('messages.validation.password_min'),
            'password.confirmed' => __('messages.validation.password_confirmed'),
        ];
    }
}
