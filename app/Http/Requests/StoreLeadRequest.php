<?php

namespace App\Http\Requests;

use App\Rules\EmailStrictRule;
use Illuminate\Foundation\Http\FormRequest;

class StoreLeadRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, array<mixed>|\Illuminate\Contracts\Validation\ValidationRule|string>
     */
    public function rules(): array
    {
        return [
            'name' => 'required|string|max:255',
            'email' => ['required', 'max:255', new EmailStrictRule],
            'phone' => 'nullable|string|max:20',
            'company' => 'nullable|string|max:255',
            'message' => 'nullable|string|max:1000',
            'source' => 'nullable|string|max:100',
            'utm_source' => 'nullable|string|max:100',
            'utm_medium' => 'nullable|string|max:100',
            'utm_campaign' => 'nullable|string|max:100',
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'name.required' => __('messages.validation.name_required'),
            'name.max' => __('messages.validation.name_max'),
            'email.required' => __('messages.validation.email_required'),
            'email.max' => __('messages.validation.email_max'),
            'email.*' => __('messages.validation.email_strict'),
            'phone.max' => __('messages.validation.phone_max'),
            'company.max' => __('messages.validation.company_max'),
            'message.max' => __('messages.validation.message_max'),
        ];
    }
}
