<?php

namespace App\Http\Requests\Website;

use App\Rules\EmailStrictRule;
use App\Rules\PhoneNumberRule;
use Illuminate\Foundation\Http\FormRequest;

class WebsiteRegisterRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, array<mixed>|\Illuminate\Contracts\Validation\ValidationRule|string>
     */
    public function rules(): array
    {
        return [
            'name' => 'required|string|max:255',
            'email' => ['required', 'string', 'max:255', 'unique:customer_users,email', new EmailStrictRule],
            'phone' => ['required', 'string', 'max:20', new PhoneNumberRule],
            'company' => 'required|string|max:255',
            'domain' => [
                'required',
                'string',
                'max:63',
                'unique:websites,domain',
            ],
            'template_id' => 'nullable|exists:templates,id',
            'template_name' => 'nullable|string|max:255',
            'package' => 'required|string|max:100',
            'purpose' => 'nullable|string|max:500',
            'details' => 'nullable|string|max:1000',
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'phone.max' => __('messages.validation.phone_max'),
            'subdomain.regex' => __('messages.validation.subdomain_regex'),
            'subdomain.unique' => __('messages.validation.subdomain_unique'),
            'email.unique' => __('messages.validation.email_unique'),
            'email.*' => __('messages.validation.email_strict'),
        ];
    }

    protected function prepareForValidation(): void
    {
        $this->merge([
            'domain' => $this->input('subdomain').'.identifynation.com',
        ]);
    }
}
