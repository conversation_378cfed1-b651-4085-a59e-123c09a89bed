<?php

namespace App\Jobs;

use App\Mail\WebsiteCreatedMail;
use App\Models\Website;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Str;
use Spatie\Ssh\Ssh;

class DeployTrial14DaysWebsiteJob implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new job instance.
     */
    public function __construct(
        public Website $website,
    ) {}

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        Log::info(sprintf('Starting deployment for website: %s', $this->website->domain), [
            'website_id' => $this->website->id,
            'domain' => $this->website->domain,
        ]);

        $process = Ssh::create(config('services.csl-99.user'), config('services.csl-99.host'))
            ->usePort(config('services.csl-99.port'))
            ->usePrivateKey(config('services.csl-99.private_key'))
            ->setTimeout(20)
            ->execute([
                $this->getCommand(),
            ]);

        Log::info($process->getOutput());
        Log::info('Exit code: '.(string) $process->getExitCode());
        Log::info('Command: '.$this->getCommand());

        if ($process->isSuccessful()) {
            $this->website->update([
                'deploy_logs' => $process->getOutput(),
                'status' => Website::STATUS_ACTIVE,
            ]);

            Log::info(sprintf('Successfully deployed website: %s', $this->website->domain), [
                'website_id' => $this->website->id,
                'domain' => $this->website->domain,
            ]);

            Mail::to($this->website->customerUser)
                ->send(new WebsiteCreatedMail($this->website));
        }
    }

    /**
     * Get the deployment command string.
     */
    public function getCommand(): string
    {
        $command = 'cd /opt/pikachu && ./scripts/deploy-trial.sh ? ? ? ? ? ?';

        return Str::replaceArray(
            search: '?',
            replace: [
                $this->website->domain,
                $this->website->template->demo_slug,
                $this->website->admin_username,
                $this->website->admin_password,
                $this->website->customer_username,
                $this->website->customer_password,
            ],
            subject: $command
        );
    }
}
