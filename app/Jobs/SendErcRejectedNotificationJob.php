<?php

namespace App\Jobs;

use App\Mail\ErcRejectedMail;
use App\Models\BusinessVerification;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

class SendErcRejectedNotificationJob implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new job instance.
     */
    public function __construct(
        public BusinessVerification $businessVerification
    ) {}

    /**
     * Execute the job.
     *
     * @throws \Exception
     */
    public function handle(): void
    {
        try {
            $customer = $this->businessVerification->customer;
            
            if ($customer && $customer->email) {
                Mail::to($customer->email)
                    ->send(new ErcRejectedMail($this->businessVerification));

                Log::info('ERC rejected notification sent successfully', [
                    'verification_id' => $this->businessVerification->id,
                    'customer_id' => $customer->id,
                    'email' => $customer->email,
                ]);
            } else {
                Log::warning('Cannot send ERC rejected notification - no customer email', [
                    'verification_id' => $this->businessVerification->id,
                    'customer_id' => $customer?->id,
                ]);
            }
        } catch (\Exception $e) {
            Log::error('Failed to send ERC rejected notification', [
                'verification_id' => $this->businessVerification->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            
            throw $e;
        }
    }

    /**
     * Get the tags that should be assigned to the job.
     *
     * @return array<int, string>
     */
    public function tags(): array
    {
        return [
            'erc-notification',
            'erc-rejected',
            'customer:' . $this->businessVerification->customer_id,
        ];
    }
}
