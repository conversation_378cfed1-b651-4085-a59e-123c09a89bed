<?php

namespace App\Jobs;

use App\Mail\ErcReminderAfter30MinutesMail;
use App\Models\BusinessVerification;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

class SendErcReminderAfter30MinutesJob implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new job instance.
     */
    public function __construct(
        public BusinessVerification $businessVerification
    ) {}

    /**
     * Execute the job.
     *
     * @throws \Exception
     */
    public function handle(): void
    {
        // Double-check that the verification still needs reminder
        $this->businessVerification->refresh();
        
        if (!$this->businessVerification->status->needsReminder()) {
            Log::info('Skipping 30-minute ERC reminder - status changed', [
                'verification_id' => $this->businessVerification->id,
                'current_status' => $this->businessVerification->status->value,
            ]);
            return;
        }

        if ($this->businessVerification->reminder_30m_sent_at) {
            Log::info('Skipping 30-minute ERC reminder - already sent', [
                'verification_id' => $this->businessVerification->id,
            ]);
            return;
        }

        try {
            // Send email to customer
            $customer = $this->businessVerification->customer;
            
            if ($customer && $customer->email) {
                Mail::to($customer->email)
                    ->send(new ErcReminderAfter30MinutesMail($this->businessVerification));

                // Mark as sent
                $this->businessVerification->markThirtyMinuteReminderSent();

                Log::info('30-minute ERC reminder sent successfully', [
                    'verification_id' => $this->businessVerification->id,
                    'customer_id' => $customer->id,
                    'email' => $customer->email,
                ]);
            } else {
                Log::warning('Cannot send 30-minute ERC reminder - no customer email', [
                    'verification_id' => $this->businessVerification->id,
                    'customer_id' => $customer?->id,
                ]);
            }
        } catch (\Exception $e) {
            Log::error('Failed to send 30-minute ERC reminder', [
                'verification_id' => $this->businessVerification->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            
            throw $e;
        }
    }

    /**
     * Get the tags that should be assigned to the job.
     *
     * @return array<int, string>
     */
    public function tags(): array
    {
        return [
            'erc-reminder',
            'erc-reminder-30m',
            'customer:' . $this->businessVerification->customer_id,
        ];
    }
}
