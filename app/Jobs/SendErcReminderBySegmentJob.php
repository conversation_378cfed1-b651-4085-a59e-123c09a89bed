<?php

namespace App\Jobs;

use App\Mail\ErcReminderBySegmentMail;
use App\Models\BusinessVerification;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

class SendErcReminderBySegmentJob implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new job instance.
     */
    public function __construct(
        public BusinessVerification $businessVerification
    ) {}

    /**
     * Execute the job.
     *
     * @throws \Exception
     */
    public function handle(): void
    {
        // Double-check that the verification still needs reminder
        $this->businessVerification->refresh();
        
        if (!$this->businessVerification->status->needsReminder()) {
            Log::info('Skipping segment ERC reminder - status changed', [
                'verification_id' => $this->businessVerification->id,
                'current_status' => $this->businessVerification->status->value,
            ]);
            return;
        }

        if ($this->businessVerification->reminder_segment_sent_at) {
            Log::info('Skipping segment ERC reminder - already sent', [
                'verification_id' => $this->businessVerification->id,
            ]);
            return;
        }

        // Check if enough time has passed based on segment
        $daysSinceCreation = $this->businessVerification->getDaysSinceCreation();
        $requiredDays = $this->businessVerification->segment->getReminderDays();
        
        if ($daysSinceCreation < $requiredDays) {
            Log::info('Skipping segment ERC reminder - not enough time passed', [
                'verification_id' => $this->businessVerification->id,
                'days_since_creation' => $daysSinceCreation,
                'required_days' => $requiredDays,
                'segment' => $this->businessVerification->segment->value,
            ]);
            return;
        }

        try {
            // Send email to customer
            $customer = $this->businessVerification->customer;
            
            if ($customer && $customer->email) {
                Mail::to($customer->email)
                    ->send(new ErcReminderBySegmentMail($this->businessVerification));

                // Mark as sent
                $this->businessVerification->markSegmentReminderSent();

                Log::info('Segment ERC reminder sent successfully', [
                    'verification_id' => $this->businessVerification->id,
                    'customer_id' => $customer->id,
                    'email' => $customer->email,
                    'segment' => $this->businessVerification->segment->value,
                    'days_since_creation' => $daysSinceCreation,
                ]);
            } else {
                Log::warning('Cannot send segment ERC reminder - no customer email', [
                    'verification_id' => $this->businessVerification->id,
                    'customer_id' => $customer?->id,
                ]);
            }
        } catch (\Exception $e) {
            Log::error('Failed to send segment ERC reminder', [
                'verification_id' => $this->businessVerification->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            
            throw $e;
        }
    }

    /**
     * Get the tags that should be assigned to the job.
     *
     * @return array<int, string>
     */
    public function tags(): array
    {
        return [
            'erc-reminder',
            'erc-reminder-segment',
            'segment:' . $this->businessVerification->segment->value,
            'customer:' . $this->businessVerification->customer_id,
        ];
    }
}
