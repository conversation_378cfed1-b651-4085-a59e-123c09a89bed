<?php

namespace App\Jobs;

use App\Mail\ErcSubmittedMail;
use App\Models\BusinessVerification;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

class SendErcSubmittedNotificationJob implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new job instance.
     */
    public function __construct(
        public BusinessVerification $businessVerification
    ) {}

    /**
     * Execute the job.
     *
     * @throws \Exception
     */
    public function handle(): void
    {
        try {
            $customer = $this->businessVerification->customer;
            
            if ($customer && $customer->email) {
                Mail::to($customer->email)
                    ->send(new ErcSubmittedMail($this->businessVerification));

                Log::info('ERC submitted notification sent successfully', [
                    'verification_id' => $this->businessVerification->id,
                    'customer_id' => $customer->id,
                    'email' => $customer->email,
                ]);
            } else {
                Log::warning('Cannot send ERC submitted notification - no customer email', [
                    'verification_id' => $this->businessVerification->id,
                    'customer_id' => $customer?->id,
                ]);
            }
        } catch (\Exception $e) {
            Log::error('Failed to send ERC submitted notification', [
                'verification_id' => $this->businessVerification->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            
            throw $e;
        }
    }

    /**
     * Get the tags that should be assigned to the job.
     *
     * @return array<int, string>
     */
    public function tags(): array
    {
        return [
            'erc-notification',
            'erc-submitted',
            'customer:' . $this->businessVerification->customer_id,
        ];
    }
}
