<?php

namespace App\Listeners;

use App\Enums\CustomerTypeEnum;
use App\Events\CustomerRegistered;
use App\Services\BusinessVerificationService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

class InitializeBusinessVerification implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * Create the event listener.
     */
    public function __construct(
        private readonly BusinessVerificationService $verificationService
    ) {}

    /**
     * Handle the event.
     *
     * @throws \Exception|\Throwable
     */
    public function handle(CustomerRegistered $event): void
    {
        try {
            // Only initialize for business accounts
            if ($event->customer->account_type->value !== CustomerTypeEnum::BUSINESS->value) {
                Log::info('Skipping business verification initialization for non-business account', [
                    'customer_id' => $event->customer->id,
                    'account_type' => $event->customer->account_type->value,
                ]);
                return;
            }

            $this->verificationService->initializeForCustomer($event->customer);

            Log::info('Business verification initialized successfully', [
                'customer_id' => $event->customer->id,
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to initialize business verification', [
                'customer_id' => $event->customer->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            // Re-throw to trigger job retry
            throw $e;
        }
    }
}
