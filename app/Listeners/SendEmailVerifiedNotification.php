<?php

namespace App\Listeners;

use App\Models\CustomerUser;
use App\Models\User;
use App\Notifications\Auth\AuthEmailVerifiedNotification;
use Illuminate\Auth\Events\Verified;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Notifiable;
use Illuminate\Queue\InteractsWithQueue;

/**
 * Send email verified notification to the user.
 * Override the default event listener for email verification.
 */
class SendEmailVerifiedNotification implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(Verified $event): void
    {
        $user = $event->user;

        // Check if the user implements Notifiable trait
        if ($user instanceof User || $user instanceof CustomerUser) {
            /** @var Notifiable $user */
            $user->notify(new AuthEmailVerifiedNotification($user));
        }
    }
}
