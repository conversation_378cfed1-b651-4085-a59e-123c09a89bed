<?php

namespace App\Mail;

use App\Models\BusinessVerification;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class ErcRejectedMail extends Mailable implements ShouldQueue
{
    use Queueable, SerializesModels;

    /**
     * Create a new message instance.
     */
    public function __construct(
        public BusinessVerification $businessVerification
    ) {}

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        return new Envelope(
            subject: __('messages.erc.rejected_subject'),
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            markdown: 'emails.erc.rejected',
            with: [
                'businessVerification' => $this->businessVerification,
                'customer' => $this->businessVerification->customer,
                'rejectionReason' => $this->businessVerification->rejection_reason,
                'rejectedAt' => $this->businessVerification->rejected_at,
                'resubmitUrl' => route('business-verification.upload'),
                'supportUrl' => route('user-dashboard'),
                'checklist' => [
                    __('messages.erc.checklist_item_1'),
                    __('messages.erc.checklist_item_2'),
                    __('messages.erc.checklist_item_3'),
                    __('messages.erc.checklist_item_4'),
                ],
            ],
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
}
