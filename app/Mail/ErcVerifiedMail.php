<?php

namespace App\Mail;

use App\Models\BusinessVerification;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class ErcVerifiedMail extends Mailable implements ShouldQueue
{
    use Queueable, SerializesModels;

    /**
     * Create a new message instance.
     */
    public function __construct(
        public BusinessVerification $businessVerification
    ) {}

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        return new Envelope(
            subject: __('messages.erc.verified_subject'),
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            markdown: 'emails.erc.verified',
            with: [
                'businessVerification' => $this->businessVerification,
                'customer' => $this->businessVerification->customer,
                'verifiedAt' => $this->businessVerification->verified_at,
                'dashboardUrl' => route('user-dashboard'),
                'unlockedFeatures' => [
                    __('messages.erc.feature_upload_limit'),
                    __('messages.erc.feature_priority_support'),
                    __('messages.erc.feature_business_features'),
                    __('messages.erc.feature_detailed_reports'),
                ],
            ],
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
}
