<?php

namespace App\Models;

use App\Enums\BusinessVerificationSegmentEnum;
use App\Enums\BusinessVerificationStatusEnum;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Storage;

/**
 * @property int $id
 * @property int $customer_id
 * @property BusinessVerificationStatusEnum $status
 * @property BusinessVerificationSegmentEnum $segment
 * @property null|string $erc_file_path
 * @property null|string $erc_file_name
 * @property null|string $erc_file_hash
 * @property null|int $erc_file_size
 * @property null|string $erc_file_mime_type
 * @property null|array $erc_info
 * @property null|Carbon $submitted_at
 * @property null|Carbon $reviewed_at
 * @property null|Carbon $verified_at
 * @property null|Carbon $rejected_at
 * @property null|int $reviewed_by
 * @property null|string $rejection_reason
 * @property null|string $admin_notes
 * @property null|Carbon $reminder_30m_sent_at
 * @property null|Carbon $reminder_segment_sent_at
 * @property int $reminder_count
 * @property Carbon $created_at
 * @property Carbon $updated_at
 * @property-read Customer $customer
 * @property-read null|User $reviewer
 * @property-read string $erc_file_url
 * @property-read string $status_label
 * @property-read string $status_badge_class
 * @property-read string $segment_label
 *
 * @method static Builder<static>|<BusinessVerification> pendingReview()
 * @method static Builder<static>|<BusinessVerification> needsThirtyMinuteReminder()
 * @method static Builder<static>|<BusinessVerification> needsSegmentReminder()
 */
class BusinessVerification extends Model
{
    use HasFactory;

    /** @var array<int, string> */
    protected $fillable = [
        'customer_id',
        'status',
        'segment',
        'erc_file_path',
        'erc_file_name',
        'erc_file_hash',
        'erc_file_size',
        'erc_file_mime_type',
        'erc_info',
        'submitted_at',
        'reviewed_at',
        'verified_at',
        'rejected_at',
        'reviewed_by',
        'rejection_reason',
        'admin_notes',
        'reminder_30m_sent_at',
        'reminder_segment_sent_at',
        'reminder_count',
    ];

    /** @var array<string, string> */
    protected $casts = [
        'status' => BusinessVerificationStatusEnum::class,
        'segment' => BusinessVerificationSegmentEnum::class,
        'erc_info' => 'array',
        'erc_file_size' => 'integer',
        'submitted_at' => 'datetime',
        'reviewed_at' => 'datetime',
        'verified_at' => 'datetime',
        'rejected_at' => 'datetime',
        'reminder_30m_sent_at' => 'datetime',
        'reminder_segment_sent_at' => 'datetime',
        'reminder_count' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Get the customer that owns this verification.
     *
     * @return BelongsTo<Customer, $this>
     */
    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    /**
     * Get the user who reviewed this verification.
     *
     * @return BelongsTo<User, $this>
     */
    public function reviewer(): BelongsTo
    {
        return $this->belongsTo(User::class, 'reviewed_by');
    }

    /**
     * Scope to get verifications that need 30-minute reminder.
     * (->needsThirtyMinuteReminder())
     */
    public function scopeNeedsThirtyMinuteReminder(Builder $query): Builder
    {
        return $query->where('status', BusinessVerificationStatusEnum::UNSUBMITTED)
            ->whereNull('reminder_30m_sent_at')
            ->where('created_at', '<=', now()->subMinutes(30));
    }

    /**
     * Scope to get verifications that need segment-based reminder.
     * (->needsSegmentReminder())
     */
    public function scopeNeedsSegmentReminder(Builder $query): Builder
    {
        return $query->where('status', BusinessVerificationStatusEnum::UNSUBMITTED)
            ->whereNull('reminder_segment_sent_at')
            ->where(function ($q) {
                $q->where('segment', BusinessVerificationSegmentEnum::PURCHASED)
                    ->where('created_at', '<=', now()->subDays(1))
                    ->orWhere(function ($q2) {
                        $q2->where('segment', BusinessVerificationSegmentEnum::DEMO)
                            ->where('created_at', '<=', now()->subDays(2));
                    })
                    ->orWhere(function ($q3) {
                        $q3->where('segment', BusinessVerificationSegmentEnum::DEFAULT)
                            ->where('created_at', '<=', now()->subDays(3));
                    });
            });
    }

    /**
     * Scope to get verifications pending review.
     * (->pendingReview())
     */
    public function scopePendingReview(Builder $query): Builder
    {
        return $query->whereIn('status', [
            BusinessVerificationStatusEnum::SUBMITTED,
            BusinessVerificationStatusEnum::IN_REVIEW
        ]);
    }

    /**
     * Accessor for ERC file URL. (->erc_file_url)
     * Get the ERC file URL.
     */
    public function getErcFileUrlAttribute(): ?string
    {
        if (!$this->erc_file_path) {
            return null;
        }

        return Storage::disk('private')->url($this->erc_file_path);
    }

    /**
     * Check if the verification has an uploaded file.
     */
    public function hasFile(): bool
    {
        return !empty($this->erc_file_path) && Storage::disk('private')->exists($this->erc_file_path);
    }

    /**
     * Mark as submitted.
     */
    public function markAsSubmitted(): bool
    {
        if (!$this->status->canTransitionTo(BusinessVerificationStatusEnum::SUBMITTED)) {
            return false;
        }

        return $this->update([
            'status' => BusinessVerificationStatusEnum::SUBMITTED,
            'submitted_at' => now(),
        ]);
    }

    /**
     * Mark as verified.
     */
    public function markAsVerified(int $reviewedBy, ?string $adminNotes = null): bool
    {
        if (!$this->status->canTransitionTo(BusinessVerificationStatusEnum::VERIFIED)) {
            return false;
        }

        return $this->update([
            'status' => BusinessVerificationStatusEnum::VERIFIED,
            'verified_at' => now(),
            'reviewed_at' => now(),
            'reviewed_by' => $reviewedBy,
            'admin_notes' => $adminNotes,
        ]);
    }

    /**
     * Mark as rejected.
     */
    public function markAsRejected(int $reviewedBy, string $rejectionReason, ?string $adminNotes = null): bool
    {
        if (!$this->status->canTransitionTo(BusinessVerificationStatusEnum::REJECTED)) {
            return false;
        }

        return $this->update([
            'status' => BusinessVerificationStatusEnum::REJECTED,
            'rejected_at' => now(),
            'reviewed_at' => now(),
            'reviewed_by' => $reviewedBy,
            'rejection_reason' => $rejectionReason,
            'admin_notes' => $adminNotes,
        ]);
    }

    /**
     * Update segment based on customer data.
     */
    public function updateSegment(): bool
    {
        $newSegment = BusinessVerificationSegmentEnum::determineSegment($this->customer_id);
        
        if ($newSegment !== $this->segment) {
            return $this->update(['segment' => $newSegment]);
        }
        
        return true;
    }

    /**
     * Mark 30-minute reminder as sent.
     */
    public function markThirtyMinuteReminderSent(): bool
    {
        return $this->update([
            'reminder_30m_sent_at' => now(),
            'reminder_count' => $this->reminder_count + 1,
        ]);
    }

    /**
     * Mark segment reminder as sent.
     */
    public function markSegmentReminderSent(): bool
    {
        return $this->update([
            'reminder_segment_sent_at' => now(),
            'reminder_count' => $this->reminder_count + 1,
        ]);
    }

    /**
     * Get days since creation.
     */
    public function getDaysSinceCreation(): int
    {
        return $this->created_at->diffInDays(now());
    }

    /**
     * Accessor for status label. (->status_label)
     * Get status label.
     */
    public function getStatusLabelAttribute(): string
    {
        return $this->status->label();
    }

    /**
     * Accessor for status badge class. (->status_badge_class)
     * Get status badge class.
     */
    public function getStatusBadgeClassAttribute(): string
    {
        return $this->status->badgeClass();
    }

    /**
     * Accessor for segment label. (->segment_label)
     * Get segment label.
     */
    public function getSegmentLabelAttribute(): string
    {
        return $this->segment->label();
    }
}
