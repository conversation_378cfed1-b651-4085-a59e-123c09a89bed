<?php

namespace App\Models;

use App\Enums\ContractStatusEnum;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Carbon;

/**
 * @property int $id
 * @property string $contract_number
 * @property string $title
 * @property null|string $description
 * @property numeric $amount
 * @property Carbon $start_date
 * @property Carbon $end_date
 * @property string $status
 * @property int $customer_id
 * @property null|int $opportunity_id
 * @property null|int $quote_id
 * @property int $created_by
 * @property null|Carbon $signed_at
 * @property null|string $payment_terms
 * @property null|string $terms_and_conditions
 * @property null|Carbon $deleted_at
 * @property null|Carbon $created_at
 * @property null|Carbon $updated_at
 * @property-read User $createdBy
 * @property-read Customer $customer
 * @property-read null|Opportunity $opportunity
 * @property-read null|Quote $quote
 *
 * @method static Builder<static>|Contract newModelQuery()
 * @method static Builder<static>|Contract newQuery()
 * @method static Builder<static>|Contract onlyTrashed()
 * @method static Builder<static>|Contract query()
 * @method static Builder<static>|Contract whereAmount($value)
 * @method static Builder<static>|Contract whereContractNumber($value)
 * @method static Builder<static>|Contract whereCreatedAt($value)
 * @method static Builder<static>|Contract whereCreatedBy($value)
 * @method static Builder<static>|Contract whereCustomerId($value)
 * @method static Builder<static>|Contract whereDeletedAt($value)
 * @method static Builder<static>|Contract whereDescription($value)
 * @method static Builder<static>|Contract whereEndDate($value)
 * @method static Builder<static>|Contract whereId($value)
 * @method static Builder<static>|Contract whereOpportunityId($value)
 * @method static Builder<static>|Contract wherePaymentTerms($value)
 * @method static Builder<static>|Contract whereQuoteId($value)
 * @method static Builder<static>|Contract whereSignedAt($value)
 * @method static Builder<static>|Contract whereStartDate($value)
 * @method static Builder<static>|Contract whereStatus($value)
 * @method static Builder<static>|Contract whereTermsAndConditions($value)
 * @method static Builder<static>|Contract whereTitle($value)
 * @method static Builder<static>|Contract whereUpdatedAt($value)
 * @method static Builder<static>|Contract withTrashed()
 * @method static Builder<static>|Contract withoutTrashed()
 *
 * @mixin \Eloquent
 */
class Contract extends Model
{
    use SoftDeletes;

    /** @var array<int, string> */
    protected $fillable = [
        'contract_number',
        'title',
        'description',
        'amount',
        'start_date',
        'end_date',
        'status',
        'customer_id',
        'opportunity_id',
        'quote_id',
        'created_by',
        'signed_at',
        'payment_terms',
        'terms_and_conditions',
    ];

    /** @var array<string, string> */
    protected $casts = [
        'amount' => 'decimal:2',
        'start_date' => 'date',
        'end_date' => 'date',
        'signed_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    /**
     * Get the customer that owns the contract.
     *
     * @return BelongsTo<Customer, $this>
     */
    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }
    
    /**
     * Scope a query to only include active contracts.
     */
    public function scopeActive(Builder $query): Builder
    {
        return $query->where('status', ContractStatusEnum::ACTIVE->value);
    }
    
    /**
     * Scope a query to only include expired contracts.
     */
    public function scopeExpired(Builder $query): Builder
    {
        return $query->where('status', ContractStatusEnum::EXPIRED->value)
            ->orWhere(function ($query) {
                $query->where('status', ContractStatusEnum::ACTIVE->value)
                    ->where('end_date', '<', now());
            });
    }
    
    /**
     * Scope a query to only include contracts expiring soon.
     */
    public function scopeExpiringSoon(Builder $query, int $days = 30): Builder
    {
        return $query->where('status', ContractStatusEnum::ACTIVE->value)
            ->whereBetween('end_date', [now(), now()->addDays($days)]);
    }
    
    /**
     * Check if the contract is active.
     */
    public function isActive(): bool
    {
        return ContractStatusEnum::from($this->status)->isActive() && 
               (!$this->end_date || $this->end_date->isFuture());
    }
    
    /**
     * Check if the contract is expired.
     */
    public function isExpired(): bool
    {
        return ContractStatusEnum::from($this->status)->isExpired() || 
               (ContractStatusEnum::from($this->status)->isActive() && $this->end_date->isPast());
    }
    
    /**
     * Check if the contract is pending.
     */
    public function isPending(): bool
    {
        return ContractStatusEnum::from($this->status)->isPending();
    }
    
    /**
     * Check if the contract can be edited.
     */
    public function isEditable(): bool
    {
        return ContractStatusEnum::from($this->status)->isEditable();
    }
    
    /**
     * Mark the contract as signed.
     */
    public function markAsSigned(): bool
    {
        if (!ContractStatusEnum::from($this->status)->canBeSigned()) {
            return false;
        }
        
        return $this->update([
            'status' => ContractStatusEnum::ACTIVE->value,
            'signed_at' => now(),
        ]);
    }
    
    /**
     * Renew the contract.
     */
    public function renew(Carbon $newEndDate, string $newContractNumber = null): bool
    {
        if (!ContractStatusEnum::from($this->status)->canBeRenewed()) {
            return false;
        }
        
        return $this->update([
            'status' => ContractStatusEnum::RENEWED->value,
            'end_date' => $newEndDate,
            'contract_number' => $newContractNumber ?? $this->contract_number . '-RENEWED',
        ]);
    }
    
    /**
     * Terminate the contract.
     */
    public function terminate(): bool
    {
        if (!ContractStatusEnum::from($this->status)->canBeTerminated()) {
            return false;
        }
        
        return $this->update([
            'status' => ContractStatusEnum::TERMINATED->value,
        ]);
    }
    
    /**
     * Cancel the contract.
     */
    public function cancel(): bool
    {
        if (!ContractStatusEnum::from($this->status)->canBeCancelled()) {
            return false;
        }
        
        return $this->update([
            'status' => ContractStatusEnum::CANCELLED->value,
        ]);
    }
    
    /**
     * Get the contract status label.
     */
    public function getStatusLabelAttribute(): string
    {
        return ContractStatusEnum::from($this->status)->label();
    }
    
    /**
     * Get the contract status badge class.
     */
    public function getStatusBadgeClassAttribute(): string
    {
        return ContractStatusEnum::from($this->status)->badgeClass();
    }
    
    /**
     * Get the next possible statuses for this contract.
     * 
     * @return array<ContractStatusEnum>
     */
    public function getNextPossibleStatuses(): array
    {
        return ContractStatusEnum::from($this->status)->getNextPossibleStatuses();
    }
    
    /**
     * Check if the contract can transition to the target status.
     */
    public function canTransitionTo(string $targetStatus): bool
    {
        return ContractStatusEnum::from($this->status)
            ->canTransitionTo(ContractStatusEnum::from($targetStatus));
    }

    /**
     * Get the opportunity associated with the contract.
     *
     * @return BelongsTo<Opportunity, $this>
     */
    public function opportunity(): BelongsTo
    {
        return $this->belongsTo(Opportunity::class);
    }

    /**
     * Get the quote associated with the contract.
     *
     * @return BelongsTo<Quote, $this>
     */
    public function quote(): BelongsTo
    {
        return $this->belongsTo(Quote::class);
    }

    /**
     * Get the user who created the contract.
     *
     * @return BelongsTo<User, $this>
     */
    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }
}
