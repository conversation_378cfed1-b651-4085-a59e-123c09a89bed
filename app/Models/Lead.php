<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Carbon;

/**
 * @property int $id
 * @property null|string $name
 * @property null|string $email
 * @property null|string $phone
 * @property null|string $company
 * @property null|array<array-key, mixed> $services
 * @property null|string $budget
 * @property null|string $details
 * @property null|string $purpose
 * @property null|string $website_ref
 * @property null|string $package
 * @property null|Carbon $created_at
 * @property null|Carbon $updated_at
 * @property null|string $deleted_at
 *
 * @method static Builder<static>|Lead newModelQuery()
 * @method static Builder<static>|Lead newQuery()
 * @method static Builder<static>|Lead query()
 * @method static Builder<static>|Lead where($column, $operator = null, $value = null, $boolean = 'and')
 * @method static Builder<static>|Lead create(array<string, mixed> $attributes = [])
 * @method static Builder<static>|Lead whereBudget($value)
 * @method static Builder<static>|Lead whereCompany($value)
 * @method static Builder<static>|Lead whereCreatedAt($value)
 * @method static Builder<static>|Lead whereDeletedAt($value)
 * @method static Builder<static>|Lead whereDetails($value)
 * @method static Builder<static>|Lead whereEmail($value)
 * @method static Builder<static>|Lead whereId($value)
 * @method static Builder<static>|Lead whereName($value)
 * @method static Builder<static>|Lead wherePackage($value)
 * @method static Builder<static>|Lead wherePhone($value)
 * @method static Builder<static>|Lead wherePurpose($value)
 * @method static Builder<static>|Lead whereServices($value)
 * @method static Builder<static>|Lead whereUpdatedAt($value)
 * @method static Builder<static>|Lead whereWebsiteRef($value)
 *
 * @mixin \Eloquent
 */
class Lead extends Model
{
    /** @var array<int, string> */
    protected $fillable = [
        'name',
        'email',
        'phone',
        'company',
        'services',
        'budget',
        'details',
        'purpose',
        'website_ref',
        'package',
    ];

    /** @var array<string, string> */
    protected $casts = [
        'services' => 'array',
    ];
}
