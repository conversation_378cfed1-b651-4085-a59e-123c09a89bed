<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * @property int $id
 * @property string $title
 * @property null|string $description
 * @property null|string $amount
 * @property null|string $expected_close_date
 * @property string $stage
 * @property null|string $probability
 * @property int $customer_id
 * @property int $staff_user_id
 * @property null|\Illuminate\Support\Carbon $deleted_at
 * @property null|\Illuminate\Support\Carbon $created_at
 * @property null|\Illuminate\Support\Carbon $updated_at
 * @property-read Customer $customer
 * @property-read User $staff
 *
 * @method static Builder<static>|Opportunity newModelQuery()
 * @method static Builder<static>|Opportunity newQuery()
 * @method static Builder<static>|Opportunity onlyTrashed()
 * @method static Builder<static>|Opportunity query()
 * @method static Builder<static>|Opportunity where($column, $operator = null, $value = null, $boolean = 'and')
 * @method static Builder<static>|Opportunity whereAmount($value)
 * @method static Builder<static>|Opportunity whereCreatedAt($value)
 * @method static Builder<static>|Opportunity whereCustomerId($value)
 * @method static Builder<static>|Opportunity whereDeletedAt($value)
 * @method static Builder<static>|Opportunity whereDescription($value)
 * @method static Builder<static>|Opportunity whereExpectedCloseDate($value)
 * @method static Builder<static>|Opportunity whereId($value)
 * @method static Builder<static>|Opportunity whereProbability($value)
 * @method static Builder<static>|Opportunity whereStaffUserId($value)
 * @method static Builder<static>|Opportunity whereStage($value)
 * @method static Builder<static>|Opportunity whereTitle($value)
 * @method static Builder<static>|Opportunity whereUpdatedAt($value)
 * @method static Builder<static>|Opportunity withTrashed()
 * @method static Builder<static>|Opportunity withoutTrashed()
 * @method static \Database\Factories\OpportunityFactory factory($count = null, $state = [])
 *
 * @mixin \Eloquent
 */
class Opportunity extends Model
{
    use HasFactory;
    use SoftDeletes;

    /** @var array<int, string> */
    protected $fillable = [
        'title',
        'description',
        'amount',
        'expected_close_date',
        'stage',
        'probability',
        'customer_id',
        'staff_user_id',
    ];

    /** @var array<string, string> */
    protected $casts = [
        'expected_close_date' => 'date',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    /**
     * Get the customer that owns the opportunity.
     *
     * @return BelongsTo<Customer, $this>
     */
    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    /**
     * Get the staff user assigned to the opportunity.
     *
     * @return BelongsTo<User, $this>
     */
    public function staff(): BelongsTo
    {
        return $this->belongsTo(User::class, 'staff_user_id');
    }
}
