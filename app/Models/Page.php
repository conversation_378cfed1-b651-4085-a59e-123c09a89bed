<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Carbon;

/**
 * @property int $id
 * @property string $title
 * @property string $slug
 * @property null|string $content
 * @property null|string $seo_title
 * @property null|string $seo_description
 * @property null|string $seo_keywords
 * @property null|string $featured_image
 * @property bool $is_published
 * @property int $order
 * @property null|Carbon $created_at
 * @property null|Carbon $updated_at
 * @property null|Carbon $deleted_at
 * @property-read string $url
 *
 * @method static Builder<static>|Page newModelQuery()
 * @method static Builder<static>|Page newQuery()
 * @method static Builder<static>|Page onlyTrashed()
 * @method static Builder<static>|Page query()
 * @method static Builder<static>|Page where($column, $operator = null, $value = null, $boolean = 'and')
 * @method static Builder<static>|Page whereContent($value)
 * @method static Builder<static>|Page whereCreatedAt($value)
 * @method static Builder<static>|Page whereDeletedAt($value)
 * @method static Builder<static>|Page whereFeaturedImage($value)
 * @method static Builder<static>|Page whereId($value)
 * @method static Builder<static>|Page whereIsPublished($value)
 * @method static Builder<static>|Page whereOrder($value)
 * @method static Builder<static>|Page whereSeoDescription($value)
 * @method static Builder<static>|Page whereSeoKeywords($value)
 * @method static Builder<static>|Page whereSeoTitle($value)
 * @method static Builder<static>|Page whereSlug($value)
 * @method static Builder<static>|Page whereTitle($value)
 * @method static Builder<static>|Page whereUpdatedAt($value)
 * @method static Builder<static>|Page withTrashed()
 * @method static Builder<static>|Page withoutTrashed()
 * @method static \Database\Factories\PageFactory factory($count = null, $state = [])
 *
 * @mixin \Eloquent
 */
class Page extends Model
{
    use HasFactory;
    use SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'title',
        'slug',
        'content',
        'seo_title',
        'seo_description',
        'seo_keywords',
        'featured_image',
        'is_published',
        'order',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'is_published' => 'boolean',
        'order' => 'integer',
    ];

    /**
     * The attributes that should be mutated to dates.
     *
     * @var array<int, string>
     */
    protected $dates = [
        'deleted_at',
    ];

    /**
     * Get the route key for the model.
     */
    public function getRouteKeyName(): string
    {
        return 'slug';
    }

    /**
     * Get the URL to the page.
     * Accessor for url (->url)
     */
    public function getUrlAttribute(): string
    {
        return route('pages.show', $this->slug);
    }
}
