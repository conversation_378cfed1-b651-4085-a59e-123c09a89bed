<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Carbon;
use Spatie\Permission\Models\Permission as BasePermission;

/**
 * @property int $id
 * @property string $name
 * @property string $guard_name
 * @property null|Carbon $created_at
 * @property null|Carbon $updated_at
 * @property-read Collection<int, BasePermission> $permissions
 * @property-read null|int $permissions_count
 * @property-read Collection<int, \Spatie\Permission\Models\Role> $roles
 * @property-read null|int $roles_count
 * @property-read Collection<int, \App\Models\User> $users
 * @property-read null|int $users_count
 *
 * @method static Builder<static>|Permission newModelQuery()
 * @method static Builder<static>|Permission newQuery()
 * @method static Builder<static>|Permission permission($permissions, $without = false)
 * @method static Builder<static>|Permission query()
 * @method static Builder<static>|Permission role($roles, $guard = null, $without = false)
 * @method static Builder<static>|Permission whereCreatedAt($value)
 * @method static Builder<static>|Permission whereGuardName($value)
 * @method static Builder<static>|Permission whereId($value)
 * @method static Builder<static>|Permission whereName($value)
 * @method static Builder<static>|Permission whereUpdatedAt($value)
 * @method static Builder<static>|Permission withoutPermission($permissions)
 * @method static Builder<static>|Permission withoutRole($roles, $guard = null)
 *
 * @mixin \Eloquent
 */
class Permission extends BasePermission
{
    public const TEMPLATE_ACCESS = 'template.access';

    public const TEMPLATE_CREATE = 'template.create';

    public const TEMPLATE_UPDATE = 'template.update';

    public const TEMPLATE_DELETE = 'template.delete';
}
