<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Carbon;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\MediaLibrary\MediaCollections\Models\Collections\MediaCollection;
use Spatie\Sluggable\HasSlug;
use Spatie\Sluggable\SlugOptions;
use Spatie\Tags\HasTags;
use Spatie\Tags\Tag;

/**
 * @property int $id
 * @property string $title
 * @property string $slug
 * @property null|string $summary
 * @property string $content
 * @property null|string $featured_image
 * @property null|array<array-key, mixed> $gallery
 * @property int $category_id
 * @property int $author_id
 * @property null|Carbon $published_at
 * @property bool $is_featured
 * @property bool $is_published
 * @property null|array<array-key, mixed> $meta
 * @property int $view_count
 * @property null|Carbon $created_at
 * @property null|Carbon $updated_at
 * @property null|Carbon $deleted_at
 * @property-read User $author
 * @property-read PostCategory $category
 * @property-read mixed $featured_image_url
 * @property-read MediaCollection<int, Media> $media
 * @property-read null|int $media_count
 * @property Collection<int, Tag> $tags
 * @property-read null|int $tags_count
 *
 * @method static Builder<static>|Post active()
 * @method static Builder<static>|Post featured()
 * @method static Builder<static>|Post newModelQuery()
 * @method static Builder<static>|Post newQuery()
 * @method static Builder<static>|Post onlyTrashed()
 * @method static Builder<static>|Post published()
 * @method static Builder<static>|Post query()
 * @method static Builder<static>|Post where($column, $operator = null, $value = null, $boolean = 'and')
 * @method static Builder<static>|Post whereAuthorId($value)
 * @method static Builder<static>|Post whereCategoryId($value)
 * @method static Builder<static>|Post whereContent($value)
 * @method static Builder<static>|Post whereCreatedAt($value)
 * @method static Builder<static>|Post whereDeletedAt($value)
 * @method static Builder<static>|Post whereFeaturedImage($value)
 * @method static Builder<static>|Post whereGallery($value)
 * @method static Builder<static>|Post whereId($value)
 * @method static Builder<static>|Post whereIsFeatured($value)
 * @method static Builder<static>|Post whereIsPublished($value)
 * @method static Builder<static>|Post whereMeta($value)
 * @method static Builder<static>|Post wherePublishedAt($value)
 * @method static Builder<static>|Post whereSlug($value)
 * @method static Builder<static>|Post whereSummary($value)
 * @method static Builder<static>|Post whereTitle($value)
 * @method static Builder<static>|Post whereUpdatedAt($value)
 * @method static Builder<static>|Post whereViewCount($value)
 * @method static Builder<static>|Post withTrashed()
 * @method static Builder<static>|Post withoutTrashed()
 * @method static Builder<static>|Post withAllTags(\ArrayAccess|Tag|array<array-key, mixed>|string $tags, ?string $type = null)
 * @method static Builder<static>|Post withAllTagsOfAnyType($tags)
 * @method static Builder<static>|Post withAnyTags(\ArrayAccess|Tag|array<array-key, mixed>|string $tags, ?string $type = null)
 * @method static Builder<static>|Post withAnyTagsOfAnyType($tags)
 * @method static Builder<static>|Post withoutTags(\ArrayAccess|Tag|array<array-key, mixed>|string $tags, ?string $type = null)
 * @method static Builder<static>|Post withAnyTagsOfType(array<array-key, mixed>|string $type)
 *
 * @mixin \Eloquent
 */
class Post extends Model implements HasMedia
{
    use HasSlug;
    use HasTags;
    use InteractsWithMedia;
    use SoftDeletes;

    protected $fillable = [
        'title',
        'slug',
        'summary',
        'content',
        'category_id',
        'author_id',
        'published_at',
        'is_featured',
        'is_published',
        'meta',
        'view_count',
    ];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array<string, mixed>
     */
    protected $casts = [
        'published_at' => 'datetime',
        'is_featured' => 'boolean',
        'is_published' => 'boolean',
        'meta' => 'array',
        'gallery' => 'array',
    ];

    public function registerMediaCollections(): void
    {
        $this->addMediaCollection(Media::COLLECTION_POST_FEATURED_IMAGE)
            ->singleFile();
    }

    /**
     * Get the options for generating the slug.
     */
    public function getSlugOptions(): SlugOptions
    {
        return SlugOptions::create()
            ->generateSlugsFrom('title')
            ->saveSlugsTo('slug');
    }

    /**
     * @return BelongsTo<PostCategory, $this>
     */
    public function category(): BelongsTo
    {
        return $this->belongsTo(PostCategory::class, 'category_id');
    }

    /**
     * @return BelongsTo<User, $this>
     */
    public function author(): BelongsTo
    {
        return $this->belongsTo(User::class, 'author_id');
    }

    /**
     * Scope a query to only include published posts. (->published())
     *
     * @return Builder<static>
     */
    public function scopePublished(Builder $query): Builder
    {
        return $query->where('is_published', true)
            ->where('published_at', '<=', now());
    }

    /**
     * Scope a query to only include featured posts. (->featured())
     *
     * @return Builder<static>
     */
    public function scopeFeatured(Builder $query): Builder
    {
        return $query->where('is_featured', true);
    }

    public function incrementViewCount(): void
    {
        $this->increment('view_count');
    }

    /**
     * Accessor for featured image url (->featured_image_url)
     */
    public function getFeaturedImageUrlAttribute(): string
    {
        return $this->getFirstMediaUrl(Media::COLLECTION_POST_FEATURED_IMAGE);
    }
}
