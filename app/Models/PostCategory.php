<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * @property int $id
 * @property string $name
 * @property string $slug
 * @property null|string $description
 * @property null|string $icon
 * @property bool $is_active
 * @property null|int $parent_id
 * @property int $order_column
 * @property null|\Illuminate\Support\Carbon $created_at
 * @property null|\Illuminate\Support\Carbon $updated_at
 * @property null|\Illuminate\Support\Carbon $deleted_at
 * @property-read \Illuminate\Database\Eloquent\Collection<int, PostCategory> $children
 * @property-read null|int $children_count
 * @property-read null|PostCategory $parent
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Post> $posts
 * @property-read null|int $posts_count
 *
 * @method static Builder<static>|PostCategory active()
 * @method static Builder<static>|PostCategory newModelQuery()
 * @method static Builder<static>|PostCategory newQuery()
 * @method static Builder<static>|PostCategory onlyTrashed()
 * @method static Builder<static>|PostCategory query()
 * @method static Builder<static>|PostCategory getKey()
 * @method static Builder<static>|PostCategory where($column, $operator = null, $value = null, $boolean = 'and')
 * @method static Builder<static>|PostCategory whereCreatedAt($value)
 * @method static Builder<static>|PostCategory whereDeletedAt($value)
 * @method static Builder<static>|PostCategory whereDescription($value)
 * @method static Builder<static>|PostCategory whereIcon($value)
 * @method static Builder<static>|PostCategory whereId($value)
 * @method static Builder<static>|PostCategory whereIsActive($value)
 * @method static Builder<static>|PostCategory whereName($value)
 * @method static Builder<static>|PostCategory whereOrderColumn($value)
 * @method static Builder<static>|PostCategory whereParentId($value)
 * @method static Builder<static>|PostCategory whereSlug($value)
 * @method static Builder<static>|PostCategory whereUpdatedAt($value)
 * @method static Builder<static>|PostCategory withTrashed()
 * @method static Builder<static>|PostCategory withoutTrashed()
 *
 * @mixin Builder<static>
 * @mixin \Eloquent
 */
class PostCategory extends Model
{
    use SoftDeletes;

    protected $fillable = [
        'name',
        'slug',
        'description',
        'icon',
        'is_active',
        'parent_id',
        'order_column',
    ];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'is_active' => 'boolean',
    ];

    /**
     * Get the posts for the category.
     *
     * @return HasMany<Post, $this>
     */
    public function posts(): HasMany
    {
        return $this->hasMany(Post::class, 'category_id');
    }

    /**
     * Get the parent category.
     *
     * @return BelongsTo<PostCategory, $this>
     */
    public function parent(): BelongsTo
    {
        return $this->belongsTo(PostCategory::class, 'parent_id');
    }

    /**
     * Get the child categories.
     *
     * @return HasMany<PostCategory, $this>
     */
    public function children(): HasMany
    {
        return $this->hasMany(PostCategory::class, 'parent_id');
    }

    /**
     * Scope a query to only include active categories. (->active())
     *
     *
     * @return Builder<static>
     */
    public function scopeActive(Builder $query): Builder
    {
        return $query->where('is_active', true);
    }
}
