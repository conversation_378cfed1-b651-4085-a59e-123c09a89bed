<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Carbon;

/**
 * @property int $id
 * @property string $quote_number
 * @property string $title
 * @property null|string $description
 * @property numeric $amount
 * @property null|Carbon $valid_until
 * @property string $status
 * @property int $opportunity_id
 * @property int $created_by
 * @property null|Carbon $deleted_at
 * @property null|Carbon $created_at
 * @property null|Carbon $updated_at
 * @property-read User $creator
 * @property-read Opportunity $opportunity
 *
 * @method static Builder<static>|Quote newModelQuery()
 * @method static Builder<static>|Quote newQuery()
 * @method static Builder<static>|Quote onlyTrashed()
 * @method static Builder<static>|Quote query()
 * @method static Builder<static>|Quote where($column, $operator = null, $value = null, $boolean = 'and')
 * @method static Builder<static>|Quote whereAmount($value)
 * @method static Builder<static>|Quote whereCreatedAt($value)
 * @method static Builder<static>|Quote whereCreatedBy($value)
 * @method static Builder<static>|Quote whereDeletedAt($value)
 * @method static Builder<static>|Quote whereDescription($value)
 * @method static Builder<static>|Quote whereId($value)
 * @method static Builder<static>|Quote whereOpportunityId($value)
 * @method static Builder<static>|Quote whereQuoteNumber($value)
 * @method static Builder<static>|Quote whereStatus($value)
 * @method static Builder<static>|Quote whereTitle($value)
 * @method static Builder<static>|Quote whereUpdatedAt($value)
 * @method static Builder<static>|Quote whereValidUntil($value)
 * @method static Builder<static>|Quote withTrashed()
 * @method static Builder<static>|Quote withoutTrashed()
 * @method static \Database\Factories\QuoteFactory factory($count = null, $state = [])
 *
 * @mixin \Eloquent
 */
class Quote extends Model
{
    use HasFactory;
    use SoftDeletes;

    protected $fillable = [
        'quote_number',
        'title',
        'description',
        'amount',
        'valid_until',
        'status',
        'opportunity_id',
        'created_by',
    ];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'amount' => 'decimal:2',
        'valid_until' => 'date',
    ];

    /**
     * @return BelongsTo<Opportunity, $this>
     */
    public function opportunity(): BelongsTo
    {
        return $this->belongsTo(Opportunity::class);
    }

    /**
     * @return BelongsTo<User, $this>
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    protected static function boot(): void
    {
        parent::boot();

        static::creating(function ($quote) {
            if (empty($quote->quote_number)) {
                $quote->quote_number = 'QUO-'.strtoupper(uniqid());
            }
        });
    }
}
