<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Carbon;

/**
 * @property int $id
 * @property string $name
 * @property string $slug
 * @property null|string $description
 * @property null|string $icon
 * @property bool $is_active
 * @property null|Carbon $created_at
 * @property null|Carbon $updated_at
 * @property-read Collection<int, Template> $templates
 * @property-read null|int $templates_count
 *
 * @method static Builder<static>|TemplateCategory newModelQuery()
 * @method static Builder<static>|TemplateCategory newQuery()
 * @method static Builder<static>|TemplateCategory query()
 * @method static Builder<static>|TemplateCategory whereCreatedAt($value)
 * @method static Builder<static>|TemplateCategory whereDescription($value)
 * @method static Builder<static>|TemplateCategory whereIcon($value)
 * @method static Builder<static>|TemplateCategory whereId($value)
 * @method static Builder<static>|TemplateCategory whereIsActive($value)
 * @method static Builder<static>|TemplateCategory whereName($value)
 * @method static Builder<static>|TemplateCategory whereSlug($value)
 * @method static Builder<static>|TemplateCategory whereUpdatedAt($value)
 *
 * @mixin Builder
 * @mixin \Eloquent
 */
class TemplateCategory extends Model
{
    protected $fillable = [
        'name',
        'slug',
        'description',
        'icon',
        'is_active',
    ];

    /** @var array<string, string> */
    protected $casts = [
        'is_active' => 'boolean',
    ];

    /**
     * Get the templates for the category.
     *
     * @return HasMany<Template, $this>
     */
    public function templates(): HasMany
    {
        return $this->hasMany(Template::class, 'category_id', 'id');
    }
}
