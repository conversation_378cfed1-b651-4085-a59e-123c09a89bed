<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * @property int $id
 * @property int $customer_user_id
 * @property int $template_id
 * @property \Illuminate\Support\Carbon $created_at
 * @property \Illuminate\Support\Carbon $updated_at
 * @property \Illuminate\Support\Carbon $deleted_at
 * @property-read CustomerUser $customerUser
 * @property-read Template $template
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|TemplateFavorite newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|TemplateFavorite newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|TemplateFavorite query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|TemplateFavorite onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|TemplateFavorite withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|TemplateFavorite withoutTrashed()
 *
 * @mixin \Eloquent
 */
class TemplateFavorite extends Model
{
    use SoftDeletes;

    protected $fillable = [
        'customer_user_id',
        'template_id',
    ];

    /**
     * Get the customer user that owns the favorite.
     */
    public function customerUser(): BelongsTo
    {
        return $this->belongsTo(CustomerUser::class);
    }

    /**
     * Get the template that is favorited.
     */
    public function template(): BelongsTo
    {
        return $this->belongsTo(Template::class);
    }
}
