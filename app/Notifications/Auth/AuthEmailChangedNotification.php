<?php

namespace App\Notifications\Auth;

use App\Mail\Auth\AuthEmailChangedMail;
use App\Models\CustomerUser;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Notification;

class AuthEmailChangedNotification extends Notification implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new notification instance.
     */
    public function __construct(
        public CustomerUser $user,
        public string $oldEmail
    ) {
        //
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): AuthEmailChangedMail
    {
        return (new AuthEmailChangedMail($this->user, $this->oldEmail))
            ->to($this->oldEmail);
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'user_id' => $this->user->id,
            'old_email' => $this->oldEmail,
            'new_email' => $this->user->email,
            'changed_at' => now(),
        ];
    }
}
