<?php

namespace App\Services;

use App\Enums\BusinessVerificationSegmentEnum;
use App\Enums\BusinessVerificationStatusEnum;
use App\Jobs\SendErcReminderAfter30MinutesJob;
use App\Jobs\SendErcReminderBySegmentJob;
use App\Jobs\SendErcRejectedNotificationJob;
use App\Jobs\SendErcSubmittedNotificationJob;
use App\Jobs\SendErcVerifiedNotificationJob;
use App\Models\BusinessVerification;
use App\Models\Customer;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class BusinessVerificationService
{
    /**
     * Initialize business verification for a new customer.
     *
     * @throws \Throwable
     */
    public function initializeForCustomer(Customer $customer): BusinessVerification
    {
        return DB::transaction(function () use ($customer) {
            $verification = $customer->getOrCreateBusinessVerification();

            if (!$verification instanceof BusinessVerification) {
                throw new \InvalidArgumentException('Verification is not an instance of BusinessVerification');
            }

            // Schedule reminder emails
            $this->scheduleReminderEmails($verification);
            
            Log::info('Business verification initialized', [
                'customer_id' => $customer->id,
                'verification_id' => $verification->id,
                'segment' => $verification->segment->value,
            ]);
            
            return $verification;
        });
    }

    /**
     * Schedule reminder emails for a verification.
     */
    public function scheduleReminderEmails(BusinessVerification $verification): void
    {
        if (!$verification->status->needsReminder()) {
            return;
        }

        // Cancel any existing reminder jobs for this verification
        $this->cancelReminderJobs($verification);

        // Schedule 30-minute reminder
        SendErcReminderAfter30MinutesJob::dispatch($verification)
            ->delay(now()->addMinutes(30))
            ->onQueue('emails');

        // Schedule segment-based reminder
        $reminderDelay = now()->addDays($verification->segment->getReminderDays());
        SendErcReminderBySegmentJob::dispatch($verification)
            ->delay($reminderDelay)
            ->onQueue('emails');

        Log::info('Reminder emails scheduled', [
            'verification_id' => $verification->id,
            'segment' => $verification->segment->value,
            'reminder_days' => $verification->segment->getReminderDays(),
        ]);
    }

    /**
     * Cancel reminder jobs for a verification.
     */
    public function cancelReminderJobs(BusinessVerification $verification): void
    {
        // Note: In a production environment, you might want to use a more sophisticated
        // job cancellation mechanism, such as storing job IDs or using Laravel Horizon
        Log::info('Reminder jobs cancelled', [
            'verification_id' => $verification->id,
        ]);
    }

    /**
     * Update segment for a verification and reschedule reminders if needed.
     */
    public function updateSegment(BusinessVerification $verification): bool
    {
        $oldSegment = $verification->segment;
        $newSegment = BusinessVerificationSegmentEnum::determineSegment($verification->customer_id);
        
        if ($newSegment === $oldSegment) {
            return true;
        }

        // Update segment
        $verification->update(['segment' => $newSegment]);

        // If status still needs reminders, reschedule with new timing
        if ($verification->status->needsReminder()) {
            $this->scheduleReminderEmails($verification);
        }

        Log::info('Segment updated', [
            'verification_id' => $verification->id,
            'old_segment' => $oldSegment->value,
            'new_segment' => $newSegment->value,
        ]);

        return true;
    }

    /**
     * Upload ERC file for verification.
     *
     * @throws \Throwable
     */
    public function uploadErcFile(BusinessVerification $verification, UploadedFile $file): bool
    {
        if (!$verification->status->canUploadFile()) {
            throw new \InvalidArgumentException('Cannot upload file in current status: ' . $verification->status->value);
        }

        return DB::transaction(function () use ($verification, $file) {
            // Delete old file if exists
            if ($verification->erc_file_path && Storage::disk('private')->exists($verification->erc_file_path)) {
                Storage::disk('private')->delete($verification->erc_file_path);
            }

            // Generate unique filename
            $filename = 'erc_' . $verification->customer_id . '_' . time() . '.' . $file->getClientOriginalExtension();
            $path = 'business-verifications/' . $filename;

            // Store file
            $file->storeAs('business-verifications', $filename, 'private');

            // Calculate file hash
            $fileHash = hash_file('sha256', $file->getRealPath());

            // Update verification record
            $verification->update([
                'erc_file_path' => $path,
                'erc_file_name' => $file->getClientOriginalName(),
                'erc_file_hash' => $fileHash,
                'erc_file_size' => $file->getSize(),
                'erc_file_mime_type' => $file->getMimeType(),
            ]);

            // Mark as submitted and send notification
            $this->markAsSubmitted($verification);

            Log::info('ERC file uploaded', [
                'verification_id' => $verification->id,
                'filename' => $filename,
                'size' => $file->getSize(),
                'hash' => $fileHash,
            ]);

            return true;
        });
    }

    /**
     * Mark verification as submitted.
     */
    public function markAsSubmitted(BusinessVerification $verification): bool
    {
        if (!$verification->markAsSubmitted()) {
            return false;
        }

        // Cancel reminder emails
        $this->cancelReminderJobs($verification);

        // Send submitted notification
        SendErcSubmittedNotificationJob::dispatch($verification)
            ->onQueue('emails');

        Log::info('Verification marked as submitted', [
            'verification_id' => $verification->id,
        ]);

        return true;
    }

    /**
     * Mark verification as verified by admin.
     */
    public function markAsVerified(BusinessVerification $verification, int $reviewedBy, ?string $adminNotes = null): bool
    {
        if (!$verification->markAsVerified($reviewedBy, $adminNotes)) {
            return false;
        }

        // Send verified notification
        SendErcVerifiedNotificationJob::dispatch($verification)
            ->onQueue('emails');

        Log::info('Verification marked as verified', [
            'verification_id' => $verification->id,
            'reviewed_by' => $reviewedBy,
        ]);

        return true;
    }

    /**
     * Mark verification as rejected by admin.
     */
    public function markAsRejected(BusinessVerification $verification, int $reviewedBy, string $rejectionReason, ?string $adminNotes = null): bool
    {
        if (!$verification->markAsRejected($reviewedBy, $rejectionReason, $adminNotes)) {
            return false;
        }

        // Send rejected notification
        SendErcRejectedNotificationJob::dispatch($verification)
            ->onQueue('emails');

        // Reschedule reminders since user can resubmit
        $this->scheduleReminderEmails($verification);

        Log::info('Verification marked as rejected', [
            'verification_id' => $verification->id,
            'reviewed_by' => $reviewedBy,
            'reason' => $rejectionReason,
        ]);

        return true;
    }

    /**
     * Get verification statistics.
     */
    public function getStatistics(): array
    {
        return [
            'total' => BusinessVerification::count(),
            'unsubmitted' => BusinessVerification::where('status', BusinessVerificationStatusEnum::UNSUBMITTED)->count(),
            'submitted' => BusinessVerification::where('status', BusinessVerificationStatusEnum::SUBMITTED)->count(),
            'in_review' => BusinessVerification::where('status', BusinessVerificationStatusEnum::IN_REVIEW)->count(),
            'verified' => BusinessVerification::where('status', BusinessVerificationStatusEnum::VERIFIED)->count(),
            'rejected' => BusinessVerification::where('status', BusinessVerificationStatusEnum::REJECTED)->count(),
            'pending_review' => BusinessVerification::pendingReview()->count(),
            'needs_30m_reminder' => BusinessVerification::needsThirtyMinuteReminder()->count(),
            'needs_segment_reminder' => BusinessVerification::needsSegmentReminder()->count(),
        ];
    }

    /**
     * Process pending reminders (for scheduled commands).
     */
    public function processPendingReminders(): void
    {
        // Process 30-minute reminders
        $thirtyMinuteReminders = BusinessVerification::needsThirtyMinuteReminder()->get();
        foreach ($thirtyMinuteReminders as $verification) {
            SendErcReminderAfter30MinutesJob::dispatch($verification)->onQueue('emails');
        }

        // Process segment reminders
        $segmentReminders = BusinessVerification::needsSegmentReminder()->get();
        foreach ($segmentReminders as $verification) {
            SendErcReminderBySegmentJob::dispatch($verification)->onQueue('emails');
        }

        Log::info('Pending reminders processed', [
            '30m_reminders' => $thirtyMinuteReminders->count(),
            'segment_reminders' => $segmentReminders->count(),
        ]);
    }

    /**
     * Validate ERC file.
     */
    public function validateErcFile(UploadedFile $file): array
    {
        $errors = [];

        // Check file size (max 10MB)
        if ($file->getSize() > 10 * 1024 * 1024) {
            $errors[] = 'File size must not exceed 10MB';
        }

        // Check file type
        $allowedMimes = ['image/jpeg', 'image/png', 'application/pdf'];
        if (!in_array($file->getMimeType(), $allowedMimes)) {
            $errors[] = 'File must be JPG, PNG, or PDF format';
        }

        // Check file extension
        $allowedExtensions = ['jpg', 'jpeg', 'png', 'pdf'];
        if (!in_array(strtolower($file->getClientOriginalExtension()), $allowedExtensions)) {
            $errors[] = 'File extension must be jpg, jpeg, png, or pdf';
        }

        return $errors;
    }
}
