<?php

namespace App\Services;

use Carbon\Carbon;
use FilesystemIterator;
use Illuminate\Support\Facades\Log;
use RecursiveDirectoryIterator;
use RecursiveIteratorIterator;
use Spatie\Sitemap\Sitemap;
use Spatie\Sitemap\SitemapIndex;
use Spatie\Sitemap\Tags\Url as SitemapUrl;

class SitemapService
{
    /** @var array<string, mixed> */
    protected array $dynamicRoutes = [
        'pages.dynamic' => [
            'type' => 'custom',
            'handler' => 'generateBladePagesSitemap',
            'priority' => 0.8,
        ],
        'demo.vi.show' => [
            'model' => \App\Models\Template::class,
            'columns' => ['id', 'slug', 'updated_at'],
            'conditions' => [],
        ],
        'demo.show' => [
            'model' => \App\Models\Template::class,
            'columns' => ['id', 'slug', 'updated_at'],
            'conditions' => [],
        ],
        'templates.show' => [
            'model' => \App\Models\Template::class,
            'columns' => ['id', 'slug', 'updated_at'],
            'conditions' => [],
        ],
        'templates.vi.show' => [
            'model' => \App\Models\Template::class,
            'columns' => ['id', 'slug', 'updated_at'],
            'conditions' => [],
        ],
        'templates.category' => [
            'model' => \App\Models\TemplateCategory::class,
            'columns' => ['id', 'slug', 'updated_at'],
            'conditions' => [],
            'route_params' => ['category_slug' => 'slug'],
        ],
        'templates.vi.category' => [
            'model' => \App\Models\TemplateCategory::class,
            'columns' => ['id', 'slug', 'updated_at'],
            'conditions' => [],
            'route_params' => ['category_slug' => 'slug'],
        ],
        'consultation.vi.detail' => [
            'model' => \App\Models\Post::class,
            'columns' => ['id', 'slug', 'updated_at', 'category_id'],
            'conditions' => [],
            'with' => ['category'],
            'route_params' => [
                'category_slug' => 'category.slug',
                'detail_slug' => 'slug',
            ],
        ],
        'consultation.detail' => [
            'model' => \App\Models\Post::class,
            'columns' => ['id', 'slug', 'updated_at', 'category_id'],
            'conditions' => [],
            'with' => ['category'],
            'route_params' => [
                'category_slug' => 'category.slug',
                'detail_slug' => 'slug',
            ],
        ],
        'consultation.vi.index' => [
            'model' => \App\Models\PostCategory::class,
            'columns' => ['id', 'slug', 'updated_at'],
            'conditions' => [],
            'route_params' => ['category_slug' => 'slug'],
        ],
        'consultation.index' => [
            'model' => \App\Models\PostCategory::class,
            'columns' => ['id', 'slug', 'updated_at'],
            'conditions' => [],
            'route_params' => ['category_slug' => 'slug'],
        ],
        'pages.show' => [
            'model' => \App\Models\Page::class,
            'columns' => ['id', 'slug', 'updated_at'],
            'conditions' => [],
        ],
    ];

    public function __construct(
        protected string $basePath = '',
        protected string $baseUrl = ''
    ) {
        $this->basePath = $basePath ?: storage_path('app/sitemaps');
        $this->baseUrl = $baseUrl ?: config('app.url');
    }

    public function generate(): bool
    {
        try {
            $this->ensureDirectoriesExist();

            $this->generateStaticSitemap();

            foreach (array_keys($this->dynamicRoutes) as $routeName) {
                if (isset($this->dynamicRoutes[$routeName]['type'])
                    && $this->dynamicRoutes[$routeName]['type'] === 'custom'
                ) {
                    $this->{$this->dynamicRoutes[$routeName]['handler']}();
                } else {
                    $this->generateDynamicSitemap($routeName);
                }
            }

            $this->generateSitemapIndex();

            ! windows_os() && $this->createSymlink();

            return true;
        } catch (\Exception $e) {
            Log::error('Sitemap generation failed: '.$e->getMessage());

            return false;
        }
    }

    protected function generateStaticSitemap(): void
    {
        $sitemap = Sitemap::create();
        $now = Carbon::now();

        foreach ($this->getAllWebRoutes() as $route) {
            $this->addUrlToSitemap($sitemap, [
                'name' => $route['name'],
                'freq' => $route['freq'],
                'priority' => $route['priority'],
            ], $now);
        }

        $sitemap->writeToFile("{$this->basePath}/static-sitemap.xml");
    }

    /**
     * @param  array<string, mixed>  $route
     */
    protected function addUrlToSitemap(Sitemap $sitemap, array $route, Carbon $now): void
    {
        try {
            $url = SitemapUrl::create(route($route['name'], $route['params'] ?? []))
                ->setChangeFrequency($route['freq'])
                ->setPriority($route['priority']);

            // If the route has a model instance with updated_at, use that
            $model = $route['params']['model'] ?? null;
            if (isset($model) && is_object($model)
                && method_exists($model, 'getUpdatedAtColumn')
            ) {
                $url->setLastModificationDate($model->updated_at ?? $now);
            } else {
                $url->setLastModificationDate($now);
            }

            $sitemap->add($url);
        } catch (\Exception $e) {
            Log::error('Failed to add URL to sitemap: '.$e->getMessage());
        }
    }

    protected function generateDynamicSitemap(string $routeName): void
    {
        if (! ($config = $this->dynamicRoutes[$routeName] ?? null) || ! class_exists($modelClass = $config['model'])) {
            return;
        }

        $sitemap = Sitemap::create();
        $query = $modelClass::query()->select($config['columns']);
        $chunkSize = $config['chunk_size'] ?? 200;
        $hasItems = false;

        ! empty($config['conditions']) && $query->where($config['conditions']);

        $priority = $this->getRoutePriority($routeName);

        // Load relationships if specified
        if (isset($config['with'])) {
            $relations = is_array($config['with']) ? $config['with'] : [$config['with']];
            $query->with($relations);
        }

        $query->chunk(
            $chunkSize,
            function ($items) use ($sitemap, $routeName, $priority, $config, &$hasItems) {
                $items->each(function ($item) use ($sitemap, $routeName, $priority, $config, &$hasItems) {
                    $this->addDynamicUrlToSitemap($sitemap, array_merge(
                        [
                            'route_name' => $routeName,
                            'freq' => $priority['freq'],
                            'priority' => $priority['priority'],
                        ],
                        isset($config['route_params']) ? ['route_params' => $config['route_params']] : []
                    ), $item);
                    $hasItems = true;
                });
            }
        );

        if ($hasItems) {
            $fileName = str_replace('.', '-', $routeName).'-sitemap.xml';
            $sitemap->writeToFile("{$this->basePath}/{$fileName}");
        } else {
            Log::warning("No items found for sitemap: {$routeName}");
        }
    }

    /**
     * @return array<string, mixed>
     */
    protected function getRoutePriority(string $routeName): array
    {
        $default = ['freq' => 'weekly', 'priority' => 0.5];

        $priorities = [
            'home' => ['freq' => 'daily', 'priority' => 1.0],
            'demo.index' => ['freq' => 'daily', 'priority' => 0.9],
            'templates.index' => ['freq' => 'weekly', 'priority' => 0.9],
            'pages.show' => ['freq' => 'weekly', 'priority' => 0.7],
        ];

        foreach (['admin.', 'api.'] as $prefix) {
            if (str_starts_with($routeName, $prefix)) {
                return ['freq' => 'monthly', 'priority' => 0.1];
            }
        }

        foreach ($priorities as $prefix => $config) {
            if (str_starts_with($routeName, $prefix)) {
                return $config;
            }
        }

        return $default;
    }

    /**
     * @return list<array<string, mixed>>
     */
    protected function getAllWebRoutes(): array
    {
        $routes = [];
        /** @var \Illuminate\Routing\RouteCollection $routeCollection */
        $routeCollection = \Illuminate\Support\Facades\Route::getRoutes();

        /** @var array<int, string> $ignoredPrefixes */
        $ignoredPrefixes = [
            'debugbar.',
            'ignition.',
            'sanctum.',
            'passport.',
            'horizon.',
            'telescope.',
            'admin.',
            'api.',
            'api/',
            'filament.',
            'livewire.',
            'verification.verify',
            'verification.send',
        ];

        foreach ($routeCollection as $route) {
            $routeName = $route->getName();

            // Only include routes from web (check if route action is in web middleware group)
            $middleware = $route->getAction('middleware') ?? [];
            $isWebRoute = is_array($middleware)
                ? in_array('web', $middleware)
                : $middleware === 'web';

            if (! $isWebRoute || $routeName === null || $this->shouldIgnoreRoute($routeName, $ignoredPrefixes)) {
                continue;
            }

            if (isset($this->dynamicRoutes[$routeName])) {
                continue;
            }

            $priority = $this->getRoutePriority($routeName);
            $routes[] = [
                'name' => $routeName,
                'freq' => $priority['freq'],
                'priority' => $priority['priority'],
            ];
        }

        return $routes;
    }

    /**
     * @param  array<int, string>  $ignoredPrefixes
     */
    protected function shouldIgnoreRoute(string $routeName, array $ignoredPrefixes): bool
    {
        foreach ($ignoredPrefixes as $prefix) {
            if (str_starts_with($routeName, $prefix)) {
                return true;
            }
        }

        return false;
    }

    /**
     * @param  array<string, mixed>  $config
     */
    protected function addDynamicUrlToSitemap(Sitemap $sitemap, array $config, object $item): void
    {
        try {
            $params = [];

            if (isset($config['route_params'])) {
                foreach ($config['route_params'] as $param => $valuePath) {
                    $value = $item;
                    // Handle nested properties (e.g., 'category.slug')
                    foreach (explode('.', $valuePath) as $segment) {
                        $value = $value->{$segment} ?? null;
                        if ($value === null) {
                            break;
                        }
                    }
                    $params[$param] = $value;
                }
            } else {
                $params['slug'] = $item->slug ?? null;
            }

            if (in_array(null, $params, true)) {
                return;
            }

            $sitemap->add(
                SitemapUrl::create(route($config['route_name'], $params))
                    ->setLastModificationDate($item->updated_at ?? Carbon::now())
                    ->setChangeFrequency($config['freq'])
                    ->setPriority($config['priority'])
            );
        } catch (\Exception $e) {
            Log::error("Failed to add {$config['route_name']} URL to sitemap: ".$e->getMessage());
        }
    }

    protected function generateSitemapIndex(): void
    {
        $index = SitemapIndex::create();
        $addedFiles = [];

        $sitemaps = glob("{$this->basePath}/*-sitemap.xml") ?: [];
        foreach ($sitemaps as $sitemapFile) {
            if (is_file($sitemapFile)) {
                $fileName = basename($sitemapFile);
                $addedFiles[] = $fileName;
                $index->add(url('/sitemaps/'.$fileName));
            }
        }

        if (! in_array('static-sitemap.xml', $addedFiles, true)) {
            $staticSitemap = "{$this->basePath}/static-sitemap.xml";
            if (file_exists($staticSitemap)) {
                $index->add(url('/sitemaps/static-sitemap.xml'));
            }
        }

        $index->writeToFile("{$this->basePath}/sitemap.xml");
    }

    /**
     * Get all blade files from the pages directory recursively
     */
    /**
     * @return string[]
     */
    protected function getPageFiles(?string $directory = null, string $basePath = ''): array
    {
        $pages = [];
        $dir = $directory ?: resource_path('views/pages/landing');

        $items = new \RecursiveIteratorIterator(
            new \RecursiveDirectoryIterator($dir, FilesystemIterator::SKIP_DOTS),
            \RecursiveIteratorIterator::SELF_FIRST
        );

        foreach ($items as $item) {
            if ($item->isFile() && $item->getExtension() === 'php') {
                $relativePath = str_replace([$dir, '.blade.php', '//', '\\'], ['', '', '/', '/'], $item->getPathname());
                $pages[] = ltrim($relativePath, '/');
            }
        }

        return $pages;
    }

    /**
     * Generate sitemap for dynamic pages by scanning the views directory
     */
    protected function generateBladePagesSitemap(): void
    {
        $sitemap = Sitemap::create();
        $priority = $this->getRoutePriority('pages.dynamic');

        // Get all blade files from the pages directory
        $pages = $this->getPageFiles();

        // Define change frequency based on URL patterns
        $changeFreq = [
            'chinh-sach' => 'monthly',
            'dieu-khoan' => 'monthly',
            'dich-vu' => 'weekly',
            'default' => 'weekly',
        ];

        foreach ($pages as $page) {
            try {
                // Skip partials and components (files starting with _)
                if (str_contains($page, '/_') || basename($page)[0] === '_') {
                    continue;
                }

                // Determine change frequency based on URL pattern
                $freq = $changeFreq['default'];
                foreach ($changeFreq as $pattern => $frequency) {
                    if (str_contains($page, $pattern)) {
                        $freq = $frequency;

                        break;
                    }
                }

                // Get the full path to the blade file
                $bladePath = resource_path("views/pages/landing/{$page}.blade.php");

                // Get the last modified time of the blade file
                $fileMTime = filemtime($bladePath);
                $lastModified = $fileMTime !== false
                    ? Carbon::createFromTimestamp($fileMTime)
                    : Carbon::now();

                // Remove .blade.php extension and ensure forward slashes for URLs
                $routeParam = str_replace('.blade.php', '', $page);

                // Generate the URL with the correct path structure
                $url = url($routeParam);

                $sitemap->add(
                    SitemapUrl::create($url)
                        ->setLastModificationDate($lastModified)
                        ->setChangeFrequency($freq)
                        ->setPriority($priority['priority'])
                );
            } catch (\Exception $e) {
                Log::error("Failed to add page {$page} to sitemap: ".$e->getMessage());
            }
        }

        $sitemap->writeToFile("{$this->basePath}/pages-sitemap.xml");
    }

    protected function ensureDirectoriesExist(): void
    {
        if (! is_dir($this->basePath)) {
            mkdir($this->basePath, 0755, true);
        }
    }

    public function cleanOldSitemaps(): bool
    {
        if (! is_dir($this->basePath)) {
            return true;
        }

        try {
            $files = new RecursiveIteratorIterator(
                new RecursiveDirectoryIterator($this->basePath, RecursiveDirectoryIterator::SKIP_DOTS),
                RecursiveIteratorIterator::CHILD_FIRST
            );

            $deletedCount = 0;
            foreach ($files as $file) {
                if ($file->isFile() && preg_match('/sitemap.*\.xml$/i', $file->getFilename())) {
                    if (@unlink($file->getRealPath())) {
                        $deletedCount++;
                    }
                }
            }

            Log::info("Cleaned up {$deletedCount} old sitemap files");

            return true;
        } catch (\Exception $e) {
            Log::error('Failed to clean old sitemaps: '.$e->getMessage());

            return false;
        }
    }

    public function createSymlink(): bool
    {
        try {
            $link = public_path('sitemaps');
            file_exists($link) && (is_link($link) ? @unlink($link) : @rename($link, $link.'_'.time()));

            ! is_dir($dir = dirname($link)) && @mkdir($dir, 0755, true);

            return @symlink($this->basePath, $link) !== false;
        } catch (\Exception $e) {
            Log::error('Failed to create symlink: '.$e->getMessage());

            return false;
        }
    }
}
