<?php

namespace App\Support\FileNamer;

use Spatie\MediaLibrary\Support\FileNamer\DefaultFileNamer;

final class CSlantMediaFileNamer extends DefaultFileNamer
{
    public function originalFileName(string $fileName): string
    {
        $extLength = strlen(pathinfo($fileName, PATHINFO_EXTENSION));

        $baseName = substr($fileName, 0, strlen($fileName) - ($extLength ? $extLength + 1 : 0));

        return $baseName;
    }
}
