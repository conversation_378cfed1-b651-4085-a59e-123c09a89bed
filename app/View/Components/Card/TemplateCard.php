<?php

namespace App\View\Components\Card;

use App\Models\Template;
use Closure;
use Illuminate\Contracts\View\View;
use Illuminate\View\Component;

class TemplateCard extends Component
{
    /**
     * Create a new component instance.
     */
    public function __construct(
        public Template $template,
    ) {
        //
    }

    /**
     * Get the view / contents that represent the component.
     */
    public function render(): View|Closure|string
    {
        return view('components.card.template-card');
    }
}
