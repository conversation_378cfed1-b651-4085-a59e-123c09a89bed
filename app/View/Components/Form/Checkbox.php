<?php

namespace App\View\Components\Form;

use Illuminate\Contracts\View\View;
use Illuminate\Support\Str;
use Illuminate\View\Component;

class Checkbox extends Component
{
    /**
     * ID của thẻ input.
     */
    public string $id;

    /**
     * Tạo một instance mới cho component.
     *
     * @param  string  $name  Tên của checkbox.
     * @param  string  $label  Text cho label.
     * @param  bool  $checked  Trạng thái được chọn mặc định.
     * @param  mixed  $value  Giá trị của checkbox khi được gửi đi (mặc định là 1).
     * @param  null|string  $id  ID của checkbox.
     */
    public function __construct(
        public ?string $name,
        public string $label,
        public bool $checked = false,
        public mixed $value = '1',
        ?string $id = null
    ) {
        $this->id = $id ?? 'checkbox-'.Str::kebab($name);
    }

    /**
     * Lấy view / nội dung để render component.
     */
    public function render(): View
    {
        return view('components.form.checkbox');
    }
}
