<?php

namespace App\View\Components\Form;

use Illuminate\Contracts\View\View;
use Illuminate\Support\Str;
use Illuminate\View\Component;

class Input extends Component
{
    /**
     * ID của thẻ input.
     */
    public string $id;

    /**
     * Tạo một instance mới cho component.
     *
     * @param  string  $name  Tê<PERSON> của input, dùng cho 'name' attribute và xác định lỗi validation.
     * @param  string  $type  Loại của input (text, email, password,...).
     * @param  null|string  $id  ID của input. Nếu không cung cấp, sẽ tự động tạo từ $name.
     * @param  null|string  $value  Giá trị ban đầu của input.
     */
    public function __construct(
        public string $name,
        public string $type = 'text',
        ?string $id = null,
        public ?string $value = null
    ) {
        $this->id = $id ?? 'input-'.Str::kebab($name);
    }

    /**
     * Lấy view / nội dung để render component.
     */
    public function render(): View
    {
        return view('components.form.input');
    }
}
