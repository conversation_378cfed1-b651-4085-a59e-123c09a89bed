<?php

namespace App\View\Components\Form;

use Illuminate\Contracts\View\View;
use Illuminate\Support\Str;
use Illuminate\View\Component;

class Radio extends Component
{
    /**
     * ID của thẻ input.
     */
    public string $id;

    /**
     * Tạo một instance mới cho component.
     *
     * @param  string  $name  Tên của nhóm radio button.
     * @param  string  $label  Text cho label.
     * @param  mixed  $value  Giá trị của radio button này.
     * @param  bool  $checked  Trạng thái được chọn mặc định.
     * @param  null|string  $id  ID của radio button.
     */
    public function __construct(
        public ?string $name,
        public string $label,
        public mixed $value,
        public bool $checked = false,
        ?string $id = null
    ) {
        $this->id = $id ?? 'radio-'.Str::kebab($name).'-'.Str::kebab($value);
    }

    /**
     * Lấy view / nội dung để render component.
     */
    public function render(): View
    {
        return view('components.form.radio');
    }
}
