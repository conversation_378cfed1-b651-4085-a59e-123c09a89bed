<?php

namespace App\View\Components\Form;

use Illuminate\Contracts\View\View;
use Illuminate\Support\Str;
use Illuminate\View\Component;

class Select extends Component
{
    /**
     * ID của thẻ select.
     */
    public string $id;

    /**
     * Tạo một instance mới cho component.
     *
     * @param  string  $name  Tên của select, dùng cho 'name' attribute và xác định lỗi.
     * @param  array<int, array{text: string, url?: string}>  $options  Mảng các lựa chọn, định dạng ['value' => 'Text'].
     * @param  null|string  $id  ID của select.
     * @param  mixed  $selected  Gi<PERSON> trị được chọn mặc định.
     */
    public function __construct(
        public string $name,
        public array $options = [],
        ?string $id = null,
        public mixed $selected = null
    ) {
        $this->id = $id ?? 'select-'.Str::kebab($name);
    }

    /**
     * Lấy view / nội dung để render component.
     */
    public function render(): View
    {
        return view('components.form.select');
    }
}
