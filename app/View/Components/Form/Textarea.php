<?php

namespace App\View\Components\Form;

use Illuminate\Contracts\View\View;
use Illuminate\Support\Str;
use Illuminate\View\Component;

class Textarea extends Component
{
    /**
     * ID của thẻ textarea.
     */
    public string $id;

    /**
     * Tạo một instance mới cho component.
     *
     * @param  string  $name  Tên của textarea, dùng cho 'name' attribute và xác định lỗi validation.
     * @param  null|string  $id  ID của textarea. Nếu không cung cấp, sẽ tự động tạo từ $name.
     * @param  null|string  $value  Nội dung ban đầu của textarea.
     */
    public function __construct(
        public string $name,
        ?string $id = null,
        public ?string $value = null
    ) {
        $this->id = $id ?? 'textarea-'.Str::kebab($name);
    }

    /**
     * Lấy view / nội dung để render component.
     */
    public function render(): View
    {
        return view('components.form.textarea');
    }
}
