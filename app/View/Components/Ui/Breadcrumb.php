<?php

namespace App\View\Components\Ui;

use Illuminate\Contracts\View\View;
use Illuminate\View\Component;

class Breadcrumb extends Component
{
    /**
     * Tạo một instance mới cho component.
     *
     * @param  array<int, array{text: string, url?: string}>  $items  Mảng các breadcrumb, mỗi item là một mảng con ['text' => '...', 'url' => '...'].
     */
    public function __construct(
        public array $items = []
    ) {}

    /**
     * Lấy view / nội dung để render component.
     */
    public function render(): View
    {
        return view('components.ui.breadcrumb');
    }
}
