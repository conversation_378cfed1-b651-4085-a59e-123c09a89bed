<?php

namespace App\View\Components\Ui;

use Illuminate\Contracts\View\View;
use Illuminate\View\Component;

class But<PERSON> extends Component
{
    /**
     * Các class CSS cơ bản cho button.
     */
    public string $baseClasses = 'inline-flex items-center justify-center rounded-md font-semibold focus:outline-none focus:ring-2 focus:ring-offset-2 transition ease-in-out duration-150';

    /**
     * Các class CSS cho các biến thể (variant) của button.
     *
     * @var array<string, string>
     */
    public array $variantClasses = [
        'primary' => 'bg-emerald-600 text-white hover:bg-emerald-700 focus:ring-emerald-500',
        'secondary' => 'bg-gray-200 text-gray-800 hover:bg-gray-300 focus:ring-gray-500',
        'danger' => 'bg-red-600 text-white hover:bg-red-700 focus:ring-red-500',
        'link' => 'text-emerald-600 hover:text-emerald-700 focus:ring-emerald-500',
    ];

    /**
     * Các class CSS cho các kích thước (size) của button.
     *
     * @var array<string, string>
     */
    public array $sizeClasses = [
        'sm' => 'px-2.5 py-1.5 text-xs',
        'md' => 'px-4 py-2 text-sm',
        'lg' => 'px-6 py-3 text-base',
    ];

    /**
     * Các class CSS cuối cùng sẽ được áp dụng.
     */
    public string $appliedClasses;

    /**
     * Tạo một instance mới cho component.
     *
     * @param  string  $variant  Kiểu dáng của button (primary, secondary, danger, link).
     * @param  string  $size  Kích thước của button (sm, md, lg).
     * @param  null|string  $href  Nếu có, button sẽ trở thành thẻ <a>.
     */
    public function __construct(
        public string $variant = 'primary',
        public string $size = 'md',
        public ?string $href = null
    ) {
        $this->appliedClasses = $this->buildClasses();
    }

    /**
     * Xây dựng chuỗi class CSS dựa trên các thuộc tính.
     */
    protected function buildClasses(): string
    {
        // Kết hợp các class lại với nhau
        return implode(' ', [
            $this->baseClasses,
            $this->variantClasses[$this->variant] ?? $this->variantClasses['primary'],
            $this->sizeClasses[$this->size] ?? $this->sizeClasses['md'],
        ]);
    }

    /**
     * Lấy view / nội dung để render component.
     */
    public function render(): View
    {
        return view('components.ui.button');
    }
}
