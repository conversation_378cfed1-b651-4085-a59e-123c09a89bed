<?php

namespace App\View\Components\Ui;

use App\Models\Template;
use Illuminate\Contracts\View\View;
use Illuminate\View\Component;

class FavoriteButton extends Component
{
    /**
     * The size of the button.
     */
    public string $size;

    /**
     * The position of the button (for absolute/relative positioning).
     */
    public string $position;

    /**
     * Whether to show tooltip.
     */
    public bool $showTooltip;

    /**
     * The variant of the button.
     */
    public string $variant;

    /**
     * Size classes for the button.
     *
     * @var array<string, array>
     */
    public array $sizeClasses = [
        'sm' => [
            'button' => 'w-8 h-8 p-1.5',
            'icon' => 'w-4 h-4',
        ],
        'md' => [
            'button' => 'w-10 h-10 p-2',
            'icon' => 'w-5 h-5',
        ],
        'lg' => [
            'button' => 'w-12 h-12 p-2.5',
            'icon' => 'w-6 h-6',
        ],
        'xl' => [
            'button' => 'w-14 h-14 p-3',
            'icon' => 'w-8 h-8',
        ],
    ];

    /**
     * Position classes for the button.
     *
     * @var array<string, string>
     */
    public array $positionClasses = [
        'relative' => 'relative',
        'absolute' => 'absolute top-4 left-4 z-10',
        'inline' => 'inline-block',
    ];

    /**
     * Variant classes for the button.
     *
     * @var array<string, string>
     */
    public array $variantClasses = [
        'default' => 'bg-white/90 backdrop-blur-sm shadow-md hover:bg-white',
        'transparent' => 'bg-transparent hover:bg-white/10',
        'solid' => 'bg-white shadow-lg hover:shadow-xl',
        'minimal' => 'hover:bg-gray-100',
    ];

    /**
     * The final CSS classes to be applied.
     */
    public string $buttonClasses;

    public string $iconClasses;

    public string $containerClasses;

    public bool $isLoggedIn;

    public string $tooltipText;

    /**
     * Create a new component instance.
     */
    public function __construct(
        public Template $template,
        string $size = 'md',
        string $position = 'absolute',
        bool $showTooltip = true,
        string $variant = 'default',
        public string $baseClasses = 'group/fav flex items-center justify-center rounded-full transition-colors duration-200',
        public bool $isHasButtonClasses = true,
        public bool $isFavorited = false,
    ) {
        $this->size = $size;
        $this->position = $position;
        $this->showTooltip = $showTooltip;
        $this->variant = $variant;
        $this->isLoggedIn = auth('customer_user')->check();
        $this->tooltipText =
            $this->isLoggedIn ? __('messages.template.add_to_favorites') : __('messages.auth.login_to_favorite');

        $this->buildClasses();
        $this->isFavorited = $this->template->isFavoritedBy(auth('customer_user')->user());
    }

    /**
     * Build the CSS classes based on the component's properties.
     */
    protected function buildClasses(): void
    {
        $this->containerClasses = $this->positionClasses[$this->position] ?? $this->positionClasses['absolute'];

        if (! $this->isHasButtonClasses) {
            $this->buttonClasses = '';
        } else {
            $this->buttonClasses = implode(' ', [
                $this->baseClasses,
                $this->sizeClasses[$this->size]['button'] ?? $this->sizeClasses['md']['button'],
                $this->variantClasses[$this->variant] ?? $this->variantClasses['default'],
            ]);
        }

        $this->iconClasses = $this->sizeClasses[$this->size]['icon'] ?? $this->sizeClasses['md']['icon'];
    }

    /**
     * Get the tooltip text based on the favorite status.
     */
    public function getTooltipText(): string
    {
        if (! $this->isLoggedIn) {
            return __('messages.auth.login_to_favorite');
        }

        return $this->isFavorited
            ? __('messages.template.remove_from_favorites')
            : __('messages.template.add_to_favorites');
    }

    /**
     * Get the view / contents that represent the component.
     *
     * @return \Closure|\Illuminate\Contracts\View\View|string
     */
    public function render(): View
    {
        return view('components.ui.favorite-button');
    }
}
