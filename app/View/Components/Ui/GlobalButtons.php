<?php

namespace App\View\Components\Ui;

use App\Settings\SiteSetting;
use Illuminate\View\Component;

class GlobalButtons extends Component
{
    //    public string $zaloUrl;

    //    public string $messengerUrl;

    public function __construct()
    {
        /*$settings = app(SiteSetting::class);

        $this->zaloUrl = $settings->zalo_url;
        $this->messengerUrl = $settings->messenger_url;*/
    }

    /**
     * Get the view / contents that represent the component.
     */
    public function render(): \Illuminate\Contracts\View\View
    {
        return view('components.ui.global-buttons');
    }
}
