<?php

namespace Database\Factories;

use App\Models\BusinessVerification;
use App\Models\Customer;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Carbon;

class BusinessVerificationFactory extends Factory
{
    protected $model = BusinessVerification::class;

    public function definition(): array
    {
        return [
            'status' => $this->faker->word(),
            'segment' => $this->faker->word(),
            'erc_file_path' => $this->faker->word(),
            'erc_file_name' => $this->faker->name(),
            'erc_file_hash' => $this->faker->word(),
            'erc_file_size' => $this->faker->randomNumber(),
            'erc_file_mime_type' => $this->faker->word(),
            'erc_info' => $this->faker->words(),
            'submitted_at' => Carbon::now(),
            'reviewed_at' => Carbon::now(),
            'verified_at' => Carbon::now(),
            'rejected_at' => Carbon::now(),
            'rejection_reason' => $this->faker->word(),
            'admin_notes' => $this->faker->word(),
            'reminder_30m_sent_at' => Carbon::now(),
            'reminder_segment_sent_at' => Carbon::now(),
            'reminder_count' => $this->faker->randomNumber(),
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now(),

            'customer_id' => Customer::factory(),
            'reviewed_by' => User::factory(),
        ];
    }
}
