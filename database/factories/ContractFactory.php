<?php

namespace Database\Factories;

use App\Models\Contract;
use App\Models\Customer;
use App\Models\Opportunity;
use App\Models\Quote;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Carbon;

class ContractFactory extends Factory
{
    protected $model = Contract::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $startDate = $this->faker->dateTimeBetween('now', '+1 month');

        return [
            'contract_number' => 'CON-'.strtoupper(uniqid()),
            'title' => $this->faker->sentence(),
            'description' => $this->faker->paragraph(),
            'amount' => $this->faker->randomFloat(2, 1000, 100000),
            'start_date' => $startDate,
            'end_date' => $this->faker->dateTimeBetween($startDate, '+1 year'),
            'status' => $this->faker->randomElement(['draft', 'pending', 'active', 'completed', 'cancelled']),
            'customer_id' => Customer::factory(),
            'opportunity_id' => Opportunity::factory(),
            'quote_id' => Quote::factory(),
            'created_by' => User::factory(),
            'signed_at' => $this->faker->optional()->dateTimeBetween('-1 month', 'now'),
            'payment_terms' => $this->faker->paragraph(),
            'terms_and_conditions' => $this->faker->paragraphs(3, true),
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now(),
        ];
    }

    public function draft(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'draft',
            'signed_at' => null,
        ]);
    }

    public function active(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'active',
            'signed_at' => Carbon::now(),
        ]);
    }

    public function completed(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'completed',
            'signed_at' => Carbon::now()->subMonths(6),
            'end_date' => Carbon::now()->subDay(),
        ]);
    }

    public function cancelled(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'cancelled',
        ]);
    }

    public function withoutOpportunity(): static
    {
        return $this->state(fn (array $attributes) => [
            'opportunity_id' => null,
        ]);
    }

    public function withoutQuote(): static
    {
        return $this->state(fn (array $attributes) => [
            'quote_id' => null,
        ]);
    }
}
