<?php

namespace Database\Factories;

use App\Enums\CustomerUserStatusEnum;
use App\Models\Customer;
use App\Models\CustomerUser;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Carbon;

class CustomerUserFactory extends Factory
{
    protected $model = CustomerUser::class;

    public function definition(): array
    {
        return [
            'name' => $this->faker->name(),
            'email' => $this->faker->unique()->safeEmail(),
            'password' => bcrypt('password'),
            'is_owner' => $this->faker->boolean(),
            'email_verified_at' => Carbon::now(),
            'status' => $this->faker->randomElement(CustomerUserStatusEnum::cases()),
            'remember_token' => null,
            'customer_id' => Customer::factory(),
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now(),
        ];
    }

    public function unverified(): static
    {
        return $this->state(fn (array $attributes) => [
            'email_verified_at' => null,
        ]);
    }

    public function owner(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_owner' => true,
        ]);
    }

    public function active(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => CustomerUserStatusEnum::ACTIVE,
        ]);
    }
}
