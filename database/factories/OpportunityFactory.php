<?php

namespace Database\Factories;

use App\Models\Customer;
use App\Models\Opportunity;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Carbon;

class OpportunityFactory extends Factory
{
    protected $model = Opportunity::class;

    public function definition(): array
    {
        return [
            'title' => $this->faker->sentence(),
            'description' => $this->faker->paragraph(),
            'amount' => $this->faker->randomFloat(2, 1000, 100000),
            'expected_close_date' => $this->faker->dateTimeBetween('now', '+6 months'),
            'stage' => $this->faker->randomElement(['prospecting', 'qualification', 'needs_analysis', 'proposal', 'negotiation', 'closed_won', 'closed_lost']),
            'probability' => $this->faker->numberBetween(0, 100).'%',
            'customer_id' => Customer::factory(),
            'staff_user_id' => User::factory(),
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now(),
        ];
    }

    public function wonDeal(): static
    {
        return $this->state(fn (array $attributes) => [
            'stage' => 'closed_won',
            'probability' => '100%',
        ]);
    }

    public function lostDeal(): static
    {
        return $this->state(fn (array $attributes) => [
            'stage' => 'closed_lost',
            'probability' => '0%',
        ]);
    }

    public function inProgress(): static
    {
        return $this->state(fn (array $attributes) => [
            'stage' => $this->faker->randomElement(['qualification', 'needs_analysis', 'proposal', 'negotiation']),
            'probability' => $this->faker->numberBetween(20, 80).'%',
        ]);
    }
}
