<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('business_verifications', function (Blueprint $table) {
            $table->id();
            $table->foreignId('customer_id')->constrained('customers')->onDelete('cascade');
            $table->enum('status', [
                'unsubmitted',
                'submitted', 
                'in_review',
                'verified',
                'rejected'
            ])->default('unsubmitted');
            $table->enum('segment', [
                'default',
                'demo', 
                'purchased'
            ])->default('default');
            
            // ERC file information
            $table->string('erc_file_path')->nullable();
            $table->string('erc_file_name')->nullable();
            $table->string('erc_file_hash')->nullable();
            $table->unsignedBigInteger('erc_file_size')->nullable();
            $table->string('erc_file_mime_type')->nullable();
            
            // ERC document information (extracted from file)
            $table->json('erc_info')->nullable()->comment('JSON containing business name, tax code, address, etc.');
            
            // Timestamps for different stages
            $table->timestamp('submitted_at')->nullable();
            $table->timestamp('reviewed_at')->nullable();
            $table->timestamp('verified_at')->nullable();
            $table->timestamp('rejected_at')->nullable();
            
            // Review information
            $table->foreignId('reviewed_by')->nullable()->constrained('users')->nullOnDelete();
            $table->text('rejection_reason')->nullable();
            $table->text('admin_notes')->nullable();
            
            // Reminder tracking
            $table->timestamp('reminder_30m_sent_at')->nullable();
            $table->timestamp('reminder_segment_sent_at')->nullable();
            $table->integer('reminder_count')->default(0);
            
            $table->timestamps();
            
            // Indexes
            $table->index(['customer_id', 'status']);
            $table->index(['status', 'created_at']);
            $table->index(['segment', 'status']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('business_verifications');
    }
};
