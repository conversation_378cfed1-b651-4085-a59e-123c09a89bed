<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('contracts', function (Blueprint $table) {
            $table->id();
            $table->string('contract_number')->unique();
            $table->string('title');
            $table->text('description')->nullable();
            $table->decimal('amount', 12, 2);
            $table->date('start_date');
            $table->date('end_date');
            $table->string('status')->default('draft');
            $table->foreignId('customer_id')->constrained('customers');
            $table->foreignId('opportunity_id')->nullable()->constrained('opportunities');
            $table->foreignId('quote_id')->nullable()->constrained('quotes');
            $table->foreignId('created_by')->constrained('users');
            $table->timestamp('signed_at')->nullable();
            $table->text('payment_terms')->nullable();
            $table->text('terms_and_conditions')->nullable();
            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('contracts');
    }
};
