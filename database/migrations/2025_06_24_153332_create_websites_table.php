<?php

use App\Models\Website;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('websites', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id');
            $table->string('name');
            $table->string('phone');
            $table->string('company');
            $table->string('domain');
            $table->string('package')->comment('Gói đang sử dụng, vd: express web basic, express web plus, pro web....');
            $table->string('purpose')->nullable();
            $table->text('detail')->nullable();
            $table->string('status')->default(Website::STATUS_PENDING);
            $table->foreignId('template_id')->nullable();
            $table->string('template_name')->nullable();
            $table->string('note', 1000)->nullable();
            $table->string('admin_username')->nullable();
            $table->string('admin_password')->nullable();
            $table->string('customer_username')->nullable();
            $table->string('customer_password')->nullable();
            $table->text('deploy_logs')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('websites');
    }
};
