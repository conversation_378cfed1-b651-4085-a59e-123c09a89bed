<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('posts', function (Blueprint $table) {
            if (Schema::hasColumn('posts', 'meta')
                && ! Schema::getColumnType('posts', 'meta') == 'json') {
                $table->json('meta')->change();
            }
            if (Schema::hasColumn('posts', 'tags')
                && ! Schema::getColumnType('posts', 'tags') == 'json') {
                $table->json('tags')->change();
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        //
    }
};
