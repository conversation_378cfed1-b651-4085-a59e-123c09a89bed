<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('customers', function (Blueprint $table) {
            // Make email required and unique for authentication
            $table->string('email')->nullable(false)->unique()->change();

            // Add password field
            $table->string('password');

            // Add remember token
            $table->rememberToken();

            // Add email verification timestamp
            $table->timestamp('email_verified_at')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('customers', function (Blueprint $table) {
            // Revert email to nullable and remove unique constraint
            $table->string('email')->nullable()->change();

            // Drop added columns
            $table->dropColumn(['password', 'remember_token', 'email_verified_at']);
        });
    }
};
