<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * This migration adds a unique constraint to ensure only one owner per customer.
     * It also adds an index for better query performance when filtering by is_owner.
     */
    public function up(): void
    {
        Schema::table('customer_users', function (Blueprint $table) {
            // Add index for better performance when querying by is_owner
            $table->index('is_owner');

            // Add unique constraint to ensure only one owner per customer
            // This uses a partial unique index that only applies when is_owner = true
            $table->unique(['customer_id', 'is_owner'], 'unique_customer_owner')
                ->where('is_owner', true);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('customer_users', function (Blueprint $table) {
            $table->dropIndex(['is_owner']);
            $table->dropUnique('unique_customer_owner');
        });
    }
};
