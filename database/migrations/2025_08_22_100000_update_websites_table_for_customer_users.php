<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * This migration updates the websites table to use customer_user_id instead of user_id
     * to properly reference the customer_users table.
     */
    public function up(): void
    {
        Schema::table('websites', function (Blueprint $table) {
            // Add new customer_user_id column
            $table->foreignId('customer_user_id')->nullable()->after('user_id');

            // Add foreign key constraint
            $table->foreign('customer_user_id')->references('id')->on('customer_users')->onDelete('cascade');
        });

        // Migrate existing data if any exists
        // Note: This assumes that existing user_id values correspond to customer_user IDs
        // You may need to adjust this logic based on your actual data
        DB::statement('UPDATE websites SET customer_user_id = user_id WHERE user_id IS NOT NULL');

        Schema::table('websites', function (Blueprint $table) {
            // Drop the old user_id column
            $table->dropColumn('user_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('websites', function (Blueprint $table) {
            // Add back user_id column
            $table->foreignId('user_id')->nullable()->after('id');
        });

        // Migrate data back
        DB::statement('UPDATE websites SET user_id = customer_user_id WHERE customer_user_id IS NOT NULL');

        Schema::table('websites', function (Blueprint $table) {
            // Drop customer_user_id column and its foreign key
            $table->dropForeign(['customer_user_id']);
            $table->dropColumn('customer_user_id');
        });
    }
};
