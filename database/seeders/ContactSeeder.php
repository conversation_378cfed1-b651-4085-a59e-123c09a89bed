<?php

namespace Database\Seeders;

use App\Models\Contact;
use App\Models\Customer;
use Illuminate\Database\Seeder;

class ContactSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Only create contacts if there are customers in the database
        if (Customer::count() === 0) {
            $this->command->info('No customers found. Please seed customers first.');

            return;
        }

        $customers = Customer::all();

        foreach ($customers as $customer) {
            // Create 1-3 contacts for each customer
            $contactCount = rand(1, 3);

            for ($i = 0; $i < $contactCount; $i++) {
                $isPrimary = $i === 0; // First contact is primary

                Contact::create([
                    'customer_id' => $customer->id,
                    'name' => $this->generateRandomName(),
                    'email' => strtolower(str_replace(' ', '.', $this->generateRandomName())).'@example.com',
                    'phone' => '0'.rand(100000000, 999999999),
                    'position' => $this->getRandomPosition(),
                    'department' => $this->getRandomDepartment(),
                    'is_primary' => $isPrimary,
                    'notes' => $isPrimary ? 'Primary contact person' : null,
                ]);
            }
        }
    }

    /**
     * Generate a random Vietnamese name
     */
    private function generateRandomName(): string
    {
        $firstNames = ['Nguyễn', 'Trần', 'Lê', 'Phạm', 'Hoàng', 'Huỳnh', 'Phan', 'Vũ', 'Võ', 'Đặng'];
        $middleNames = ['Văn', 'Thị', 'Ngọc', 'Minh', 'Thanh', 'Thị', 'Đức', 'Hồng', 'Kim', 'Mai'];
        $lastNames = ['Anh', 'Bảo', 'Cường', 'Dũng', 'Hà', 'Hải', 'Hùng', 'Huy', 'Khoa', 'Long', 'Minh', 'Nam', 'Phúc', 'Quân', 'Quang', 'Sơn', 'Thắng', 'Thành', 'Trung', 'Tú'];

        return $firstNames[array_rand($firstNames)].' '.
               $middleNames[array_rand($middleNames)].' '.
               $lastNames[array_rand($lastNames)];
    }

    /**
     * Get a random position
     */
    private function getRandomPosition(): string
    {
        $positions = [
            'Trưởng phòng',
            'Nhân viên kinh doanh',
            'Kế toán',
            'Giám đốc',
            'Phó giám đốc',
            'Trợ lý',
            'Chuyên viên',
            'Kỹ thuật viên',
        ];

        return $positions[array_rand($positions)];
    }

    /**
     * Get a random department
     */
    private function getRandomDepartment(): string
    {
        $departments = [
            'Kinh doanh',
            'Kế toán',
            'Nhân sự',
            'Kỹ thuật',
            'Marketing',
            'Chăm sóc khách hàng',
            'Kho',
        ];

        return $departments[array_rand($departments)];
    }
}
