<?php

namespace Database\Seeders;

use App\Models\Contract;
use App\Models\Customer;
use App\Models\User;
use Illuminate\Database\Seeder;

class ContractSeeder extends Seeder
{
    public function run(): void
    {
        $customers = Customer::pluck('id')->toArray();
        $users = User::pluck('id')->toArray();

        $statuses = ['draft', 'sent', 'approved', 'active', 'expired', 'cancelled'];
        $paymentTerms = ['Net 15', 'Net 30', 'Net 45', 'Due on receipt', '50% advance, 50% on completion'];

        for ($i = 0; $i < 20; $i++) {
            $startDate = now()->addDays(rand(-30, 30));

            Contract::create([
                'contract_number' => 'CNT-'.strtoupper(uniqid()),
                'title' => 'Service Contract '.($i + 1),
                'description' => 'This is a service contract for customer '.($i + 1),
                'amount' => rand(1000, 100000) / 100, // Random amount between 10.00 and 1000.00
                'start_date' => $startDate->format('Y-m-d'),
                'end_date' => $startDate->copy()->addMonths(rand(6, 36))->format('Y-m-d'),
                'status' => $statuses[array_rand($statuses)],
                'customer_id' => $customers[array_rand($customers)],
                'opportunity_id' => null, // Can be set if needed
                'quote_id' => null, // Can be set if needed
                'created_by' => $users[array_rand($users)],
                'signed_at' => rand(0, 1) ? now()->subDays(rand(1, 60))->toDateTimeString() : null,
                'payment_terms' => $paymentTerms[array_rand($paymentTerms)],
                'terms_and_conditions' => 'Standard terms and conditions apply. Payment is due according to the payment terms specified above.',
                'created_at' => now()->subDays(rand(1, 90)),
                'updated_at' => now()->subDays(rand(0, 30)),
            ]);
        }
    }
}
