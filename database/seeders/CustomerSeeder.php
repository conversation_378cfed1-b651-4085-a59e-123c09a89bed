<?php

namespace Database\Seeders;

use App\Models\Customer;
use Illuminate\Database\Seeder;

class CustomerSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $customers = [
            [
                'business_name' => 'Công ty TNHH ABC',
                'tax_code' => '0101234567',
                'contact_person' => 'Nguyễn Văn A',
                'phone' => '0912345678',
                'email' => '<EMAIL>',
                'address' => '123 Đường Lê Lợi, Quận 1',
                'city' => 'Hồ Chí Minh',
                'country' => 'Vietnam',
                'website' => 'https://abc.com.vn',
                'status' => 'active',
                'assigned_to' => 1, // Assuming user with ID 1 exists
            ],
            [
                'business_name' => 'Công ty Cổ phần XYZ',
                'tax_code' => '0107654321',
                'contact_person' => 'Trần Thị B',
                'phone' => '0987654321',
                'email' => '<EMAIL>',
                'address' => '456 Đường Trần <PERSON> Đạ<PERSON>, Quận 5',
                'city' => 'H<PERSON> Ch<PERSON>',
                'country' => 'Vietnam',
                'website' => 'https://xyz.com.vn',
                'status' => 'lead',
                'assigned_to' => 1,
            ],
        ];

        foreach ($customers as $customer) {
            Customer::create($customer);
        }
    }
}
