<?php

namespace Database\Seeders;

use App\Models\Role;
use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        if (app()->isProduction()) {
            $this->productionSeeder();
        } else {
            $this->devSeeder();
        }
    }

    private function productionSeeder(): void
    {
        $this->call([
            Production\TemplateSeeder::class,
            RolePermissionSeeder::class,
            PostCategorySeeder::class,
        ]);

        /** @var User $user */
        $user = User::create([
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'password' => Hash::make('HackCMN@2025'),
            'email_verified_at' => now(),
        ]);
        $user->assignRole(Role::SUPER_ADMIN);
    }

    private function devSeeder(): void
    {
        // Create a test user
        /** @var User $user */
        $user = User::factory()->create([
            'name' => 'Test User',
            'email' => '<EMAIL>',
        ]);

        // Seed template categories and templates
        $this->call([
            Production\TemplateSeeder::class,
            CustomerSeeder::class,
            ContactSeeder::class,
            OpportunitySeeder::class,
            QuoteSeeder::class,
            PostCategorySeeder::class,
            PostSeeder::class,
            ContractSeeder::class,
            PageSeeder::class,
            RolePermissionSeeder::class,
        ]);

        // Assign role
        $user->assignRole(Role::SUPER_ADMIN);
    }
}
