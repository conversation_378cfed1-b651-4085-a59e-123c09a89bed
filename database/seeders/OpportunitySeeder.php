<?php

namespace Database\Seeders;

use App\Models\Customer;
use App\Models\Opportunity;
use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Carbon;

class OpportunitySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Only seed if no opportunities exist
        if (Opportunity::count() > 0) {
            return;
        }

        /** @var Customer $customer */
        $customer = Customer::first();

        /** @var User $staff */
        $staff = User::first();

        if (! $customer || ! $staff) {
            $this->command->warn('Please make sure there is at least one customer and user in the database.');

            return;
        }

        $opportunities = [
            [
                'title' => 'Enterprise Software License',
                'description' => 'Potential large enterprise customer interested in our premium software package.',
                'amount' => 50000.00,
                'expected_close_date' => Carbon::now()->addDays(30),
                'stage' => 'prospecting',
                'probability' => '30%',
                'customer_id' => $customer->id,
                'staff_user_id' => $staff->id,
            ],
            [
                'title' => 'Annual Support Contract',
                'description' => 'Renewal of annual support contract with additional services.',
                'amount' => 25000.00,
                'expected_close_date' => Carbon::now()->addDays(15),
                'stage' => 'proposal',
                'probability' => '60%',
                'customer_id' => $customer->id,
                'staff_user_id' => $staff->id,
            ],
            [
                'title' => 'Custom Development Project',
                'description' => 'Custom module development for specific business needs.',
                'amount' => 75000.00,
                'expected_close_date' => Carbon::now()->addDays(45),
                'stage' => 'negotiation',
                'probability' => '75%',
                'customer_id' => $customer->id,
                'staff_user_id' => $staff->id,
            ],
        ];

        foreach ($opportunities as $opportunity) {
            Opportunity::create($opportunity);
        }
    }
}
