<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

class PostCategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $categories = [
            [
                'name' => 'Thiết kế website',
                'slug' => 'thiet-ke-website',
                'description' => 'Các bài viết về thiết kế website, xu hướng mới nhất và các mẹo hữu ích',
                'icon' => 'ri-global-line',
                'is_active' => true,
                'order_column' => 1,
            ],
            [
                'name' => 'SEO & Marketing',
                'slug' => 'seo-marketing',
                'description' => '<PERSON><PERSON><PERSON> lược SEO, tiếp thị số và tối ưu hóa công cụ tìm kiếm',
                'icon' => 'ri-line-chart-line',
                'is_active' => true,
                'order_column' => 3,
            ],
            [
                'name' => 'Lập trình phần mềm',
                'slug' => 'lap-trinh-phan-mem',
                'description' => '<PERSON><PERSON><PERSON> thức lập trình phần mềm, framework và công nghệ mới nhất',
                'icon' => 'ri-code-s-slash-line',
                'is_active' => true,
                'order_column' => 4,
            ],
        ];

        foreach ($categories as $category) {
            \App\Models\PostCategory::create($category);
        }
    }
}
