<?php

namespace Database\Seeders;

use App\Models\PostCategory;
use App\Models\User;
use Illuminate\Database\Seeder;

class PostSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Make sure we have at least one user
        /** @var null|User $user */
        $user = User::first();
        if (! $user) {
            $user = User::factory()->create([
                'name' => 'Admin',
                'email' => '<EMAIL>',
                'password' => bcrypt('password'),
            ]);
        }

        // Make sure we have categories
        if (PostCategory::count() === 0) {
            $this->call(PostCategorySeeder::class);
        }

        $faker = \Faker\Factory::create('vi_VN');
        $categories = PostCategory::all();
        $tags = ['tư vấn', 'hướng dẫn', 'mẹo vặt', 'tài liệu', 'thông tin'];

        // Create featured posts
        for ($i = 0; $i < 3; $i++) {
            // Get a random category
            $categoryId = $categories->random()->id ?? 1; // Default to category 1 if none exist

            \App\Models\Post::create([
                'title' => $faker->sentence(6),
                'slug' => $faker->slug(),
                'summary' => $faker->sentence(15),
                'content' => $this->generateContent($faker),
                'featured_image' => 'https://picsum.photos/800/500?random='.$i,
                'gallery' => [
                    'https://picsum.photos/800/500?gallery=1_'.$i,
                    'https://picsum.photos/800/500?gallery=2_'.$i,
                ],
                'category_id' => $categoryId,
                'author_id' => $user->id ?? 1, // Default to user 1 if none exist
                'published_at' => now()->subDays(rand(0, 30)),
                'is_featured' => true,
                'is_published' => true,
                'meta' => [
                    'meta_title' => $faker->sentence(6),
                    'meta_description' => $faker->sentence(15),
                    'meta_keywords' => implode(', ', (array) $faker->words(5)),
                ],
                'view_count' => rand(100, 1000),
                'tags' => $tags,
            ]);
        }

        // Create regular posts
        for ($i = 0; $i < 100; $i++) {
            // Get a random category ID from the collection
            $categoryId = $categories->random()->id ?? 1; // Default to category 1 if none exist
            $isPublished = $faker->boolean(80); // 80% chance of being published

            \App\Models\Post::create([
                'title' => $faker->sentence(6),
                'slug' => $faker->slug(),
                'summary' => $faker->sentence(15),
                'content' => $this->generateContent($faker),
                'featured_image' => 'https://picsum.photos/800/500?post='.$i,
                'category_id' => $categoryId,
                'author_id' => $user->id ?? 1, // Default to user 1 if none exist
                'published_at' => $isPublished ? now()->subDays(rand(0, 90)) : null,
                'is_featured' => $faker->boolean(20), // 20% chance of being featured
                'is_published' => $isPublished,
                'meta' => [
                    'meta_title' => $faker->sentence(6),
                    'meta_description' => $faker->sentence(15),
                    'meta_keywords' => implode(', ', (array) $faker->words(5)),
                ],
                'view_count' => rand(0, 500),
                'tags' => $tags,
            ]);
        }
    }

    /**
     * Generate fake content with multiple paragraphs and formatting
     */
    private function generateContent(\Faker\Generator $faker): string
    {
        $content = '<p>'.$faker->paragraph(10).'</p>';

        // Add some headings
        $content .= '<h2>'.$faker->sentence(4).'</h2>';
        $content .= '<p>'.$faker->paragraph(8).'</p>';

        // Add a list
        $content .= '<h3>'.$faker->sentence(3).'</h3>';
        $content .= '<ul>';
        for ($i = 0; $i < 5; $i++) {
            $content .= '<li>'.$faker->sentence(6).'</li>';
        }
        $content .= '</ul>';

        // Add more content
        $content .= '<p>'.$faker->paragraph(12).'</p>';

        // Add a blockquote
        $content .= '<blockquote><p>'.$faker->sentence(10).'</p></blockquote>';

        // Add final paragraph
        $content .= '<p>'.$faker->paragraph(6).'</p>';

        return $content;
    }
}
