<?php

namespace Database\Seeders;

use App\Models\Opportunity;
use App\Models\Quote;
use App\Models\User;
use Illuminate\Database\Seeder;

class QuoteSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get the first user and opportunity to associate with the quote
        $user = User::first();
        /** @var Opportunity $opportunity */
        $opportunity = Opportunity::first();

        if (! $user || ! $opportunity) {
            $this->command->info('Please seed users and opportunities first!');

            return;
        }

        $quotes = [
            [
                'title' => 'Website Development Proposal',
                'description' => 'Complete website development with 10 pages and CMS integration',
                'amount' => 5000.00,
                'valid_until' => now()->addDays(30),
                'status' => 'draft',
                'opportunity_id' => $opportunity->getKey(),
                'created_by' => $user->getKey(),
            ],
            [
                'title' => 'Mobile App Development',
                'description' => 'Cross-platform mobile application development',
                'amount' => 10000.00,
                'valid_until' => now()->addDays(45),
                'status' => 'sent',
                'opportunity_id' => $opportunity->getKey(),
                'created_by' => $user->getKey(),
            ],
        ];

        foreach ($quotes as $quoteData) {
            Quote::create($quoteData);
        }
    }
}
