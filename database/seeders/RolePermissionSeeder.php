<?php

namespace Database\Seeders;

use App\Models\Permission;
use App\Models\Role;
use Illuminate\Database\Seeder;

class RolePermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Step 1: Create roles
        foreach ($this->getRoles() as $role) {
            Role::create(['name' => $role]);
        }

        // Step 2: Create permissions
        foreach ($this->getPermissions() as $permission) {
            Permission::create(['name' => $permission]);
        }

        // Step 3: Assign permissions to roles

    }

    /**
     * Get the list of roles to be created.
     *
     * @return array<string>
     */
    private function getRoles(): array
    {
        return [
            /** System roles */
            Role::SUPER_ADMIN,
            Role::ADMIN,
            Role::STAFF,

            /** User roles */
            Role::CUSTOMER,
        ];
    }

    /**
     * Get the list of permissions to be created.
     *
     * @return array<string>
     */
    private function getPermissions(): array
    {
        return [
            /** Template permissions */
            Permission::TEMPLATE_ACCESS,
            Permission::TEMPLATE_CREATE,
            Permission::TEMPLATE_UPDATE,
            Permission::TEMPLATE_DELETE,
        ];
    }
}
