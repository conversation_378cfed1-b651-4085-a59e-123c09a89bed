<?php

namespace Database\Seeders;

use App\Models\Template;
use App\Models\TemplateCategory;
use Faker\Factory as Faker;
use Illuminate\Database\Seeder;
use Illuminate\Support\Str;

class TemplateSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $faker = Faker::create('vi_VN');
        $categories = [
            [
                'name' => 'Kinh doanh',
                'slug' => 'kinh-doanh',
                'description' => 'Mẫu dành cho doanh nghiệp',
                'templates' => [
                    ['Mẫu báo cáo doanh thu', '<PERSON><PERSON> hoạch kinh doanh', '<PERSON><PERSON> sơ công ty', '<PERSON><PERSON><PERSON> cáo tài chính', '<PERSON><PERSON> xuất dự án', '<PERSON>ến lược tiếp thị', '<PERSON><PERSON><PERSON> cáo thị trường', '<PERSON><PERSON><PERSON> giá hiệu suất', '<PERSON><PERSON> hoạch mở rộng', '<PERSON><PERSON> tích đối thủ'],
                ],
            ],
            [
                'name' => 'Tiế<PERSON> thị',
                'slug' => 'tiep-thi',
                'description' => 'Mẫu tiếp thị và quảng cáo',
                'templates' => [
                    ['Kế hoạch truyền thông', 'Chiến dịch email', 'Báo cáo SEO', 'Phân tích mạng xã hội', 'Kịch bản quảng cáo', 'Kế hoạch nội dung', 'Báo cáo hiệu suất', 'Chiến lược thương hiệu', 'Kế hoạch khuyến mãi', 'Phân tích đối thủ'],
                ],
            ],
            [
                'name' => 'Giáo dục',
                'slug' => 'giao-duc',
                'description' => 'Mẫu giáo dục và học thuật',
                'templates' => [
                    ['Giáo án giảng dạy', 'Bài kiểm tra', 'Đề cương môn học', 'Báo cáo nghiên cứu', 'Kế hoạch bài học', 'Đánh giá học sinh', 'Kế hoạch giảng dạy', 'Bài thuyết trình', 'Đề tài luận văn', 'Báo cáo thực tập'],
                ],
            ],
            [
                'name' => 'Sáng tạo',
                'slug' => 'sang-tao',
                'description' => 'Mẫu sáng tạo và nghệ thuật',
                'templates' => [
                    ['Portfolio', 'Mẫu danh thiếp', 'Tạp chí điện tử', 'Lịch sáng tạo', 'Mẫu CV sáng tạo', 'Trình chiếu nghệ thuật', 'Mẫu áp phích', 'Thiệp mời', 'Mẫu lịch', 'Bộ nhận diện thương hiệu'],
                ],
            ],
            [
                'name' => 'Công nghệ',
                'slug' => 'cong-nghe',
                'description' => 'Mẫu liên quan đến công nghệ',
                'templates' => [
                    ['Tài liệu API', 'Hướng dẫn sử dụng', 'Báo cáo lỗi', 'Tài liệu kỹ thuật', 'Kế hoạch phát triển', 'Đặc tả yêu cầu', 'Kiến trúc hệ thống', 'Tài liệu đào tạo', 'Bảo mật thông tin', 'Quy trình kiểm thử'],
                ],
            ],
            [
                'name' => 'Tài chính',
                'slug' => 'tai-chinh',
                'description' => 'Mẫu tài chính và kế toán',
                'templates' => [
                    ['Báo cáo tài chính', 'Ngân sách hàng tháng', 'Báo cáo thuế', 'Theo dõi chi phí', 'Báo cáo lưu chuyển tiền tệ', 'Phân tích đầu tư', 'Báo cáo doanh thu', 'Dự báo tài chính', 'Báo cáo kế toán', 'Phân tích lợi nhuận'],
                ],
            ],
            [
                'name' => 'Sức khỏe',
                'slug' => 'suc-khoe',
                'description' => 'Mẫu về sức khỏe và chăm sóc sức khỏe',
                'templates' => [
                    ['Theo dõi sức khỏe', 'Kế hoạch tập luyện', 'Nhật ký dinh dưỡng', 'Lịch khám bệnh', 'Hồ sơ bệnh án', 'Theo dõi thuốc', 'Kế hoạch chăm sóc', 'Đánh giá sức khỏe', 'Mục tiêu thể chất', 'Nhật ký giấc ngủ'],
                ],
            ],
            [
                'name' => 'Thương mại điện tử',
                'slug' => 'thuong-mai-dien-tu',
                'description' => 'Mẫu cửa hàng trực tuyến và sản phẩm',
                'templates' => [
                    ['Mẫu sản phẩm', 'Trang danh mục', 'Trang giỏ hàng', 'Trang thanh toán', 'Trang đơn hàng', 'Mẫu khuyến mãi', 'Đánh giá sản phẩm', 'Trang bán chạy', 'Sản phẩm mới', 'Khuyến mãi đặc biệt'],
                ],
            ],
        ];

        foreach ($categories as $categoryData) {
            /** @var TemplateCategory $category */
            $category = TemplateCategory::create([
                'name' => $categoryData['name'],
                'slug' => $categoryData['slug'],
                'description' => $categoryData['description'],
            ]);

            for ($i = 0; $i < 10; $i++) {
                $templateName = $faker->sentence;
                $originalPrice = rand(1000000, 5000000);
                $salePrice = rand(70, 90) / 100 * $originalPrice;
                $shouldHaveSale = rand(1, 10) <= 7;

                // Create or update the template
                Template::updateOrCreate(
                    ['slug' => Str::slug($templateName)],
                    [
                        'category_id' => $category->id,
                        'name' => $templateName,
                        'description' => $faker->paragraph(10),
                        /*'thumbnail' => 'https://picsum.photos/seed/'.Str::random(5).'/600/400',
                        'screenshots' => [
                            'https://picsum.photos/seed/'.Str::random(5).'/600/400',
                            'https://picsum.photos/seed/'.Str::random(5).'/600/400',
                            'https://picsum.photos/seed/'.Str::random(5).'/600/400',
                        ],*/
                        'features' => [
                            'Responsive Design',
                            'Tối ưu SEO',
                            'Tốc độ tải nhanh',
                            'Hỗ trợ đa ngôn ngữ',
                        ],
                        'is_featured' => rand(0, 1),
                        'view_count' => rand(100, 10000),
                        'original_price' => $originalPrice,
                        'sale_price' => $shouldHaveSale ? $salePrice : null,
                        'demo_url' => 'https://dotphonglong.com/tools/11-random.html',
                    ]
                );
            }
        }
    }
}
