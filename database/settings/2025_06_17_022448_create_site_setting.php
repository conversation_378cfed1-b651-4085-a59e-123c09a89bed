<?php

use App\Settings\SiteSetting;
use <PERSON><PERSON>\LaravelSettings\Migrations\SettingsBlueprint;
use <PERSON><PERSON>\LaravelSettings\Migrations\SettingsMigration;

return new class extends SettingsMigration
{
    public function up(): void
    {
        $this->migrator->inGroup(SiteSetting::group(), function (SettingsBlueprint $blueprint): void {
            $blueprint->add('email', '<EMAIL>');
            $blueprint->add('phone', '+84987356150');
            $blueprint->add('address', 'Tầng 5, CF Tower, 70 Phạm <PERSON>, Phư<PERSON>ng <PERSON>, Quận 3, Tp<PERSON> <PERSON>, Việt Nam');
            $blueprint->add('facebook', 'https://facebook.com/cslant.official');
            $blueprint->add('youtube', 'https://youtube.com/@cslant_official');
            $blueprint->add('twitter', 'https://twitter.com/cslantofficial');
        });
    }

    public function down(): void
    {
        $this->migrator->inGroup(SiteSetting::group(), function (SettingsBlueprint $blueprint): void {
            $blueprint->delete('email');
            $blueprint->delete('phone');
            $blueprint->delete('address');
            $blueprint->delete('facebook');
            $blueprint->delete('youtube');
            $blueprint->delete('twitter');
        });
    }
};
