<?php

use App\Settings\SiteSetting;
use <PERSON><PERSON>\LaravelSettings\Migrations\SettingsBlueprint;
use <PERSON><PERSON>\LaravelSettings\Migrations\SettingsMigration;

return new class extends SettingsMigration
{
    public function up(): void
    {
        $this->migrator->inGroup(SiteSetting::group(), function (SettingsBlueprint $blueprint): void {
            $blueprint->update('address', function () {
                return 'Tầng 5, CF Tower, 70 Phạm Ng<PERSON>c <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Tp<PERSON> <PERSON><PERSON>, Việt Nam';
            });
        });
    }

    public function down(): void
    {
        $this->migrator->inGroup(SiteSetting::group(), function (SettingsBlueprint $blueprint): void {
            $blueprint->update('address', function () {
                return 'Tầng 5, CF Tower, 70 Phạm Ngọc Thạch, Ph<PERSON><PERSON><PERSON>, Quận 3, <PERSON>p<PERSON> <PERSON><PERSON>, Việt Nam';
            });
        });
    }
};
