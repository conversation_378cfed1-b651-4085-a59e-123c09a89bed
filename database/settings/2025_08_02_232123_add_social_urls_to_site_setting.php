<?php

use App\Settings\SiteSetting;
use <PERSON><PERSON>\LaravelSettings\Migrations\SettingsBlueprint;
use Spatie\LaravelSettings\Migrations\SettingsMigration;

return new class extends SettingsMigration
{
    public function up(): void
    {
        $this->migrator->inGroup(SiteSetting::group(), function (SettingsBlueprint $blueprint): void {
            $blueprint->add('zalo_url', 'https://zalo.me/2494953315066015534');
            $blueprint->add('messenger_url', 'https://messenger.com/t/cslant.official');
        });
    }

    public function down(): void
    {
        $this->migrator->inGroup(SiteSetting::group(), function (SettingsBlueprint $blueprint): void {
            $blueprint->delete(name: 'zalo_url');
            $blueprint->delete(name: 'messenger_url');
        });
    }
};
