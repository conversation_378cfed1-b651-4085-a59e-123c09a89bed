<?php

use App\Settings\SiteSetting;
use Spa<PERSON>\LaravelSettings\Migrations\SettingsBlueprint;
use <PERSON>tie\LaravelSettings\Migrations\SettingsMigration;

return new class extends SettingsMigration
{
    public function up(): void
    {
        $this->migrator->inGroup(SiteSetting::group(), function (SettingsBlueprint $blueprint): void {
            $blueprint->add('linkedin', 'https://www.linkedin.com/company/cslant');
        });
    }

    public function down(): void
    {
        $this->migrator->inGroup(SiteSetting::group(), function (SettingsBlueprint $blueprint): void {
            $blueprint->delete(name: 'linkedin');
        });
    }
};
