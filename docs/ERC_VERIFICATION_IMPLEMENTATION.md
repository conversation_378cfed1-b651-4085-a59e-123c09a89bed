# Hệ thống <PERSON>ác minh Doanh nghiệp bằng ERC - Implementation Guide

## Tổng quan

Hệ thống xác minh doanh nghiệp bằng Gi<PERSON>y chứng nhận đăng ký doanh nghiệp (ERC) được thiết kế để tự động hóa quy trình xác minh và phân loại khách hàng doanh nghiệp.

## Kiến trúc hệ thống

### State Machine

```mermaid
stateDiagram-v2
    [*] --> UNSUBMITTED
    UNSUBMITTED --> SUBMITTED : Upload ERC
    SUBMITTED --> IN_REVIEW : Assign reviewer
    IN_REVIEW --> VERIFIED : Approve
    IN_REVIEW --> REJECTED : Reject (reason)
    REJECTED --> SUBMITTED : Resubmit
    SUBMITTED --> VERIFIED : Auto-approve (optional)
    VERIFIED --> [*]
```

### Email Flow Timeline

```mermaid
timeline
    title Email Reminder Flow
    
    Registration : Customer registers
                 : Initialize BusinessVerification
                 : Schedule reminder jobs
    
    +30 minutes : Email #1 (30-minute reminder)
                : "Hướng dẫn nộp ERC"
    
    +1-3 days   : Email #2 (Segment-based reminder)
                : PURCHASED: +1 day
                : DEMO: +2 days  
                : DEFAULT: +3 days
    
    Submit ERC  : Email #3 (Submitted confirmation)
                : "Đã nhận hồ sơ ERC"
                : Cancel pending reminders
    
    Admin Review : Email #4 (Verified) OR Email #R (Rejected)
                 : "Chúc mừng xác minh thành công"
                 : "Hồ sơ ERC cần bổ sung"
```

### Segment Classification

```mermaid
flowchart TD
    A[Customer Registration] --> B{Has Active Contracts?}
    B -->|Yes| C[PURCHASED Segment]
    B -->|No| D{Has Demo Websites?}
    D -->|Yes| E[DEMO Segment]
    D -->|No| F[DEFAULT Segment]
    
    C --> G[Reminder after 1 day]
    E --> H[Reminder after 2 days]
    F --> I[Reminder after 3 days]
```

## Các bước triển khai đã thực hiện

### 1. Database & Models

#### 1.1 Tạo Enums
- `app/Enums/BusinessVerificationStatusEnum.php`
- `app/Enums/BusinessVerificationSegmentEnum.php`

#### 1.2 Migration
- `database/migrations/2025_01_08_100000_create_business_verifications_table.php`

#### 1.3 Models
- `app/Models/BusinessVerification.php`
- Cập nhật `app/Models/Customer.php` (thêm relationship)

### 2. Jobs & Email System

#### 2.1 Jobs
- `app/Jobs/SendErcReminderAfter30MinutesJob.php`
- `app/Jobs/SendErcReminderBySegmentJob.php`
- `app/Jobs/SendErcSubmittedNotificationJob.php`
- `app/Jobs/SendErcVerifiedNotificationJob.php`
- `app/Jobs/SendErcRejectedNotificationJob.php`

#### 2.2 Mail Classes
- `app/Mail/ErcReminderAfter30MinutesMail.php`
- `app/Mail/ErcReminderBySegmentMail.php`
- `app/Mail/ErcSubmittedMail.php`
- `app/Mail/ErcVerifiedMail.php`
- `app/Mail/ErcRejectedMail.php`

#### 2.3 Email Templates
- `resources/views/emails/erc/reminder-30m.blade.php`
- `resources/views/emails/erc/reminder-segment.blade.php`
- `resources/views/emails/erc/submitted.blade.php`
- `resources/views/emails/erc/verified.blade.php`
- `resources/views/emails/erc/rejected.blade.php`

### 3. Business Logic

#### 3.1 Service Layer
- `app/Services/BusinessVerificationService.php`

#### 3.2 Controllers
- `app/Http/Controllers/BusinessVerificationController.php`

#### 3.3 Request Validation
- `app/Http/Requests/BusinessVerificationUploadRequest.php`

### 4. Admin Panel (Filament)

#### 4.1 Resource
- `app/Filament/Resources/BusinessVerificationResource.php`

#### 4.2 Pages
- `app/Filament/Resources/BusinessVerificationResource/Pages/ListBusinessVerifications.php`
- `app/Filament/Resources/BusinessVerificationResource/Pages/CreateBusinessVerification.php`
- `app/Filament/Resources/BusinessVerificationResource/Pages/ViewBusinessVerification.php`
- `app/Filament/Resources/BusinessVerificationResource/Pages/EditBusinessVerification.php`

### 5. Events & Listeners

#### 5.1 Events
- `app/Events/CustomerRegistered.php`

#### 5.2 Listeners
- `app/Listeners/InitializeBusinessVerification.php`

#### 5.3 Event Registration
- Cập nhật `app/Providers/EventServiceProvider.php`

### 6. Commands & Scheduling

#### 6.1 Commands
- `app/Console/Commands/ProcessErcRemindersCommand.php`

#### 6.2 Scheduling
- Cập nhật `routes/console.php`

### 7. Frontend

#### 7.1 Views
- `resources/views/business-verification/status.blade.php`
- `resources/views/business-verification/upload.blade.php`

#### 7.2 Routes
- Cập nhật `routes/web.php`
- Cập nhật `routes/api.php` (sẽ được sửa)

### 8. Configuration

#### 8.1 Filesystems
- Cập nhật `config/filesystems.php` (thêm private disk)

#### 8.2 Translations
- Cập nhật `lang/vi/enums.php`
- Cập nhật `lang/en/enums.php`
- Cập nhật `lang/vi/messages.php`
- Cập nhật `lang/vi/validation.php`

### 9. Integration Points

#### 9.1 Customer Registration
- Cập nhật `app/Http/Controllers/Auth/RegisterController.php`

## Luồng hoạt động chi tiết

### Customer Flow

```mermaid
sequenceDiagram
    participant C as Customer
    participant S as System
    participant Q as Queue
    participant E as Email
    participant A as Admin

    C->>S: Register Business Account
    S->>S: Create BusinessVerification (UNSUBMITTED)
    S->>Q: Schedule 30m reminder
    S->>Q: Schedule segment reminder (1-3 days)
    
    Note over Q,E: After 30 minutes
    Q->>E: Send Email #1 (30m reminder)
    
    Note over Q,E: After 1-3 days (based on segment)
    Q->>E: Send Email #2 (segment reminder)
    
    C->>S: Upload ERC file
    S->>S: Update status to SUBMITTED
    S->>Q: Cancel pending reminders
    S->>E: Send Email #3 (submitted)
    
    A->>S: Review ERC file
    alt Approved
        A->>S: Mark as VERIFIED
        S->>E: Send Email #4 (verified)
    else Rejected
        A->>S: Mark as REJECTED with reason
        S->>E: Send Email #R (rejected)
        S->>Q: Reschedule reminders
    end
```

### Admin Flow

```mermaid
flowchart TD
    A[Admin Login] --> B[View Pending Reviews]
    B --> C{Select Verification}
    C --> D[Download ERC File]
    D --> E[Review Document]
    E --> F{Decision}
    F -->|Approve| G[Mark as VERIFIED]
    F -->|Reject| H[Add Rejection Reason]
    G --> I[Send Success Email]
    H --> J[Send Rejection Email]
    I --> K[Update Dashboard]
    J --> L[Reschedule Reminders]
```

## API Endpoints

### Customer APIs
- `GET /api/business-verification/status` - Lấy trạng thái xác minh
- `POST /api/business-verification/upload` - Upload ERC file
- `DELETE /api/business-verification/file` - Xóa file đã upload

### Admin APIs (Filament)
- Tích hợp trong Filament Resource với các actions:
  - Approve verification
  - Reject verification
  - Download file
  - View statistics

## Cấu hình cần thiết

### Environment Variables
```env
QUEUE_CONNECTION=database
FILESYSTEM_DISK=local
MAIL_MAILER=smtp
```

### Cron Job
```bash
* * * * * cd /path-to-your-project && php artisan schedule:run >> /dev/null 2>&1
```

### Queue Worker
```bash
php artisan queue:work --queue=emails,default
```

## Testing

### Artisan Commands
```bash
# Process pending reminders manually
php artisan erc:process-reminders

# Check queue status
php artisan queue:monitor

# View failed jobs
php artisan queue:failed
```

### Database Seeding
```bash
# Run migrations
php artisan migrate

# Create storage link
php artisan storage:link
```

## Security Features

1. **File Validation**: Size, type, mime type checking
2. **Private Storage**: ERC files stored in private disk
3. **File Hashing**: SHA-256 hash for integrity
4. **Authentication**: All routes protected by auth middleware
5. **CSRF Protection**: All forms include CSRF tokens
6. **Rate Limiting**: Can be added to upload endpoints

## Monitoring & Analytics

### Statistics Available
- Total verifications by status
- Pending review count
- Reminder statistics
- Segment distribution
- Processing time metrics

### Logging
- All major actions logged with context
- Error tracking for failed jobs
- File upload/download audit trail

## Troubleshooting

### Common Issues
1. **Queue not processing**: Check queue worker is running
2. **Emails not sending**: Verify mail configuration
3. **File upload fails**: Check storage permissions
4. **Reminders not sent**: Verify cron job setup

### Debug Commands
```bash
# Check queue status
php artisan queue:monitor

# View logs
tail -f storage/logs/laravel.log

# Test email configuration
php artisan tinker
>>> Mail::raw('Test', function($msg) { $msg->to('<EMAIL>'); });
```
