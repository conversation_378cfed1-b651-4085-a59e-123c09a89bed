Flant - A combination of “Flex” + “CSlant” → represents flexibility

Welcome to the development documentation of this project!
This document provides an overview of the development environment, tools, and commands used in this project. It is designed to help developers set up their local environment and understand the development workflow.

## Installation

After cloning the repository, you need to install the dependencies and set up the environment.

### Update env

```bash
cp .env.example .env
```

### Composer install

```bash
composer install
php artisan key:generate
```

### Migrate database

```bash
php artisan migrate
```

For fresh database:

```bash
php artisan migrate:fresh
```

For fresh force database with seeders:

```bash
php artisan migrate:fresh --seed --force
```

### NPM install

```bash
npm install
```

## Development

Here are some commands that you can use during development.

### IDE Helper

If you want to use IDE Helper, you can use the following command in the root directory of this project:

```bash
php artisan ide-helper:generate
php artisan ide-helper:meta
php artisan ide-helper:models -W
php artisan ide-helper:eloquent
composer f
```

### Code analysis

#### Default

If you want to use PHPStan or LaraStan, you can use the following command in the root directory of this project:

```bash
vendor/bin/phpstan analyse
```

#### Customized

I configured LaraStan(PHPStan) to use level 9, and I customized new commands to use PHPStan with Laravel.

```bash
composer a
```

OR

```bash
composer analyse
```

### Code style format

#### Default

If you want to use Pint (based on PHP-CS-Fixer), you can use the following command in the root directory of this project:

```bash
vendor/bin/pint --repair
```

#### Customized

I configured Pint to use the `--repair` option by default.

```bash
composer f
```

OR

```bash
composer format
```

### Testing

We use Pest and UnitTest as the testing frameworks. You can write one of the two (Required: Pest or PHPUnit, Pest is recommended) or both (not required but recommended to learn the Pest testing framework).

```bash
vendor/bin/pest
```

## Tutorials

### Seeder

```bash
php artisan db:seed --class="Database\Seeders\TemplateSeeder"
```

### Factory

```bash
php artisan tinker
```

```php
\App\Models\User::factory()->create()
```

OR

```bash
php artisan tinker --execute="\App\Models\User::factory()->create()"
```
