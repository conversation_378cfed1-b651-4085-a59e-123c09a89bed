{"preset": "laravel", "exclude": ["docs", "bootstrap", "storage", "vendor"], "rules": {"simplified_null_return": true, "braces": false, "new_with_braces": {"anonymous_class": false, "named_class": false}, "array_syntax": {"syntax": "short"}, "ordered_imports": {"sort_algorithm": "alpha"}, "no_unused_imports": true, "trailing_comma_in_multiline": true, "phpdoc_scalar": true, "unary_operator_spaces": true, "binary_operator_spaces": true, "blank_line_before_statement": {"statements": ["break", "continue", "declare", "return", "throw", "try"]}, "phpdoc_single_line_var_spacing": true, "phpdoc_var_without_name": true, "class_attributes_separation": {"elements": {"const": "one", "method": "one", "property": "one"}}, "method_argument_space": {"on_multiline": "ensure_fully_multiline", "keep_multiple_spaces_after_comma": true}, "single_trait_insert_per_statement": true, "blank_lines_before_namespace": true, "doctrine_annotation_indentation": true, "doctrine_annotation_spaces": true, "elseif": true, "full_opening_tag": true, "include": true, "php_unit_method_casing": false, "phpdoc_types": true, "phpdoc_types_order": true, "phpdoc_var_annotation_correct_order": true}}