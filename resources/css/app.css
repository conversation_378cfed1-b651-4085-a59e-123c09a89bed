@import 'tailwindcss';
@import './components/dropdown.css';
@import './components/swiper.css';
@import './components/global-buttons.css';

@plugin "@tailwindcss/typography";

@source '../../vendor/laravel/framework/src/Illuminate/Pagination/resources/views/*.blade.php';
@source '../../storage/framework/views/*.php';
@source '../**/*.blade.php';
@source '../**/*.js';

body {
    font-family: 'Be Vietnam Pro', sans-serif;
}

/* Global clickable elements */
a, button, [role="button"], [onclick], [href], [tabindex]:not([tabindex="-1"]) {
    cursor: pointer;
}

/* Ensure buttons and inputs with type button/submit have pointer cursor */
button, input[type="button"], input[type="submit"], input[type="reset"] {
    cursor: pointer;
}
