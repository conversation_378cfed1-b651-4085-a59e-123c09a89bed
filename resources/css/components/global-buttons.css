/* Global Buttons Styles */
.global-buttons-container {
    @apply fixed bottom-6 right-6 z-40 flex flex-col gap-3;
}

/* Mobile adjustments */
@media (max-width: 640px) {
    .global-buttons-container {
        @apply bottom-4 right-4 gap-2;
    }
    
    .global-buttons-container button,
    .global-buttons-container a {
        @apply w-11 h-11;
    }
    
    .global-buttons-container svg {
        @apply w-5 h-5;
    }
}

/* Screen reader only text */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border-width: 0;
}

/* Animation for floating effect */
@keyframes float {
    0%, 100% {
        transform: translateY(0);
    }
    50% {
        transform: translateY(-5px);
    }
}

.global-buttons-container button:hover:not(:disabled),
.global-buttons-container a:hover {
    animation: float 1s ease-in-out infinite;
}

/* Pulse animation for attention */
@keyframes pulse-ring {
    0% {
        box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.5);
    }
    100% {
        box-shadow: 0 0 0 10px rgba(59, 130, 246, 0);
    }
}

.global-buttons-container a:nth-child(2),
.global-buttons-container a:nth-child(3) {
    animation: pulse-ring 2s infinite;
}

/* Enhanced focus states for accessibility */
.global-buttons-container button:focus,
.global-buttons-container a:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.5);
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .global-buttons-container button,
    .global-buttons-container a {
        border: 2px solid currentColor;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .global-buttons-container button:hover,
    .global-buttons-container a:hover {
        animation: none;
    }
    
    .global-buttons-container a:nth-child(2),
    .global-buttons-container a:nth-child(3) {
        animation: none;
    }
    
    .global-buttons-container * {
        transition-duration: 0.01ms !important;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    .global-buttons-container button,
    .global-buttons-container a {
        @apply shadow-2xl;
    }
}