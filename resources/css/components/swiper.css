@import 'swiper/css';
@import 'swiper/css/navigation';
@import 'swiper/css/pagination';

:root {
    --swiper-navigation-sides-offset: -4rem;
}

/* Custom Swiper Navigation Styles */
.swiper-button-next,
.swiper-button-prev {
    color: #10B981;
    /* emerald-500 */
    background-color: white;
    width: 44px;
    height: 44px;
    border-radius: 50%;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    transition: background-color 0.3s, color 0.3s, opacity 0.3s ease-in-out;
    opacity: 0;
    pointer-events: none;
    z-index: 10;
}

.swiper-container:hover .swiper-button-next,
.swiper-container:hover .swiper-button-prev {
    opacity: 1;
    pointer-events: auto;
}

.swiper-button-next:hover,
.swiper-button-prev:hover {
    background-color: #059669;
    /* emerald-600 */
    color: white;
}

.swiper-button-next::after,
.swiper-button-prev::after {
    font-size: 1.25rem;
    font-weight: bold;
}

/* Custom Swiper Pagination Styles */
.swiper-pagination-bullet {
    background-color: #D1D5DB;
    /* gray-300 */
    opacity: 1;
}

.swiper-pagination-bullet-active {
    background-color: #10B981;
    /* emerald-500 */
}
