/* Consultation Page Styles */
.consultation .article-card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.consultation .article-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Swiper Navigation */
.consultation .swiper-button-next:after,
.consultation .swiper-button-prev:after {
    font-size: 1.5rem;
    color: #3b82f6;
}

.consultation .swiper-button-next,
.consultation .swiper-button-prev {
    top: 50%;
    margin-top: -16px;
    width: 32px;
    height: 32px;
    background: white;
    border-radius: 50%;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    display: flex;
    align-items: center;
    justify-content: center;
}

.consultation .swiper-button-next {
    right: -16px;
}

.consultation .swiper-button-prev {
    left: -16px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .consultation .swiper-button-next,
    .consultation .swiper-button-prev {
        width: 28px;
        height: 28px;
    }
    
    .consultation .swiper-button-next:after,
    .consultation .swiper-button-prev:after {
        font-size: 1.25rem;
    }
}
