/* Base Swiper Styles */
.swiper {
    padding-bottom: 4rem;
    padding-right: 1rem;
    padding-left: 1rem;
    margin-left: -1rem;
    margin-right: -1rem;
}

/* Hero Section Styles */
#hero {
    position: relative;
}

#hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 30% 60%, rgba(16, 185, 129, 0.03) 0%, rgba(255, 255, 255, 0) 50%),
                radial-gradient(circle at 70% 40%, rgba(59, 130, 246, 0.03) 0%, rgba(255, 255, 255, 0) 50%);
    z-index: 0;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    #hero h1 {
        font-size: 2.5rem;
        line-height: 1.2;
    }
    
    #hero .trust-indicators {
        flex-direction: column;
        gap: 1rem;
    }
    
    #hero .divider {
        display: none;
    }
}