/* Template Show Page Styles */

/* Swiper Slider Styles */

/* Thumbnail Slider Styles */
.thumbnail-swiper {
    width: 100%;
    padding: 4px 0;
    margin-top: 8px;
}

.thumbnail-swiper .swiper-slide {
    opacity: 0.6;
    transition: opacity 0.3s ease;
    cursor: pointer;
    border: 2px solid transparent;
    border-radius: 0.375rem;
    overflow: hidden;
}

.thumbnail-swiper .swiper-slide:hover {
    opacity: 0.9;
}

.thumbnail-swiper .swiper-slide-thumb-active {
    opacity: 1;
    border-color: #10b981; /* emerald-500 */
    position: relative;
}

.thumbnail-swiper .swiper-slide-thumb-active::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(16, 185, 129, 0.1);
}

.thumbnail-swiper img {
    display: block;
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.thumbnail-swiper .swiper-slide:hover img {
    transform: scale(1.05);
}

/* Adjust main swiper spacing when thumbnails are present */
.template-swiper {
    margin-bottom: 0.5rem;
}

/* Main Swiper Slider Styles */
.template-swiper {
    width: 100%;
    height: 500px;
    border-radius: 0.5rem;
    overflow: hidden;
    --swiper-navigation-size: 24px;
    --swiper-navigation-color: white;
    --swiper-pagination-color: white;
    --swiper-pagination-bullet-inactive-color: white;
    --swiper-pagination-bullet-inactive-opacity: 0.5;
}

.template-swiper .swiper-slide {
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f3f4f6;
}

.template-swiper .swiper-slide img {
    display: block;
    width: 100%;
    height: 100%;
    background: white;
}

.template-swiper .swiper-button-prev,
.template-swiper .swiper-button-next {
    background: rgba(0, 0, 0, 0.3);
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease, background 0.3s ease;
}

.template-swiper:hover .swiper-button-prev,
.template-swiper:hover .swiper-button-next {
    opacity: 1;
}

.template-swiper .swiper-button-prev {
    left: 10px;
}

.template-swiper .swiper-button-next {
    right: 10px;
}

.template-swiper .swiper-pagination-bullet {
    width: 10px;
    height: 10px;
    margin: 0 4px;
    background: white;
    opacity: 0.5;
    transition: all 0.3s ease;
}

.template-swiper .swiper-pagination-bullet-active {
    opacity: 1;
    transform: scale(1.2);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .template-swiper {
        height: 350px;
    }
    
    .template-swiper .swiper-button-prev,
    .template-swiper .swiper-button-next {
        width: 30px;
        height: 30px;
        --swiper-navigation-size: 18px;
    }
}

/* GLightbox Custom Styles */
.gslide-image img {
    max-height: 90vh !important;
    width: auto !important;
    margin: 0 auto;
}

.gslide-description {
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 1rem;
}

/* Template Preview Styles */
.template-preview {
    position: relative;
    transition: transform 0.3s ease;
}

.template-preview:hover {
    transform: scale(1.05);
}

/* Demo Button Overlay */
.demo-overlay {
    position: absolute;
    inset: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: rgba(0, 0, 0, 0.5);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.demo-overlay:hover {
    opacity: 1;
}

/* Template Info Styles */
.template-info {
    position: sticky;
    top: 1.5rem;
}

/* Related Templates */
.related-template {
    transition: all 0.3s ease;
    border: 1px solid #f3f4f6;
}

.related-template:hover {
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    transform: translateY(-0.25rem);
}

/* Contact Form Styles */
.contact-form input,
.contact-form textarea {
    width: 100%;
    padding: 0.5rem 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 0.375rem;
    outline: none;
    transition: all 0.2s ease;
}

.contact-form input:focus,
.contact-form textarea:focus {
    border-color: #3b82f6;
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .template-info {
        position: static;
        margin-top: 2rem;
    }
    
    .related-template {
        margin-bottom: 1.5rem;
    }
}
