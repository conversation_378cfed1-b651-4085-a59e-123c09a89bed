<!DOCTYPE html>
<html lang="vi" class="scroll-smooth">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON>u Website Cho Nhà B<PERSON> Nhỏ</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Be+Vietnam+Pro:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- Chosen Palette: Calm Commerce -->
    <!-- Application Structure Plan: The application is structured as a single-page decision-making tool for small sellers. It follows a logical user journey: 1. Header to grab attention with the core problem. 2. A sticky navigation for easy access. 3. "The Problem" section with an interactive fee chart and a personalized cost calculator to make the issue tangible. 4. "The Solution" section with a clear pros/cons comparison. 5. "Costs & ROI" section to demystify the investment. 6. "Roadmap" section providing a clear, step-by-step implementation guide. 7. "Future Trends" section for forward-looking inspiration. This structure transforms a dense report into an intuitive, interactive experience focused on user understanding and action, rather than passive reading. -->
    <!-- Visualization & Content Choices: 1. Fee Comparison (Problem) -> Goal: Compare -> Viz: Interactive Grouped Bar Chart (Chart.js) -> Interaction: Hover to see details, click to highlight a platform -> Justification: Visually shows the fee burden across platforms. 2. Cost Calculator (Problem) -> Goal: Inform/Personalize -> Viz: HTML form + JS logic -> Interaction: User inputs price, selects services -> Justification: Makes the financial impact immediate and personal. 3. Pros/Cons (Solution) -> Goal: Compare -> Viz: Two-column HTML layout with icons -> Interaction: Click-to-expand for details -> Justification: Easily digestible summary. 4. Roadmap (Guide) -> Goal: Organize -> Viz: HTML/CSS Stepper/Timeline -> Interaction: Click to expand stages -> Justification: Provides a clear, actionable path. 5. Future Trends (Insight) -> Goal: Inform -> Viz: Interactive HTML cards -> Interaction: Hover/click to reveal info -> Justification: Modern, engaging way to present future concepts. -->
    <!-- CONFIRMATION: NO SVG graphics used. NO Mermaid JS used. -->
    <style>
        body {
            font-family: 'Be Vietnam Pro', sans-serif;
            background-color: #FDFBF5;
            color: #3D352E;
        }
        .chart-container {
            position: relative;
            width: 100%;
            max-width: 800px;
            margin-left: auto;
            margin-right: auto;
            height: 320px;
            max-height: 400px;
        }
        @media (min-width: 768px) {
            .chart-container {
                height: 400px;
                max-height: 500px;
            }
        }
        .nav-link {
            transition: all 0.3s ease;
            border-bottom: 2px solid transparent;
        }
        .nav-link.active, .nav-link:hover {
            color: #C07953;
            border-bottom-color: #C07953;
        }
        .step-item.active .step-circle {
            background-color: #C07953;
            color: white;
            transform: scale(1.1);
        }
        .step-item.active .step-title {
            color: #C07953;
            font-weight: 600;
        }
        .card-flip {
            perspective: 1000px;
        }
        .card-inner {
            position: relative;
            width: 100%;
            height: 100%;
            transition: transform 0.6s;
            transform-style: preserve-3d;
        }
        .card-flip:hover .card-inner {
            transform: rotateY(180deg);
        }
        .card-front, .card-back {
            position: absolute;
            width: 100%;
            height: 100%;
            backface-visibility: hidden;
            -webkit-backface-visibility: hidden;
        }
        .card-back {
            transform: rotateY(180deg);
        }
        ::-webkit-scrollbar {
            width: 8px;
        }
        ::-webkit-scrollbar-track {
            background: #FDFBF5;
        }
        ::-webkit-scrollbar-thumb {
            background: #D1B49D;
            border-radius: 10px;
        }
        ::-webkit-scrollbar-thumb:hover {
            background: #C07953;
        }
    </style>
</head>
<body class="antialiased">

    <!-- Header & Navigation -->
    <header id="header" class="bg-white/80 backdrop-blur-lg sticky top-0 z-50 shadow-sm">
        <nav class="container mx-auto px-6 py-4 flex justify-between items-center">
            <div class="text-2xl font-bold text-[#C07953]">
                <a href="#hero">Nhà Bán Hàng 4.0</a>
            </div>
            <div class="hidden md:flex items-center space-x-8">
                <a href="#section-problem" class="nav-link font-medium pb-1">Vấn Đề Chi Phí</a>
                <a href="#section-solution" class="nav-link font-medium pb-1">Giải Pháp Website</a>
                <a href="#section-cost" class="nav-link font-medium pb-1">Chi Phí & Lộ Trình</a>
                <a href="#section-future" class="nav-link font-medium pb-1">Xu Hướng Tương Lai</a>
            </div>
            <button id="mobile-menu-button" class="md:hidden p-2 rounded-md">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16m-7 6h7" />
                </svg>
            </button>
        </nav>
        <!-- Mobile Menu -->
        <div id="mobile-menu" class="hidden md:hidden bg-white px-6 pb-4">
            <a href="#section-problem" class="block py-2 text-center nav-link">Vấn Đề Chi Phí</a>
            <a href="#section-solution" class="block py-2 text-center nav-link">Giải Pháp Website</a>
            <a href="#section-cost" class="block py-2 text-center nav-link">Chi Phí & Lộ Trình</a>
            <a href="#section-future" class="block py-2 text-center nav-link">Xu Hướng Tương Lai</a>
        </div>
    </header>

    <main>
        <!-- Hero Section -->
        <section id="hero" class="py-20 md:py-32 bg-amber-50">
            <div class="container mx-auto px-6 text-center">
                <h1 class="text-4xl md:text-6xl font-bold text-[#C07953] leading-tight mb-4">Phí Sàn TMĐT Quá Cao?</h1>
                <p class="text-xl md:text-2xl font-light text-gray-700 max-w-3xl mx-auto">Đã đến lúc xây dựng "ngôi nhà" của riêng bạn, giành lại quyền kiểm soát lợi nhuận và thương hiệu.</p>
                <p class="mt-8 text-lg text-gray-600">Ứng dụng này sẽ giúp bạn phân tích chi phí, khám phá lợi ích và vạch ra lộ trình xây dựng website bán hàng độc lập.</p>
            </div>
        </section>

        <!-- Section 1: The Problem -->
        <section id="section-problem" class="py-16 md:py-24">
            <div class="container mx-auto px-6">
                <div class="text-center mb-12">
                    <h2 class="text-3xl md:text-4xl font-bold mb-2">Vấn Đề Chi Phí: Gánh Nặng Từ Các Sàn TMĐT</h2>
                    <p class="text-lg text-gray-600 max-w-3xl mx-auto">Các sàn TMĐT đang đồng loạt tăng phí, làm xói mòn nghiêm trọng lợi nhuận của nhà bán hàng nhỏ lẻ. Hãy cùng xem xét các con số thực tế.</p>
                </div>

                <div class="bg-white rounded-xl shadow-lg p-6 md:p-8 mb-16">
                    <h3 class="text-2xl font-semibold text-center mb-6">So Sánh Các Loại Phí Chính (Ước tính T7/2024)</h3>
                    <div class="chart-container">
                        <canvas id="feeChart"></canvas>
                    </div>
                     <p class="text-center text-sm text-gray-500 mt-4">Di chuột qua các cột để xem chi tiết. Các mức phí có thể thay đổi và chỉ mang tính tham khảo.</p>
                </div>

                <div class="bg-white rounded-xl shadow-lg p-6 md:p-8">
                    <h3 class="text-2xl font-semibold text-center mb-6">Công Cụ Ước Tính Chi Phí & Lợi Nhuận</h3>
                    <p class="text-center text-gray-600 mb-8">Thử nhập giá sản phẩm và chọn các gói dịch vụ để xem bạn thực sự còn lại bao nhiêu trên mỗi sàn.</p>
                    
                    <div class="grid md:grid-cols-2 gap-8 items-center">
                        <div class="space-y-6">
                            <div>
                                <label for="productPrice" class="block text-lg font-medium mb-2">Nhập giá bán sản phẩm (VNĐ):</label>
                                <input type="number" id="productPrice" value="200000" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#C07953] focus:border-[#C07953] transition">
                            </div>
                            <div>
                                <label class="block text-lg font-medium mb-2">Chọn gói dịch vụ tham gia (trên Shopee):</label>
                                <div class="flex flex-wrap gap-4">
                                    <label class="flex items-center space-x-2 p-2 rounded-lg bg-gray-100 cursor-pointer">
                                        <input type="checkbox" id="freeshipXtra" class="h-5 w-5 text-[#C07953] focus:ring-[#C07953] rounded">
                                        <span>Freeship Xtra (6%)</span>
                                    </label>
                                    <label class="flex items-center space-x-2 p-2 rounded-lg bg-gray-100 cursor-pointer">
                                        <input type="checkbox" id="voucherXtra" class="h-5 w-5 text-[#C07953] focus:ring-[#C07953] rounded">
                                        <span>Voucher Xtra (3%)</span>
                                    </label>
                                </div>
                            </div>
                        </div>

                        <div id="result" class="bg-amber-50 p-6 rounded-lg border-l-4 border-[#C07953]">
                           <h4 class="text-xl font-bold mb-4">Kết Quả Ước Tính:</h4>
                           <div id="calculationDetails" class="space-y-3 text-lg">
                                <!-- Results will be populated by JS -->
                           </div>
                           <p class="mt-4 text-sm text-gray-500">Đây là ước tính dựa trên biểu phí tham khảo, chưa bao gồm các chi phí ẩn, chi phí quảng cáo và vận hành khác.</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Section 2: The Solution -->
        <section id="section-solution" class="py-16 md:py-24 bg-amber-50">
            <div class="container mx-auto px-6">
                <div class="text-center mb-12">
                    <h2 class="text-3xl md:text-4xl font-bold mb-2">Giải Pháp Website Riêng: Tự Chủ Để Phát Triển</h2>
                    <p class="text-lg text-gray-600 max-w-3xl mx-auto">Sở hữu website riêng không chỉ giúp tiết kiệm chi phí mà còn mở ra cơ hội xây dựng thương hiệu bền vững. Tuy nhiên, cũng có những thách thức cần vượt qua.</p>
                </div>

                <div class="grid md:grid-cols-2 gap-8">
                    <!-- Advantages -->
                    <div class="bg-white rounded-xl shadow-lg p-8">
                        <h3 class="text-2xl font-semibold mb-6 flex items-center text-green-700">
                            <span class="text-3xl mr-3">✔️</span> Ưu Điểm
                        </h3>
                        <ul class="space-y-4 text-lg">
                            <li class="flex items-start">
                                <span class="text-green-500 mr-3 mt-1">💰</span>
                                <div><strong class="font-semibold">Tiết kiệm chi phí dài hạn:</strong> Loại bỏ phí hoa hồng, phí dịch vụ % trên mỗi đơn hàng.</div>
                            </li>
                             <li class="flex items-start">
                                <span class="text-green-500 mr-3 mt-1">👑</span>
                                <div><strong class="font-semibold">Kiểm soát thương hiệu 100%:</strong> Tự do thiết kế, tùy chỉnh giao diện và câu chuyện thương hiệu.</div>
                            </li>
                             <li class="flex items-start">
                                <span class="text-green-500 mr-3 mt-1">📊</span>
                                <div><strong class="font-semibold">Sở hữu dữ liệu khách hàng:</strong> Thu thập và phân tích thông tin để marketing và chăm sóc cá nhân hóa.</div>
                            </li>
                             <li class="flex items-start">
                                <span class="text-green-500 mr-3 mt-1">🕊️</span>
                                <div><strong class="font-semibold">Linh hoạt & Không phụ thuộc:</strong> Tự quyết định chính sách, không lo bị khóa gian hàng hay thay đổi thuật toán.</div>
                            </li>
                        </ul>
                    </div>

                    <!-- Challenges -->
                    <div class="bg-white rounded-xl shadow-lg p-8">
                        <h3 class="text-2xl font-semibold mb-6 flex items-center text-red-700">
                            <span class="text-3xl mr-3">⚠️</span> Thách Thức
                        </h3>
                        <ul class="space-y-4 text-lg">
                            <li class="flex items-start">
                                <span class="text-red-500 mr-3 mt-1">🚦</span>
                                <div><strong class="font-semibold">Thu hút lưu lượng truy cập (Traffic):</strong> Phải tự đầu tư công sức, chi phí vào SEO, quảng cáo.</div>
                            </li>
                             <li class="flex items-start">
                                <span class="text-red-500 mr-3 mt-1">💸</span>
                                <div><strong class="font-semibold">Chi phí đầu tư ban đầu:</strong> Cần ngân sách cho việc thiết kế, hosting, tên miền.</div>
                            </li>
                             <li class="flex items-start">
                                <span class="text-red-500 mr-3 mt-1">🛠️</span>
                                <div><strong class="font-semibold">Quản lý kỹ thuật và vận hành:</strong> Đòi hỏi kiến thức hoặc chi phí thuê ngoài để bảo trì website.</div>
                            </li>
                             <li class="flex items-start">
                                <span class="text-red-500 mr-3 mt-1">🤝</span>
                                <div><strong class="font-semibold">Xây dựng niềm tin khách hàng:</strong> Cần thời gian để tạo uy tín cho một website mới.</div>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </section>
        
        <!-- Section 3: Cost & Roadmap -->
        <section id="section-cost" class="py-16 md:py-24">
            <div class="container mx-auto px-6">
                <div class="text-center mb-12">
                    <h2 class="text-3xl md:text-4xl font-bold mb-2">Chi Phí & Lộ Trình Triển Khai</h2>
                    <p class="text-lg text-gray-600 max-w-3xl mx-auto">Xây dựng website không quá tốn kém như bạn nghĩ. Dưới đây là các khoản chi phí dự kiến và lộ trình 4 bước để bạn bắt đầu.</p>
                </div>
                
                <div class="grid lg:grid-cols-5 gap-12">
                    <!-- Cost -->
                    <div class="lg:col-span-2 bg-white rounded-xl shadow-lg p-8">
                        <h3 class="text-2xl font-semibold mb-6">Ước Tính Chi Phí</h3>
                        <div class="space-y-6">
                            <div>
                                <h4 class="font-bold text-lg text-[#C07953]">Chi phí xây dựng ban đầu</h4>
                                <ul class="list-disc list-inside mt-2 space-y-1 text-gray-700">
                                    <li>Tên miền: 300.000 - 800.000 VNĐ</li>
                                    <li>Hosting: 800.000 - 1.500.000 VNĐ</li>
                                    <li>Thiết kế & Lập trình cơ bản: 3.000.000 - 10.000.000 VNĐ</li>
                                </ul>
                                <div class="mt-3 text-right font-bold text-xl">Tổng cộng: ~ 4 - 12 triệu+</div>
                            </div>
                            <div class="border-t pt-6">
                                <h4 class="font-bold text-lg text-[#C07953]">Chi phí duy trì hàng năm</h4>
                                <ul class="list-disc list-inside mt-2 space-y-1 text-gray-700">
                                    <li>Gia hạn tên miền & hosting: 1.100.000 - 2.300.000 VNĐ</li>
                                    <li>Bảo trì (tùy chọn): 0 - 5.000.000 VNĐ</li>
                                </ul>
                                <div class="mt-3 text-right font-bold text-xl">Tổng cộng: ~ 1 - 7 triệu+</div>
                            </div>
                        </div>
                         <p class="mt-6 text-sm text-gray-500">Chi phí chưa bao gồm marketing. So sánh với mức phí 15-18% trên doanh thu phải trả cho sàn, việc đầu tư này có thể hoàn vốn nhanh chóng.</p>
                    </div>

                    <!-- Roadmap -->
                    <div class="lg:col-span-3">
                        <div class="space-y-8">
                            <!-- Step 1 -->
                            <div class="step-item cursor-pointer" data-step="1">
                                <div class="flex items-center">
                                    <div class="step-circle h-12 w-12 rounded-full bg-amber-100 flex items-center justify-center font-bold text-xl text-[#C07953] transition-all duration-300">1</div>
                                    <h4 class="step-title ml-4 text-xl font-medium transition-colors">Nghiên cứu & Lập kế hoạch</h4>
                                </div>
                                <div id="step-content-1" class="step-content pl-16 pt-2 hidden">
                                    <ul class="list-disc list-inside text-gray-600 space-y-1">
                                        <li>Xác định mục tiêu, đối tượng khách hàng.</li>
                                        <li>Phân tích đối thủ, lập danh sách tính năng.</li>
                                        <li>Dự trù ngân sách, chọn nền tảng (WordPress, Sapo,...).</li>
                                        <li>Chọn và đăng ký tên miền, hosting.</li>
                                    </ul>
                                </div>
                            </div>
                             <!-- Step 2 -->
                            <div class="step-item cursor-pointer" data-step="2">
                                <div class="flex items-center">
                                    <div class="step-circle h-12 w-12 rounded-full bg-amber-100 flex items-center justify-center font-bold text-xl text-[#C07953] transition-all duration-300">2</div>
                                    <h4 class="step-title ml-4 text-xl font-medium transition-colors">Thiết kế & Phát triển</h4>
                                </div>
                                <div id="step-content-2" class="step-content pl-16 pt-2 hidden">
                                    <ul class="list-disc list-inside text-gray-600 space-y-1">
                                        <li>Thiết kế giao diện (UI/UX) chuyên nghiệp.</li>
                                        <li>Lập trình các tính năng đã xác định.</li>
                                        <li>Chuẩn bị hình ảnh, video, mô tả sản phẩm.</li>
                                        <li>Tích hợp cổng thanh toán, đơn vị vận chuyển.</li>
                                    </ul>
                                </div>
                            </div>
                             <!-- Step 3 -->
                            <div class="step-item cursor-pointer" data-step="3">
                                <div class="flex items-center">
                                    <div class="step-circle h-12 w-12 rounded-full bg-amber-100 flex items-center justify-center font-bold text-xl text-[#C07953] transition-all duration-300">3</div>
                                    <h4 class="step-title ml-4 text-xl font-medium transition-colors">Kiểm tra & Ra mắt</h4>
                                </div>
                                <div id="step-content-3" class="step-content pl-16 pt-2 hidden">
                                    <ul class="list-disc list-inside text-gray-600 space-y-1">
                                        <li>Kiểm tra toàn diện tính năng, quy trình mua hàng.</li>
                                        <li>Tối ưu tốc độ tải trang, SEO cơ bản.</li>
                                        <li>Lên kế hoạch truyền thông cho việc ra mắt.</li>
                                        <li>Chính thức đưa website vào hoạt động.</li>
                                    </ul>
                                </div>
                            </div>
                             <!-- Step 4 -->
                            <div class="step-item cursor-pointer" data-step="4">
                                <div class="flex items-center">
                                    <div class="step-circle h-12 w-12 rounded-full bg-amber-100 flex items-center justify-center font-bold text-xl text-[#C07953] transition-all duration-300">4</div>
                                    <h4 class="step-title ml-4 text-xl font-medium transition-colors">Vận hành & Cải tiến</h4>
                                </div>
                                <div id="step-content-4" class="step-content pl-16 pt-2 hidden">
                                    <ul class="list-disc list-inside text-gray-600 space-y-1">
                                        <li>Triển khai marketing thu hút khách (Ads, SEO).</li>
                                        <li>Xử lý đơn hàng, chăm sóc khách hàng.</li>
                                        <li>Phân tích dữ liệu để hiểu hành vi người dùng.</li>
                                        <li>Liên tục cải tiến website và chiến lược kinh doanh.</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Section 4: Future Trends -->
        <section id="section-future" class="py-16 md:py-24 bg-amber-50">
            <div class="container mx-auto px-6">
                <div class="text-center mb-12">
                    <h2 class="text-3xl md:text-4xl font-bold mb-2">Đón Đầu Xu Hướng Tương Lai</h2>
                    <p class="text-lg text-gray-600 max-w-3xl mx-auto">Để phát triển bền vững, hãy nắm bắt các công nghệ và chiến lược mới giúp nâng cao trải nghiệm khách hàng và tối ưu hiệu quả kinh doanh.</p>
                </div>

                <div class="grid sm:grid-cols-2 lg:grid-cols-4 gap-8">
                    <!-- Card 1: AI -->
                    <div class="card-flip h-64">
                        <div class="card-inner rounded-xl shadow-lg">
                            <div class="card-front bg-white p-6 flex flex-col justify-center items-center text-center rounded-xl">
                                <div class="text-5xl mb-4">🤖</div>
                                <h3 class="text-xl font-bold">Trí tuệ nhân tạo (AI)</h3>
                                <p class="text-gray-500 mt-2">Tự động hóa & Cá nhân hóa</p>
                            </div>
                            <div class="card-back bg-[#C07953] text-white p-6 flex flex-col justify-center rounded-xl">
                                <h4 class="font-bold mb-2">Ứng dụng:</h4>
                                <ul class="list-disc list-inside text-sm space-y-1">
                                    <li>Chatbot 24/7 trả lời khách hàng</li>
                                    <li>Gợi ý sản phẩm thông minh</li>
                                    <li>Tự động hóa email marketing</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                     <!-- Card 2: Omnichannel -->
                    <div class="card-flip h-64">
                        <div class="card-inner rounded-xl shadow-lg">
                            <div class="card-front bg-white p-6 flex flex-col justify-center items-center text-center rounded-xl">
                                <div class="text-5xl mb-4">🔄</div>
                                <h3 class="text-xl font-bold">Omnichannel</h3>
                                <p class="text-gray-500 mt-2">Kết hợp đa kênh</p>
                            </div>
                            <div class="card-back bg-[#C07953] text-white p-6 flex flex-col justify-center rounded-xl">
                                <h4 class="font-bold mb-2">Chiến lược:</h4>
                                 <ul class="list-disc list-inside text-sm space-y-1">
                                    <li>Dùng Sàn TMĐT làm phễu thu hút</li>
                                    <li>Dẫn khách hàng về website riêng</li>
                                    <li>Tạo trải nghiệm liền mạch</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                     <!-- Card 3: Social Commerce -->
                    <div class="card-flip h-64">
                        <div class="card-inner rounded-xl shadow-lg">
                            <div class="card-front bg-white p-6 flex flex-col justify-center items-center text-center rounded-xl">
                                <div class="text-5xl mb-4">❤️</div>
                                <h3 class="text-xl font-bold">Social Commerce</h3>
                                <p class="text-gray-500 mt-2">Bán hàng qua mạng xã hội</p>
                            </div>
                            <div class="card-back bg-[#C07953] text-white p-6 flex flex-col justify-center rounded-xl">
                                <h4 class="font-bold mb-2">Hành động:</h4>
                                 <ul class="list-disc list-inside text-sm space-y-1">
                                    <li>Tích hợp cửa hàng trên Facebook, Instagram</li>
                                    <li>Livestream bán hàng, tương tác</li>
                                    <li>Xây dựng cộng đồng trung thành</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                     <!-- Card 4: CAC vs LTV -->
                    <div class="card-flip h-64">
                        <div class="card-inner rounded-xl shadow-lg">
                            <div class="card-front bg-white p-6 flex flex-col justify-center items-center text-center rounded-xl">
                                <div class="text-5xl mb-4">📈</div>
                                <h3 class="text-xl font-bold">LTV > CAC</h3>
                                <p class="text-gray-500 mt-2">Tối ưu chi phí Marketing</p>
                            </div>
                            <div class="card-back bg-[#C07953] text-white p-6 flex flex-col justify-center rounded-xl">
                                <h4 class="font-bold mb-2">Nguyên tắc:</h4>
                                <p class="text-sm">Giá trị vòng đời khách hàng (LTV) phải lớn hơn Chi phí thu hút khách hàng (CAC). Tỷ lệ lý tưởng là 3:1. Tập trung giữ chân khách hàng cũ để tăng LTV.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <footer class="bg-gray-800 text-white py-8">
        <div class="container mx-auto px-6 text-center">
            <p>&copy; 2024 Nhà Bán Hàng 4.0. All Rights Reserved.</p>
            <p class="text-sm text-gray-400 mt-2">Dữ liệu trong ứng dụng này được tổng hợp và phân tích từ báo cáo "Phân Tích Nhu Cầu Thiết Kế Website Của Nhóm Nhà Bán Hàng Nhỏ Lẻ". Mọi thông tin chỉ mang tính chất tham khảo tại thời điểm phân tích.</p>
        </div>
    </footer>
    
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            // Data from the report
            const feeData = {
                platforms: ['Shopee', 'Lazada', 'Tiki', 'TikTok Shop'],
                paymentFee: [5, 2.75, 2, 5], 
                fixedFee: [4, 3.993, 8, 0], 
                serviceFee: [9, 11, 0, 0] 
            };
            const reportData = {
                shopee: { payment: 5, fixed: 4, freeship: 6, voucher: 3 },
                lazada: { payment: 2.75, fixed: 3.993 }, 
                tiki: { payment: 2, fixed: 8 }, 
                tiktok: { transaction: 5 }
            };

            // Mobile menu toggle
            const mobileMenuButton = document.getElementById('mobile-menu-button');
            const mobileMenu = document.getElementById('mobile-menu');
            mobileMenuButton.addEventListener('click', () => {
                mobileMenu.classList.toggle('hidden');
            });
            
            // Fee Comparison Chart
            const ctx = document.getElementById('feeChart').getContext('2d');
            const feeChart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: feeData.platforms,
                    datasets: [
                        {
                            label: 'Phí Thanh Toán / Giao dịch (%)',
                            data: feeData.paymentFee,
                            backgroundColor: '#EDCDBB',
                            borderColor: '#C07953',
                            borderWidth: 1
                        },
                        {
                            label: 'Phí Cố Định / Chiết khấu (%)',
                            data: feeData.fixedFee,
                            backgroundColor: '#C07953',
                            borderColor: '#8A5A44',
                            borderWidth: 1
                        },
                        {
                            label: 'Phí Dịch Vụ (Freeship/Voucher) (%)',
                            data: feeData.serviceFee,
                            backgroundColor: '#8A5A44',
                            borderColor: '#583A2A',
                            borderWidth: 1
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: 'Tỷ lệ phí (%)'
                            }
                        }
                    },
                    plugins: {
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    let label = context.dataset.label || '';
                                    if (label) {
                                        label += ': ';
                                    }
                                    if (context.parsed.y !== null) {
                                        label += context.parsed.y + '%';
                                    }
                                    return label;
                                }
                            }
                        },
                        legend: {
                           position: 'bottom',
                        }
                    },
                    interaction: {
                        mode: 'index',
                        intersect: false,
                    },
                }
            });

            // Cost Calculator
            const productPriceInput = document.getElementById('productPrice');
            const freeshipXtraCheckbox = document.getElementById('freeshipXtra');
            const voucherXtraCheckbox = document.getElementById('voucherXtra');
            const calculationDetailsDiv = document.getElementById('calculationDetails');

            function formatCurrency(value) {
                return new Intl.NumberFormat('vi-VN', { style: 'currency', currency: 'VND' }).format(value);
            }

            function calculateFees() {
                const price = parseFloat(productPriceInput.value) || 0;
                
                // Shopee Calculation
                let shopeeTotalFeePercent = reportData.shopee.payment + reportData.shopee.fixed;
                if(freeshipXtraCheckbox.checked) shopeeTotalFeePercent += reportData.shopee.freeship;
                if(voucherXtraCheckbox.checked) shopeeTotalFeePercent += reportData.shopee.voucher;
                const shopeeTotalFee = price * (shopeeTotalFeePercent / 100);
                const shopeeProfit = price - shopeeTotalFee;

                // Lazada Calculation
                const lazadaTotalFeePercent = reportData.lazada.payment + reportData.lazada.fixed;
                const lazadaTotalFee = price * (lazadaTotalFeePercent / 100);
                const lazadaProfit = price - lazadaTotalFee;
                
                // Tiki Calculation
                const tikiTotalFeePercent = reportData.tiki.payment + reportData.tiki.fixed;
                const tikiTotalFee = price * (tikiTotalFeePercent / 100);
                const tikiProfit = price - tikiTotalFee;
                
                // TikTok Shop Calculation
                const tiktokTotalFee = price * (reportData.tiktok.transaction / 100);
                const tiktokProfit = price - tiktokTotalFee;
                
                calculationDetailsDiv.innerHTML = `
                    <p><strong>Shopee:</strong></p>
                    <p class="pl-4">Tổng phí: <span class="font-semibold text-red-600">${formatCurrency(shopeeTotalFee)}</span> (~${shopeeTotalFeePercent.toFixed(1)}%)</p>
                    <p class="pl-4">Lợi nhuận còn lại: <span class="font-semibold text-green-700">${formatCurrency(shopeeProfit)}</span></p>
                    <p class="mt-2"><strong>Lazada:</strong></p>
                    <p class="pl-4">Tổng phí: <span class="font-semibold text-red-600">${formatCurrency(lazadaTotalFee)}</span> (~${lazadaTotalFeePercent.toFixed(1)}%)</p>
                    <p class="pl-4">Lợi nhuận còn lại: <span class="font-semibold text-green-700">${formatCurrency(lazadaProfit)}</span></p>
                    <p class="mt-2"><strong>TikTok Shop:</strong></p>
                    <p class="pl-4">Tổng phí: <span class="font-semibold text-red-600">${formatCurrency(tiktokTotalFee)}</span> (~${reportData.tiktok.transaction.toFixed(1)}%)</p>
                    <p class="pl-4">Lợi nhuận còn lại: <span class="font-semibold text-green-700">${formatCurrency(tiktokProfit)}</span></p>
                `;
            }

            productPriceInput.addEventListener('input', calculateFees);
            freeshipXtraCheckbox.addEventListener('change', calculateFees);
            voucherXtraCheckbox.addEventListener('change', calculateFees);
            calculateFees(); // Initial calculation

            // Roadmap interaction
            const stepItems = document.querySelectorAll('.step-item');
            stepItems.forEach(item => {
                item.addEventListener('click', () => {
                    const stepNumber = item.dataset.step;
                    const content = document.getElementById(`step-content-${stepNumber}`);
                    
                    const wasActive = item.classList.contains('active');
                    
                    // Reset all
                    stepItems.forEach(i => {
                        i.classList.remove('active');
                        document.getElementById(`step-content-${i.dataset.step}`).classList.add('hidden');
                    });
                    
                    // Toggle current
                    if (!wasActive) {
                        item.classList.add('active');
                        content.classList.remove('hidden');
                    }
                });
            });
             // Activate first step by default
            const firstStep = document.querySelector('.step-item[data-step="1"]');
            if(firstStep) {
                firstStep.classList.add('active');
                document.getElementById('step-content-1').classList.remove('hidden');
            }


            // Nav link active state on scroll
            const sections = document.querySelectorAll('main section');
            const navLinks = document.querySelectorAll('.nav-link');
            
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        navLinks.forEach(link => {
                            link.classList.remove('active');
                            if (link.getAttribute('href').substring(1) === entry.target.id) {
                                link.classList.add('active');
                            }
                        });
                    }
                });
            }, { threshold: 0.5 }); 

            sections.forEach(section => {
                observer.observe(section);
            });
        });
    </script>
</body>
</html>
