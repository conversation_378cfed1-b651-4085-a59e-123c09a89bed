import Alpine from 'alpinejs';

// Define global store for modal state
Alpine.store('modal', {
    isOpen: false,
    open() { this.isOpen = true; },
    close() { this.isOpen = false; }
});

// Register the contact modal component
Alpine.data('contactModal', () => ({
    step: 1,
    formSubmitted: false,
    showClosePopover: false,
    formData: {
        name: '',
        email: '',
        phone: '',
        company: '',
        services: [],
        purpose: '',
        budget: '',
        details: '',
        website_ref: '',
        package: '',
    },

    resetForm() {
        this.step = 1;
        this.formSubmitted = false;
        this.formData = {
            name: '',
            email: '',
            phone: '',
            company: '',
            services: [],
            purpose: '',
            budget: '',
            details: '',
            website_ref: '',
            package: ''
        };
    },

    autoFillUserData() {
        if (this.userLoggedIn && this.userData) {
            this.formData.name = this.userData.name || '';
            this.formData.email = this.userData.email || '';
            this.formData.phone = this.userData.phone || '';
            this.formData.company = this.userData.company || '';
        }
    },
    nextStep() {
        if (this.isStepValid()) {
            if (this.step < 3) this.step++;
        }
    },
    prevStep() {
        if (this.step > 1) this.step--;
    },
    currentTitle() {
        const titles = ['Thông tin Liên hệ', 'Nhu cầu của bạn', 'Thông tin bổ sung'];
        return titles[this.step - 1];
    },
    isStepValid() {
        return true;
    },
    submitForm() {
        const response = fetch('/api/lead', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(this.formData),
        });

        response.then(() => {
            this.formSubmitted = true;
            setTimeout(() => {
                if(this.$store.modal.isOpen) {
                    this.$store.modal.close();
                }
            }, 5000);
        });
    }
}));
