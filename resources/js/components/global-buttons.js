/**
 * Global Buttons Component
 * Handles initialization and behavior for global buttons
 */

// Alpine component is already defined inline in the Blade component
// This file is for any additional functionality if needed

class GlobalButtonsTracker {
    constructor() {
        this.isGtagAvailable = typeof gtag !== 'undefined' && gtag;
    }

    /**
     * Track button click event with error handling
     * @param {string} buttonType - Type of button clicked
     */
    trackButtonClick(buttonType) {
        if (!this.isGtagAvailable) {
            console.warn('Google Analytics not available');
            return;
        }

        try {
            gtag('event', 'click', {
                'event_category': 'Global Buttons',
                'event_label': buttonType,
                'transport_type': 'beacon'
            });
        } catch (error) {
            console.error('Analytics tracking error:', error);
        }
    }

    /**
     * Track custom event
     * @param {string} eventName - Name of the event
     * @param {Object} parameters - Event parameters
     */
    trackEvent(eventName, parameters = {}) {
        if (!this.isGtagAvailable) return;

        try {
            gtag('event', eventName, {
                'event_category': 'Global Buttons',
                ...parameters
            });
        } catch (error) {
            console.error('Analytics tracking error:', error);
        }
    }
}

// Initialize tracker
const tracker = new GlobalButtonsTracker();

document.addEventListener('DOMContentLoaded', () => {
    // Add click tracking to buttons
    document.addEventListener('click', (e) => {
        const button = e.target.closest('[data-track-button]');
        if (button) {
            tracker.trackButtonClick(button.getAttribute('data-track-button'));
        }
    });

    // Add smooth scroll behavior for browsers that don't support it
    if (!('scrollBehavior' in document.documentElement.style)) {
        const scrollToTop = () => {
            const scrollStep = -window.scrollY / 15;
            const scrollInterval = setInterval(() => {
                if (window.scrollY !== 0) {
                    window.scrollBy(0, scrollStep);
                } else {
                    clearInterval(scrollInterval);
                }
            }, 15);
        };

        // Override the scroll to top function
        // @ts-ignore
        window.scrollToTop = scrollToTop;
    }

    // Track scroll-to-top visibility changes
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                tracker.trackEvent('scroll_button_shown');
            }
        });
    });

    // Observe scroll button when it becomes visible
    const scrollButton = document.querySelector('[data-track-button="go-to-top"]');
    if (scrollButton) {
        observer.observe(scrollButton);
    }
});

// Export for use in other modules if needed
export { GlobalButtonsTracker };
