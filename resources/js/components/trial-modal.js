import Alpine from 'alpinejs';

Alpine.store('trialModal', {
    isOpen: false,
    templateId: null,
    templateName: null,
    open(templateId, templateName) { 
        this.templateId = templateId;
        this.templateName = templateName;
        this.isOpen = true; 
    },
    close() { 
        this.isOpen = false; 
        this.templateId = null; 
        this.templateName = null;
    }
});

// Register the contact modal component
Alpine.data('trialModal', () => ({
    step: 1,
    isSubmitting: false,
    formSubmitted: false,
    showClosePopover: false,
    errors: {},
    formData: {
        name: '',
        phone: '',
        company: '',
        subdomain: '',
        email: '',
        purpose: '',
        details: '',
        package: 'basic'
    },
    
    // Validation rules for each field
    validationRules: {
        name: {
            required: true,
            minLength: 2,
            maxLength: 100
        },
        phone: {
            required: true,
            pattern: /^(\+?84|0)[1-9][0-9]{8,9}$/
        },
        company: {
            required: true,
            minLength: 2,
            maxLength: 200
        },
        email: {
            required: true,
            pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/
        },
        package: {
            required: true
        },
        subdomain: {
            required: true,
            pattern: /^[a-z0-9](?:[a-z0-9-]{0,61}[a-z0-9])?$/
        },
        purpose: {
            required: false
        },
        details: {
            required: false,
            maxLength: 1000
        }
    },
    
    // Error messages for each field
    errorMessages: {
        name: {
            required: 'Vui lòng nhập họ và tên',
            minLength: 'Tên phải có ít nhất 2 ký tự',
            maxLength: 'Tên không được vượt quá 100 ký tự'
        },
        phone: {
            required: 'Vui lòng nhập số điện thoại',
            pattern: 'Số điện thoại không hợp lệ'
        },
        company: {
            required: 'Vui lòng nhập tên công ty',
            minLength: 'Tên công ty phải có ít nhất 2 ký tự',
            maxLength: 'Tên công ty không được vượt quá 200 ký tự'
        },
        email: {
            required: 'Vui lòng nhập email',
            pattern: 'Email không hợp lệ',
            exists: 'Email đã được sử dụng. Vui lòng sử dụng email khác.'
        },
        package: {
            required: 'Vui lòng chọn gói đăng ký'
        },
        subdomain: {
            required: 'Vui lòng nhập tên miền',
            pattern: 'Tên miền chỉ được chứa chữ thường, số và dấu gạch ngang',
            exists: 'Tên miền đã được sử dụng. Vui lòng chọn tên khác.'
        },
        details: {
            maxLength: 'Mô tả không được vượt quá 1000 ký tự'
        }
    },
    
    // Check if email exists in the database
    async checkEmailExists(email) {
        if (!email || !this.validationRules.email.pattern.test(email)) {
            return false;
        }
        
        try {
            const response = await fetch(`/api/check-email?email=${encodeURIComponent(email)}`);
            const data = await response.json();
            return data.exists;
        } catch (error) {
            console.error('Error checking email:', error);
            return false;
        }
    },

    // Check if subdomain exists in the database
    async checkSubdomainExists(subdomain) {
        if (!subdomain || !this.validationRules.subdomain.pattern.test(subdomain)) {
            return false;
        }
        
        try {
            const response = await fetch(`/api/check-subdomain?subdomain=${encodeURIComponent(subdomain)}`);
            const data = await response.json();
            return data.exists;
        } catch (error) {
            console.error('Error checking subdomain:', error);
            return false;
        }
    },

    // Validate a single field
    async validateField(field) {
        // Clear any existing error
        if (this.errors[field]) {
            delete this.errors[field];
        }

        const value = this.formData[field];
        const rules = this.validationRules[field];

        // Skip validation if no rules exist
        if (!rules) return true;
        
        // Check required
        if (rules.required && !value) {
            this.errors[field] = this.errorMessages[field].required;
            return false;
        }
        
        // Check min length
        if (rules.minLength && value && value.length < rules.minLength) {
            this.errors[field] = this.errorMessages[field].minLength;
            return false;
        }
        
        // Check max length
        if (rules.maxLength && value && value.length > rules.maxLength) {
            this.errors[field] = this.errorMessages[field].maxLength;
            return false;
        }
        
        // Check pattern
        if (rules.pattern && value && !rules.pattern.test(value)) {
            this.errors[field] = this.errorMessages[field].pattern || 'Giá trị không hợp lệ';
            return false;
        }
        
        // Check if email exists in database
        if (field === 'email' && value) {
            const emailExists = await this.checkEmailExists(value);
            if (emailExists) {
                this.errors[field] = this.errorMessages[field].exists;
                return false;
            }
        }
        
        // Check if subdomain exists in database
        if (field === 'subdomain' && value) {
            const subdomainExists = await this.checkSubdomainExists(value);
            if (subdomainExists) {
                this.errors[field] = this.errorMessages[field].exists;
                return false;
            }
        }
        
        return true;
    },
    
    // Check if a field has an error
    hasError(field) {
        return !!this.errors[field];
    },
    
    // Get error message for a field
    getErrorMessage(field) {
        return this.errors[field] || '';
    },

    resetForm() {
        this.step = 1;
        this.formSubmitted = false;
        this.formData = {
            name: '',
            phone: '',
            company: '',
            subdomain: '',
            email: '',
            purpose: '',
            details: '',
            package: ''
        };
    },
    nextStep() {
        this.isStepValid().then(isValid => {
            if (isValid && this.step < 3) this.step++;
        });
    },
    prevStep() {
        if (this.step > 1) this.step--;
    },
    currentTitle() {
        const titles = [
            'Tạo Website Dùng Thử Miễn Phí Trong 14 Ngày',
            'Nhập thông tin Website của bạn',
            'Xác nhận thông tin Website'
        ];
        return titles[this.step - 1];
    },
    async isStepValid() {
        let isValid = true;
        
        // Clear all errors for the current step
        Object.keys(this.errors).forEach(field => {
            delete this.errors[field];
        });

        // Validate fields based on current step
        if (this.step === 1) {
            // Step 1: Validate personal information
            const fieldsToValidate = ['name', 'phone', 'company', 'email'];
            for (const field of fieldsToValidate) {
                if (!await this.validateField(field)) {
                    isValid = false;
                }
            }
        } else if (this.step === 2) {
            // Step 2: Validate package and subdomain
            const fieldsToValidate = ['package', 'subdomain'];
            for (const field of fieldsToValidate) {
                if (!await this.validateField(field)) {
                    isValid = false;
                }
            }
            
            // Step 2 also has optional fields that need length validation
            if (this.formData.details && this.formData.details.length > 1000) {
                this.errors.details = this.errorMessages.details.maxLength;
                isValid = false;
            }
        }
        // Step 3 is summary, no validation needed
        
        // Scroll to first error if any
        if (!isValid) {
            const firstErrorField = Object.keys(this.errors)[0];
            if (firstErrorField) {
                await this.$nextTick();
                const element = document.querySelector(`[name="${firstErrorField}"]`);
                if (element) {
                    element.scrollIntoView({ behavior: 'smooth', block: 'center' });
                    element.focus();
                }
            }
        }
        
        return isValid;
    },
    async submitForm() {
        // Validate all fields before submission
        let isValid = true;
        Object.keys(this.formData).forEach(field => {
            if (field !== 'details' && field !== 'purpose') {
                if (!this.validateField(field)) {
                    isValid = false;
                }
            }
        });
        
        if (!isValid) {
            // Scroll to first error
            const firstErrorField = Object.keys(this.errors)[0];
            if (firstErrorField) {
                const element = document.querySelector(`[name="${firstErrorField}"]`);
                if (element) {
                    element.scrollIntoView({ behavior: 'smooth', block: 'center' });
                    element.focus();
                }
            }
            return;
        }
        
        try {
            const response = await fetch('/api/website', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.content || ''
                },
                body: JSON.stringify({
                    ...this.formData,
                    template_id: this.$store.trialModal.templateId,
                    template_name: this.$store.trialModal.templateName,
                }),
            });
            
            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.message || 'Có lỗi xảy ra khi gửi thông tin');
            }
            
            this.formSubmitted = true;
            
            // Reset form after successful submission
            setTimeout(() => {
                if (this.$store.trialModal.isOpen) {
                    this.resetForm();
                    this.$store.trialModal.close();
                }
            }, 3000);
            
        } catch (error) {
            console.error('Error submitting form:', error);
            // Show error message to user
            alert(error.message || 'Có lỗi xảy ra. Vui lòng thử lại sau.');
        }
    }
}));
