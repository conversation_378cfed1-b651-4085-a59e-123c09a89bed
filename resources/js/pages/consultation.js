import Swiper from 'swiper';
import { Navigation } from 'swiper/modules';

export class ConsultationPage {
    constructor() {
        this.initCategorySwipers();
    }

    initCategorySwipers() {
        const swiperContainers = document.querySelectorAll('.consultation-category-swiper');
        
        swiperContainers.forEach((container, index) => {
            const navigation = {
                nextEl: container.querySelector('.swiper-button-next'),
                prevEl: container.querySelector('.swiper-button-prev'),
            };

            new Swiper(container.querySelector('.swiper'), {
                modules: [Navigation],
                slidesPerView: 1,
                spaceBetween: 20,
                navigation: navigation,
                breakpoints: {
                    640: {
                        slidesPerView: 2,
                    },
                    1024: {
                        slidesPerView: 4,
                    },
                },
            });
        });
    }
}

// Initialize on page load
if (document.querySelector('.consultation-category-swiper')) {
    new ConsultationPage();
}
