import Swiper from 'swiper';
import { Navigation, Pagination, Autoplay } from 'swiper/modules';

export class HomePage {
    constructor() {}

    init() {
        this.initTestimonialSwiper();
        this.initFeaturedSwiper();
        this.initNewSwiper();
    }

    initTestimonialSwiper() {
        if (document.querySelector('.testimonial-swiper')) {
            new Swiper('.testimonial-swiper', {
                modules: [Navigation, Pagination, Autoplay],
                slidesPerView: 1,
                spaceBetween: 30,
                loop: true,
                autoplay: {
                    delay: 5000,
                    disableOnInteraction: false,
                },
                pagination: {
                    el: '.testimonial-swiper .swiper-pagination',
                    clickable: true,
                },
                breakpoints: {
                    768: { slidesPerView: 2 },
                    1024: { slidesPerView: 3 }
                }
            });
        }
    }

    initFeaturedSwiper() {
        if (document.querySelector('.featured-carousel')) {
            new Swiper('.featured-carousel', {
                modules: [Navigation, Pagination],
                loop: true,
                slidesPerView: 1,
                spaceBetween: 30,
                pagination: {
                    el: '#projects .swiper-pagination',
                    clickable: true,
                },
                navigation: {
                    nextEl: '#projects .swiper-button-next',
                    prevEl: '#projects .swiper-button-prev',
                },
                breakpoints: {
                    768: { slidesPerView: 2 },
                    1024: { slidesPerView: 4 }
                }
            });
        }
    }

    initNewSwiper() {
        if (document.querySelector('.new-carousel')) {
            new Swiper('.new-carousel', {
                modules: [Navigation, Pagination],
                loop: true,
                slidesPerView: 1,
                spaceBetween: 30,
                pagination: {
                    el: '#new-projects .swiper-pagination',
                    clickable: true,
                },
                navigation: {
                    nextEl: '#new-projects .swiper-button-next',
                    prevEl: '#new-projects .swiper-button-prev',
                },
                breakpoints: {
                    768: { slidesPerView: 2 },
                    1024: { slidesPerView: 4 }
                }
            });
        }
    }
}

