import Swiper from 'swiper';
import { Navigation, Autoplay, Thumbs, Pagination } from 'swiper/modules';

export class TemplateShowPage {
    constructor() {
        this.mainSwiper = null;
        this.thumbSwiper = null;
    }

    init() {
        this.initSmoothScroll();
        this.initImageHandlers();
        this.initTemplateSwiper();
        this.initFAQAccordion();
        this.initRelatedTemplatesSwiper();
    }

    initTemplateSwiper() {
        const mainSwiperEl = document.querySelector('.template-swiper');
        const thumbSwiperEl = document.querySelector('.thumbnail-swiper');
        
        if (!mainSwiperEl) return;

        // Initialize thumbnail swiper first if it exists
        if (thumbSwiperEl) {
            this.thumbSwiper = new Swiper(thumbSwiperEl, {
                modules: [Navigation, Thumbs],
                spaceBetween: 8,
                slidesPerView: 4,
                freeMode: true,
                watchSlidesProgress: true,
                breakpoints: {
                    640: {
                        slidesPerView: 4,
                    },
                    1024: {
                        slidesPerView: 5,
                    }
                }
            });
        }

        // Initialize main swiper
        this.mainSwiper = new Swiper(mainSwiperEl, {
            modules: [Navigation, Autoplay, Thumbs],
            loop: true,
            slidesPerView: 1,
            spaceBetween: 10,
            grabCursor: true,
            centeredSlides: true,
            autoplay: {
                delay: 5000,
                disableOnInteraction: false,
            },
            lazy: {
                loadPrevNext: true,
            },
            navigation: {
                nextEl: '.swiper-button-next',
                prevEl: '.swiper-button-prev',
            },
            thumbs: {
                swiper: this.thumbSwiper
            },
            breakpoints: {
                640: {
                    slidesPerView: 1,
                },
                1024: {
                    slidesPerView: 1,
                }
            },
            on: {
                slideChange: function() {
                    if (this.thumbSwiper) {
                        this.thumbSwiper.slideTo(this.realIndex);
                    }
                }.bind(this)
            }
        });

        // Handle thumbnail click
        if (this.thumbSwiper) {
            this.thumbSwiper.on('click', (swiper, event) => {
                const clickedSlide = event.target.closest('.swiper-slide');
                if (clickedSlide) {
                    const index = [...clickedSlide.parentElement.children].indexOf(clickedSlide);
                    this.mainSwiper.slideTo(index);
                }
            });
        }

        // Update slide counter
        const updateSlideCounter = (swiper) => {
            const currentSlide = document.querySelector('.current-slide');
            if (currentSlide) {
                currentSlide.textContent = swiper.realIndex + 1;
            }
        };

        // Initial update
        updateSlideCounter(this.mainSwiper);

        // Update on slide change
        this.mainSwiper.on('slideChange', () => {
            updateSlideCounter(this.mainSwiper);
        });
    }

    initSmoothScroll() {
        document.querySelectorAll('a[href^="#contact-form"]').forEach(anchor => {
            anchor.addEventListener('click', (e) => this.handleContactFormLinkClick(e, anchor));
        });
    }

    handleContactFormLinkClick(e, anchor) {
        e.preventDefault();
        const target = document.querySelector(anchor.getAttribute('href'));
        
        if (!target) return;
        
        target.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
        });

        // Add focus to the first form field for better accessibility
        const firstInput = target.querySelector('input, textarea');
        if (firstInput) {
            setTimeout(() => firstInput.focus(), 500);
        }
    }

    initImageHandlers() {
        this.setupImageErrorHandling();
        this.setupLazyLoading();
    }

    setupImageErrorHandling() {
        document.querySelectorAll('img').forEach(img => {
            img.addEventListener('error', () => this.handleImageError(img));
        });
    }

    handleImageError(img) {
        if (img.getAttribute('data-fallback')) {
            img.src = img.getAttribute('data-fallback');
        } else if (img.src !== '/images/placeholder.webp') {
            img.src = '/images/placeholder.webp';
        }
    }

    setupLazyLoading() {
        document.querySelectorAll('img:not([loading])').forEach(img => {
            img.loading = 'lazy';
        });
    }

    initFAQAccordion() {
        const faqQuestions = document.querySelectorAll('.faq-question');
        
        faqQuestions.forEach(question => {
            question.addEventListener('click', () => {
                const answer = question.nextElementSibling;
                const icon = question.querySelector('svg');
                const isExpanded = answer.classList.contains('hidden');
                
                // Close all other open FAQs
                document.querySelectorAll('.faq-answer').forEach(item => {
                    if (item !== answer) {
                        item.classList.add('hidden');
                    }
                });

                answer.classList.toggle('hidden');
                
                // Reset all icons
                document.querySelectorAll('.faq-question svg').forEach(svg => {
                    if (svg !== icon) {
                        svg.classList.remove('rotate-180');
                    }
                });
                
                if (isExpanded) {
                    question.querySelector('svg').classList.remove('rotate-180');
                } else {
                    question.querySelector('svg').classList.add('rotate-180');
                }
            });
        });
        
        // Initialize first FAQ as open by default
        if (faqQuestions.length > 0) {
            faqQuestions[0].click();
        }
    }

    initRelatedTemplatesSwiper() {
        const relatedTemplatesSwiperEl = document.querySelector('.related-templates-swiper');
        
        if (!relatedTemplatesSwiperEl) return;

        new Swiper(relatedTemplatesSwiperEl, {
            modules: [Navigation, Pagination],
            loop: false,
            slidesPerView: 1,
            spaceBetween: 20,
            navigation: {
                nextEl: '.related-templates-swiper .swiper-button-next',
                prevEl: '.related-templates-swiper .swiper-button-prev',
            },
            pagination: {
                el: '.related-templates-swiper .swiper-pagination',
                clickable: true,
            },
            breakpoints: {
                576: {
                    slidesPerView: 2,
                },
                768: {
                    slidesPerView: 3,
                },
                1024: {
                    slidesPerView: 4,
                }
            }
        });
    }

}

// Initialize the template show page when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    const templateShowPage = new TemplateShowPage();
    templateShowPage.init();
});
