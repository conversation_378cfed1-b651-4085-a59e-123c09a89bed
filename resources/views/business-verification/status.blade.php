@extends('layouts.default')

@section('title', __('messages.erc.verification_status'))

@php
    /** @var \App\Models\BusinessVerification $verification */
@endphp

@section('content')
<div class="container mx-auto px-4 py-8">
    <div class="max-w-4xl mx-auto">
        <div class="bg-white rounded-lg shadow-lg p-6">
            <h1 class="text-2xl font-bold text-gray-900 mb-6">
                {{ __('messages.erc.business_verification_status') }}
            </h1>

            <!-- Status Card -->
            <div class="bg-gray-50 rounded-lg p-6 mb-6">
                <div class="flex items-center justify-between mb-4">
                    <h2 class="text-lg font-semibold text-gray-900">
                        {{ __('messages.erc.current_status') }}
                    </h2>
                    <span class="px-3 py-1 rounded-full text-sm font-medium {{ $verification->status_badge_class }}">
                        {{ $verification->status_label }}
                    </span>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                    <div>
                        <p class="text-sm text-gray-600">{{ __('messages.erc.business_name') }}</p>
                        <p class="font-medium">{{ $customer->business_name }}</p>
                    </div>
                    <div>
                        <p class="text-sm text-gray-600">{{ __('messages.erc.tax_code') }}</p>
                        <p class="font-medium">{{ $customer->tax_code ?? __('messages.erc.not_provided') }}</p>
                    </div>
                    <div>
                        <p class="text-sm text-gray-600">{{ __('messages.erc.segment') }}</p>
                        <p class="font-medium">{{ $verification->segment_label }}</p>
                    </div>
                </div>

                @if($verification->submitted_at)
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <p class="text-sm text-gray-600">{{ __('messages.erc.submitted_at') }}</p>
                        <p class="font-medium">{{ $verification->submitted_at->format('d/m/Y H:i') }}</p>
                    </div>
                    @if($verification->verified_at)
                    <div>
                        <p class="text-sm text-gray-600">{{ __('messages.erc.verified_at') }}</p>
                        <p class="font-medium">{{ $verification->verified_at->format('d/m/Y H:i') }}</p>
                    </div>
                    @endif
                </div>
                @endif
            </div>

            <!-- Action based on status -->
            @if($verification->status->canUploadFile())
                <!-- Upload Section -->
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-6">
                    <div class="flex items-start">
                        <div class="flex-shrink-0">
                            <svg class="h-6 w-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                        </div>
                        <div class="ml-3 flex-1">
                            <h3 class="text-lg font-medium text-blue-900">
                                {{ __('messages.erc.upload_required') }}
                            </h3>
                            <p class="mt-2 text-blue-700">
                                {{ __('messages.erc.upload_description') }}
                            </p>
                            <div class="mt-4">
                                <a href="{{ route('business-verification.upload') }}" 
                                   class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                    <svg class="mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                                    </svg>
                                    {{ __('messages.erc.upload_erc_now') }}
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            @elseif($verification->status->value === 'submitted' || $verification->status->value === 'in_review')
                <!-- Under Review Section -->
                <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-6 mb-6">
                    <div class="flex items-start">
                        <div class="flex-shrink-0">
                            <svg class="h-6 w-6 text-yellow-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-lg font-medium text-yellow-900">
                                {{ __('messages.erc.under_review') }}
                            </h3>
                            <p class="mt-2 text-yellow-700">
                                {{ __('messages.erc.review_in_progress') }}
                            </p>
                            @if($verification->erc_file_name)
                            <p class="mt-2 text-sm text-yellow-600">
                                {{ __('messages.erc.uploaded_file') }}: {{ $verification->erc_file_name }}
                            </p>
                            @endif
                        </div>
                    </div>
                </div>
            @elseif($verification->status->value === 'verified')
                <!-- Verified Section -->
                <div class="bg-green-50 border border-green-200 rounded-lg p-6 mb-6">
                    <div class="flex items-start">
                        <div class="flex-shrink-0">
                            <svg class="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-lg font-medium text-green-900">
                                {{ __('messages.erc.verification_complete') }}
                            </h3>
                            <p class="mt-2 text-green-700">
                                {{ __('messages.erc.verification_success_message') }}
                            </p>
                        </div>
                    </div>
                </div>
            @elseif($verification->status->value === 'rejected')
                <!-- Rejected Section -->
                <div class="bg-red-50 border border-red-200 rounded-lg p-6 mb-6">
                    <div class="flex items-start">
                        <div class="flex-shrink-0">
                            <svg class="h-6 w-6 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                            </svg>
                        </div>
                        <div class="ml-3 flex-1">
                            <h3 class="text-lg font-medium text-red-900">
                                {{ __('messages.erc.verification_rejected') }}
                            </h3>
                            @if($verification->rejection_reason)
                            <div class="mt-2 p-3 bg-red-100 rounded-md">
                                <p class="text-sm text-red-800">
                                    <strong>{{ __('messages.erc.rejection_reason') }}:</strong>
                                    {{ $verification->rejection_reason }}
                                </p>
                            </div>
                            @endif
                            <p class="mt-2 text-red-700">
                                {{ __('messages.erc.resubmit_instruction') }}
                            </p>
                            <div class="mt-4">
                                <a href="{{ route('business-verification.upload') }}" 
                                   class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                                    {{ __('messages.erc.resubmit_erc') }}
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            @endif

            <!-- File Information -->
            @if($verification->hasFile())
            <div class="bg-gray-50 rounded-lg p-4">
                <h4 class="font-medium text-gray-900 mb-2">{{ __('messages.erc.uploaded_file_info') }}</h4>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                    <div>
                        <span class="text-gray-600">{{ __('messages.erc.file_name') }}:</span>
                        <span class="ml-1 font-medium">{{ $verification->erc_file_name }}</span>
                    </div>
                    <div>
                        <span class="text-gray-600">{{ __('messages.erc.file_size') }}:</span>
                        <span class="ml-1 font-medium">{{ number_format($verification->erc_file_size / 1024, 2) }} KB</span>
                    </div>
                    <div>
                        <a href="{{ route('business-verification.download') }}" 
                           class="text-blue-600 hover:text-blue-800 font-medium">
                            {{ __('messages.erc.download_file') }}
                        </a>
                    </div>
                </div>
            </div>
            @endif
        </div>
    </div>
</div>
@endsection
