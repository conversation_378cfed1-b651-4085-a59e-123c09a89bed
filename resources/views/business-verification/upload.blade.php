@extends('layouts.default')

@section('title', __('messages.erc.upload_erc'))

@section('content')
<div class="container mx-auto px-4 py-8">
    <div class="max-w-2xl mx-auto">
        <div class="bg-white rounded-lg shadow-lg p-6">
            <div class="mb-6">
                <h1 class="text-2xl font-bold text-gray-900 mb-2">
                    {{ __('messages.erc.upload_erc_title') }}
                </h1>
                <p class="text-gray-600">
                    {{ __('messages.erc.upload_erc_description') }}
                </p>
            </div>

            <!-- Requirements -->
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                <h3 class="font-medium text-blue-900 mb-2">
                    {{ __('messages.erc.file_requirements_title') }}
                </h3>
                <ul class="text-sm text-blue-800 space-y-1">
                    <li>• {{ __('messages.erc.requirement_format') }}</li>
                    <li>• {{ __('messages.erc.requirement_size') }}</li>
                    <li>• {{ __('messages.erc.requirement_quality') }}</li>
                    <li>• {{ __('messages.erc.requirement_validity') }}</li>
                </ul>
            </div>

            <!-- Upload Form -->
            <form id="erc-upload-form" enctype="multipart/form-data">
                @csrf
                <div class="mb-6">
                    <label for="erc_file" class="block text-sm font-medium text-gray-700 mb-2">
                        {{ __('messages.erc.select_file') }}
                    </label>
                    <div class="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md hover:border-gray-400 transition-colors">
                        <div class="space-y-1 text-center">
                            <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                                <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                            </svg>
                            <div class="flex text-sm text-gray-600">
                                <label for="erc_file" class="relative cursor-pointer bg-white rounded-md font-medium text-blue-600 hover:text-blue-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-blue-500">
                                    <span>{{ __('messages.erc.upload_file') }}</span>
                                    <input id="erc_file" name="erc_file" type="file" class="sr-only" accept=".jpg,.jpeg,.png,.pdf" required>
                                </label>
                                <p class="pl-1">{{ __('messages.erc.or_drag_drop') }}</p>
                            </div>
                            <p class="text-xs text-gray-500">
                                PNG, JPG, PDF {{ __('messages.erc.up_to_10mb') }}
                            </p>
                        </div>
                    </div>
                    <div id="file-info" class="mt-2 hidden">
                        <div class="flex items-center text-sm text-gray-600">
                            <svg class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                            </svg>
                            <span id="file-name"></span>
                            <span id="file-size" class="ml-2 text-gray-400"></span>
                        </div>
                    </div>
                </div>

                <!-- Error Messages -->
                <div id="error-messages" class="hidden mb-4">
                    <div class="bg-red-50 border border-red-200 rounded-md p-4">
                        <div class="flex">
                            <div class="flex-shrink-0">
                                <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                                </svg>
                            </div>
                            <div class="ml-3">
                                <h3 class="text-sm font-medium text-red-800">
                                    {{ __('messages.erc.upload_errors') }}
                                </h3>
                                <div id="error-list" class="mt-2 text-sm text-red-700">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Submit Button -->
                <div class="flex items-center justify-between">
                    <a href="{{ route('business-verification.status') }}" 
                       class="text-gray-600 hover:text-gray-800">
                        ← {{ __('messages.erc.back_to_status') }}
                    </a>
                    <button type="submit" id="submit-btn" 
                            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed">
                        <svg id="loading-spinner" class="hidden animate-spin -ml-1 mr-3 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <span id="submit-text">{{ __('messages.erc.upload_and_submit') }}</span>
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('erc-upload-form');
    const fileInput = document.getElementById('erc_file');
    const fileInfo = document.getElementById('file-info');
    const fileName = document.getElementById('file-name');
    const fileSize = document.getElementById('file-size');
    const errorMessages = document.getElementById('error-messages');
    const errorList = document.getElementById('error-list');
    const submitBtn = document.getElementById('submit-btn');
    const loadingSpinner = document.getElementById('loading-spinner');
    const submitText = document.getElementById('submit-text');

    // File input change handler
    fileInput.addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            fileName.textContent = file.name;
            fileSize.textContent = `(${(file.size / 1024 / 1024).toFixed(2)} MB)`;
            fileInfo.classList.remove('hidden');
            errorMessages.classList.add('hidden');
        } else {
            fileInfo.classList.add('hidden');
        }
    });

    // Form submit handler
    form.addEventListener('submit', function(e) {
        e.preventDefault();
        
        const formData = new FormData(form);
        
        // Show loading state
        submitBtn.disabled = true;
        loadingSpinner.classList.remove('hidden');
        submitText.textContent = '{{ __("messages.erc.uploading") }}';
        errorMessages.classList.add('hidden');

        fetch('{{ route("business-verification.store") }}', {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Success - redirect to status page
                window.location.href = data.redirect_url;
            } else {
                // Show errors
                showErrors(data.errors || [data.message]);
            }
        })
        .catch(error => {
            console.error('Upload error:', error);
            showErrors(['{{ __("messages.erc.upload_failed") }}']);
        })
        .finally(() => {
            // Reset loading state
            submitBtn.disabled = false;
            loadingSpinner.classList.add('hidden');
            submitText.textContent = '{{ __("messages.erc.upload_and_submit") }}';
        });
    });

    function showErrors(errors) {
        errorList.innerHTML = '';
        if (Array.isArray(errors)) {
            errors.forEach(error => {
                const li = document.createElement('li');
                li.textContent = error;
                errorList.appendChild(li);
            });
        } else {
            errorList.textContent = errors;
        }
        errorMessages.classList.remove('hidden');
    }

    // Drag and drop functionality
    const dropZone = document.querySelector('.border-dashed');
    
    ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
        dropZone.addEventListener(eventName, preventDefaults, false);
    });

    function preventDefaults(e) {
        e.preventDefault();
        e.stopPropagation();
    }

    ['dragenter', 'dragover'].forEach(eventName => {
        dropZone.addEventListener(eventName, highlight, false);
    });

    ['dragleave', 'drop'].forEach(eventName => {
        dropZone.addEventListener(eventName, unhighlight, false);
    });

    function highlight(e) {
        dropZone.classList.add('border-blue-500', 'bg-blue-50');
    }

    function unhighlight(e) {
        dropZone.classList.remove('border-blue-500', 'bg-blue-50');
    }

    dropZone.addEventListener('drop', handleDrop, false);

    function handleDrop(e) {
        const dt = e.dataTransfer;
        const files = dt.files;
        
        if (files.length > 0) {
            fileInput.files = files;
            fileInput.dispatchEvent(new Event('change'));
        }
    }
});
</script>
@endpush
@endsection
