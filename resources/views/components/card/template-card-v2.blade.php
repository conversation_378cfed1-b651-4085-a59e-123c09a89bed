@props([
    'template' => null,
    'showFavoriteButton' => true,
    'showCategory' => true,
    'showDescription' => true,
    'showActions' => true,
    'class' => '',
])

@php
    $templateId = $template->id ?? null;
    $templateName = $template->name ?? '';
    $templateSlug = $template->slug ?? '';
    $templateDescription = $template->description ?? '';
    $templateImage = $template->featured_image_url ?? null;
    $category = $template->category ?? null;
    $demoUrl = $template->demo_url ?? null;
    $isFavorited = $template->is_favorited ?? false;
@endphp

<div {{ $attributes->merge(['class' => "bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300 {$class}"]) }}>
    <!-- Template Image -->
    <div class="relative aspect-video bg-gray-200">
        @if($templateImage)
            <img src="{{ $templateImage }}" 
                 alt="{{ $templateName }}"
                 class="w-full h-full object-cover">
        @else
            <div class="w-full h-full flex items-center justify-center text-gray-400">
                <svg class="w-12 h-12" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                </svg>
            </div>
        @endif
        
        @if($showFavoriteButton)
            <!-- Favorite Button -->
            <div class="absolute top-3 right-3">
                <x-ui.favorite-button 
                    :template="$template"
                    size="md"
                    position="relative"
                    variant="default"
                    :showTooltip="true"
                />
            </div>
        @endif
    </div>

    <!-- Template Info -->
    <div class="p-4">
        <a href="{{ route('templates.show', $templateSlug) }}">
            <h3 class="font-semibold text-gray-900 mb-2 line-clamp-2">{{ $templateName }}</h3>

            @if($showCategory && $category)
                <span class="inline-block px-2 py-1 text-xs font-medium bg-emerald-100 text-emerald-800 rounded-full mb-3">
                    {{ $category->name }}
                </span>
            @endif

            @if($showDescription && $templateDescription)
                <p class="text-sm text-gray-600 mb-3 line-clamp-2">{{ $templateDescription }}</p>
            @endif
        </a>
        <!-- Actions -->
        @if($showActions)
            <div class="flex items-center justify-between">
                <div class="flex space-x-2">
                    @if($demoUrl)
                        <a href="{{ route('demo.show', $templateSlug) }}"
                           class="inline-flex items-center px-3 py-1.5 text-xs font-medium text-blue-600 bg-blue-50 rounded-md hover:bg-blue-100 transition-colors">
                            <svg class="w-5 h-5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
                            </svg>
                            {{ __('messages.template.preview') }}
                        </a>
                    @endif
                    
                    <a href="{{ route('templates.show', $templateSlug) }}"
                       class="inline-flex items-center px-3 py-1.5 text-xs font-medium text-emerald-600 bg-emerald-50 rounded-md hover:bg-emerald-100 transition-colors">
                        <svg class="w-5 h-5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                        </svg>
                        {{ __('messages.template.details') }}
                    </a>
                </div>
            </div>
        @endif
    </div>
</div>
