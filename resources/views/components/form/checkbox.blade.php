<div class="group flex items-center">
    <div class="relative h-4 w-4 flex-shrink-0">
        <input id="{{ $id }}" name="{{ $name }}" type="checkbox" value="{{ $value }}"
            @if (old($name, $checked)) checked @endif
            {{ $attributes->class([
                'absolute h-4 w-4 cursor-pointer appearance-none rounded-sm border-2 border-gray-300 transition-colors duration-200',
                'checked:border-emerald-600 checked:bg-emerald-600 checked:hover:bg-emerald-700',
                'hover:border-emerald-500 focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-emerald-600',
                'border-red-500' => ($errors ?? app(\Illuminate\Support\ViewErrorBag::class))->has($name),
                'peer' => true,
            ]) }}
            
            <!-- Checkmark icon -->
            <div class="absolute inset-0 flex items-center justify-center pointer-events-none opacity-0 peer-checked:opacity-100 transition-opacity duration-200">
                <svg class="h-3 w-3 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="3" d="M5 13l4 4L19 7" />
                </svg>
            </div>
        </div>

    <label for="{{ $id }}" class="ml-1.5 block text-sm font-medium leading-6 text-gray-900">
        {{ $label }}
    </label>
</div>
