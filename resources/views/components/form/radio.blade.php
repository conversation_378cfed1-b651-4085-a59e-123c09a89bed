<div class="flex items-center">
    <input
        id="{{ $id }}"
        name="{{ $name }}"
        type="radio"
        value="{{ $value }}"
        @if(old($name) == $value || (!old($name) && $checked))
            checked
        @endif
        {{ $attributes->class([
            'relative size-4 appearance-none rounded-full border border-gray-300 bg-white',
            'before:absolute before:inset-1 before:rounded-full not-checked:before:hidden before:bg-emerald-600',
            'checked:border-emerald-600 checked:bg-emerald-600',
            'focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-emerald-600',
            'border-red-500' => ($errors ?? app(\Illuminate\Support\ViewErrorBag::class))->has($name)
        ]) }}
    />
    <label for="{{ $id }}" class="ml-3 block text-sm/6 font-medium text-gray-900">
        {{ $label }}
    </label>
</div>
