<select id="{{ $id }}" name="{{ $name }}"
    {{ $attributes->class([
        'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 block w-full p-2.5',
        'border-red-500 focus:border-red-500 focus:ring-red-500' => ($errors ?? app(\Illuminate\Support\ViewErrorBag::class))->has($name),
    ]) }}>
    <option value="">-- Select an option --</option>
    @foreach ($options as $key => $value)
        <option value="{{ $key }}" @if ((string) $key === (string) old($name, $selected)) selected @endif>
            {{ $value }}
        </option>
    @endforeach
</select>
