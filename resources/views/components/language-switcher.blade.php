@props([
    'currentLocale' => app()->getLocale(),
    'availableLocales' => [
        'vi' => __('messages.language.vi'),
        'en' => __('messages.language.en')
    ],
    'flags' => [
        'vi' => '🇻🇳',
        'en' => '🇬🇧'
    ]
])

<div x-data="{ open: false }" class="relative inline-block text-left" @keydown.escape="open = false">
    <div>
        <button
            @click="open = !open"
            type="button"
            class="flex items-center justify-between w-full px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-emerald-500"
            id="language-menu"
            :aria-expanded="open ? 'true' : 'false'"
            :aria-label="'{{ __('messages.language.switch') }}'"
            x-bind:title="'{{ __('messages.language.current') }}: ' + '{{ $availableLocales[$currentLocale] }}'">
            <div class="flex items-center">
                <span class="text-lg mr-2" aria-hidden="true">{{ $flags[$currentLocale] }}</span>
                <span>{{ $availableLocales[$currentLocale] }}</span>
            </div>
            <svg class="w-5 h-5 ml-2 text-gray-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
            </svg>
        </button>
    </div>

    <div x-show="open"
         @click.away="open = false"
         x-transition:enter="transition ease-out duration-100"
         x-transition:enter-start="transform opacity-0 scale-95"
         x-transition:enter-end="transform opacity-100 scale-100"
         x-transition:leave="transition ease-in duration-75"
         x-transition:leave-start="transform opacity-100 scale-100"
         x-transition:leave-end="transform opacity-0 scale-95"
         class="absolute right-0 z-50 w-48 bottom-12 origin-top-right bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none"
         role="menu"
         aria-orientation="vertical"
         aria-labelledby="language-menu"
         style="display: none;"
         x-cloak>
        <div class="py-1" role="none">
            @foreach($availableLocales as $locale => $name)
                @if($locale !== $currentLocale)
                    <a href="{{ route('language.switch', $locale) }}"
                       class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900"
                       role="menuitem"
                       hx-get="{{ route('language.switch', $locale) }}"
                       hx-push-url="true"
                       hx-target="body"
                       hx-swap="outerHTML"
                       @click="open = false">
                        <span class="mr-2 text-lg" aria-hidden="true">{{ $flags[$locale] }}</span>
                        <span>{{ $name }}</span>
                    </a>
                @else
                    <span class="flex items-center px-4 py-2 text-sm font-medium text-emerald-600 bg-emerald-50">
                        <span class="mr-2 text-lg" aria-hidden="true">{{ $flags[$locale] }}</span>
                        <span>{{ $name }}</span>
                    </span>
                @endif
            @endforeach
        </div>
    </div>
</div>
