<div
    x-data="Object.assign(trialModal())"
    x-show="$store.trialModal.isOpen"
    x-init="
        $watch('$store.trialModal.isOpen', value => {
            if (value && formSubmitted) {
                resetForm();
            }
        });
    "
    class="fixed inset-0 z-50 flex items-center justify-center p-4"
    style="display: none;"
>
    <div
        x-show="$store.trialModal.isOpen"
        x-transition:enter="ease-out duration-300"
        x-transition:enter-start="opacity-0"
        x-transition:enter-end="opacity-100"
        x-transition:leave="ease-in duration-200"
        x-transition:leave-start="opacity-100"
        x-transition:leave-end="opacity-0"
        class="fixed inset-0 bg-black/60 backdrop-blur-sm"
    ></div>

    <!-- Modal Content -->
    <div
        x-show="$store.trialModal.isOpen"
        x-transition:enter="ease-out duration-300"
        x-transition:enter-start="opacity-0 scale-95"
        x-transition:enter-end="opacity-100 scale-100"
        x-transition:leave="ease-in duration-200"
        x-transition:leave-start="opacity-100 scale-100"
        x-transition:leave-end="opacity-0 scale-95"
        class="relative z-10 w-full max-w-2xl bg-white rounded-2xl shadow-xl overflow-hidden"
    >
        <!-- Form section -->
        <div x-show="!formSubmitted">
            <!-- Header -->
            <div class="flex items-start justify-between p-5 border-b border-gray-200 rounded-t">
                <div>
                    <h3 class="text-xl font-bold text-gray-900" x-text="currentTitle()"></h3>
                    <p class="text-sm text-gray-500 mt-1">{{ __('messages.modal.trial_subtitle') }}</p>
                </div>
                <!-- Close button with popover confirm -->
                <div class="relative">
                    <button @click="showClosePopover = true" type="button" class="text-gray-400 bg-transparent hover:bg-gray-200 hover:cursor-pointer hover:text-gray-900 rounded-lg text-sm p-1.5 ml-auto inline-flex items-center">
                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path></svg>
                    </button>
                    <!-- Popover confirm -->
                    <div x-show="showClosePopover" @click.away="showClosePopover = false" class="absolute right-0 mt-2 w-80 bg-white border border-gray-200 rounded-xl shadow-lg z-50 p-4" style="min-width:260px;">
                        <div class="text-gray-900 font-semibold mb-2">Bạn vẫn muốn đóng?</div>
                        <div class="text-gray-700 text-sm mb-4">Chỉ cần một vài bước đơn giản, bạn sẽ có 1 website như mơ, bạn vẫn muốn đóng?</div>
                        <div class="flex justify-end gap-2">
                            <button @click="$store.trialModal.close(); showClosePopover = false" class="px-3 py-1 bg-emerald-600 text-white rounded hover:cursor-pointer hover:bg-emerald-700 text-sm">Yes</button>
                            <button @click="showClosePopover = false" class="px-3 py-1 bg-gray-200 text-gray-700 rounded hover:bg-gray-300 hover:cursor-pointer text-sm">No</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Progress bar -->
            <div class="p-5">
                <div class="w-full bg-gray-200 rounded-full h-2">
                    <div class="bg-emerald-600 h-2 rounded-full transition-all duration-500" :style="`width: ${(100/3)*step}%`"></div>
                </div>
            </div>

            <!-- Form steps -->
            <form class="p-6 pt-0 space-y-6 max-h-[60vh] overflow-y-auto" autocomplete="off">
                <!-- Step 1: Basic Info -->
                <div x-show.transition.in.opacity.duration.300="step === 1">
                    <div class="space-y-4">
                        <div>
                            <label for="name" class="block mb-2 text-sm font-medium text-gray-900">{{ __('messages.modal.name_label') }} <span class="text-red-500">*</span></label>
                            <x-form.input 
                                name="name" 
                                x-model="formData.name" 
                                x-on:blur="validateField('name')"
                                ::class="{'border-red-500 focus:ring-red-500': hasError('name')}"
                                placeholder="{{ __('messages.modal.name_placeholder') }}"
                                required 
                            />
                            <p x-show="hasError('name')" x-text="getErrorMessage('name')" class="mt-1 text-sm text-red-600"></p>
                        </div>
                        <div>
                            <label for="phone" class="block mb-2 text-sm font-medium text-gray-900">Số điện thoại <span class="text-red-500">*</span></label>
                            <x-form.input 
                                name="phone" 
                                x-model="formData.phone" 
                                x-on:blur="validateField('phone')"
                                ::class="{'border-red-500 focus:ring-red-500': hasError('phone')}"
                                placeholder="0989 123 456" 
                                required 
                                type="tel" 
                            />
                            <p x-show="hasError('phone')" x-text="getErrorMessage('phone')" class="mt-1 text-sm text-red-600"></p>
                        </div>
                        <div>
                            <label for="company" class="block mb-2 text-sm font-medium text-gray-900">Tên công ty <span class="text-red-500">*</span></label>
                            <x-form.input 
                                name="company" 
                                x-model="formData.company" 
                                x-on:blur="validateField('company')"
                                ::class="{'border-red-500 focus:ring-red-500': hasError('company')}"
                                placeholder="Công ty TNHH CSlant Solutions/Cá nhân"
                                required 
                                type="text" 
                            />
                            <p x-show="hasError('company')" x-text="getErrorMessage('company')" class="mt-1 text-sm text-red-600"></p>
                        </div>
                        <div>
                            <label for="email" class="block mb-2 text-sm font-medium text-gray-900">Email <span class="text-red-500">*</span></label>
                            <x-form.input 
                                name="email" 
                                x-model="formData.email" 
                                x-on:blur="validateField('email')"
                                x-on:input.debounce.500ms="validateField('email')"
                                ::class="{'border-red-500 focus:ring-red-500': hasError('email')}"
                                placeholder="<EMAIL>" 
                                required 
                                type="email" 
                                autocomplete="email"
                            />
                            <p x-show="hasError('email')" x-text="getErrorMessage('email')" class="mt-1 text-sm text-red-600"></p>
                        </div>
                    </div>
                </div>

                <!-- Step 2: Customer Needs -->
                <div x-show.transition.in.opacity.duration.300="step === 2">
                    <div class="space-y-6">
                        <div>
                            <h4 class="mb-3 text-sm font-medium text-gray-900">Gói đăng ký <span class="text-red-500">*</span></h4>
                            <div class="grid grid-cols-1 sm:grid-cols-2 gap-3">
                                @php
                                    $packages = [
                                        ['package' => 'basic', 'name' => 'Express Web Basic', 'helper' => '1.500.000 đ'],
                                        ['package' => 'plus', 'name' => 'Express Web Plus', 'helper' => '3.000.000 đ'],
                                        ['package' => 'ultimate', 'name' => 'Express Web Ultimate', 'helper' => '5.000.000 đ'],
                                    ];
                                @endphp
                                @foreach($packages as $index => $package)
                                    <label class="hover:bg-gray-200 p-2 bg-gray-100 rounded-xl hover:cursor-pointer border-2 transition-colors duration-200" 
                                           :class="{ 'bg-gray-200 border-emerald-500': formData.package === '{{$package['package']}}', 'border-transparent': formData.package !== '{{$package['package']}}', 'border-red-500': hasError('package') }">
                                        <x-form.radio
                                            x-model="formData.package"
                                            @change="validateField('package')"
                                            value="{{ $package['package'] }}"
                                            id="chk-packages-{{ $index }}"
                                            label="{{ $package['name'] }}"
                                        />
                                        <p class="text-sm text-gray-500 pl-7"><span class="line-through">{{ $package['helper'] }}</span> Miễn phí dùng thử 14 ngày</p>
                                    </label>
                                @endforeach
                            </div>
                            <p x-show="hasError('package')" x-text="getErrorMessage('package')" class="mt-1 text-sm text-red-600"></p>
                        </div>

                        <div>
                            <label for="subdomain" class="block mb-2 text-sm font-medium text-gray-900">Tên miền website <span class="text-red-500">*</span></label>
                            <div class="flex">
                                <div class="flex-grow">
                                    <x-form.input 
                                        name="subdomain" 
                                        x-model="formData.subdomain" 
                                        x-on:blur="validateField('subdomain')"
                                        x-on:input.debounce.500ms="validateField('subdomain')"
                                        ::class="{'border-red-500 focus:ring-red-500': hasError('subdomain')}"
                                        placeholder="congty" 
                                        required 
                                        autocomplete="off"
                                    />
                                </div>
                                <div class="flex items-center px-2"><span>.identifynation.com</span></div>
                            </div>
                            <p x-show="hasError('subdomain')" x-text="getErrorMessage('subdomain')" class="mt-1 text-sm text-red-600"></p>
                        </div>

                        <hr class="border-0 border-b border-b-gray-200" />

                        <div>
                            <h4 class="mb-3 text-sm font-medium text-gray-900">Mục tiêu chính của website là gì?</h4>
                            <div class="grid grid-cols-1 sm:grid-cols-2 gap-3">
                                @php
                                    $purposes = [
                                        'Tăng nhận diện thương hiệu',
                                        'Tìm kiếm khách hàng tiềm năng',
                                        'Bán hàng trực tuyến',
                                        'Cung cấp thông tin',
                                        'Mục tiêu khác',
                                    ];
                                @endphp
                                @foreach($purposes as $index => $purpose)
                                    <x-form.radio
                                        x-model="formData.purpose"
                                        value="{{ $purpose }}"
                                        id="chk-purposes-trial-{{ $index }}"
                                        label="{{ $purpose }}"
                                    />
                                @endforeach
                            </div>
                        </div>

                        <div>
                            <h4 class="mb-3 text-sm font-medium text-gray-900">Mô tả khác</h4>
                            <div>
                                <x-form.textarea
                                    name="details"
                                    rows="4"
                                    x-model="formData.details"
                                    x-on:input="validateField('details')"
                                    ::class="{'border-red-500 focus:ring-red-500': hasError('details')}"
                                    placeholder="Hãy chia sẻ thêm về lĩnh vực kinh doanh, website bạn thích, hoặc các tính năng bạn mong muốn..."
                                    class="w-full"
                                />
                                <p x-show="hasError('details')" x-text="getErrorMessage('details')" class="mt-1 text-sm text-red-600"></p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Step 3: Summary -->
                <div x-show.transition.in.opacity.duration.300="step === 3">
                    <div class="space-y-6">
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <div class="overflow-x-auto">
                                <table class="min-w-full divide-y divide-gray-200">
                                    <tbody class="bg-white divide-y divide-gray-200">
                                        <tr>
                                            <td class="px-4 py-3 whitespace-nowrap font-medium text-gray-800" colspan="2">Thông tin Công ty</td>
                                        </tr> 
                                        <tr>
                                            <td class="px-4 py-3 whitespace-nowrap text-sm font-medium text-gray-500 w-1/3">Họ và tên</td>
                                            <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900" x-text="formData.name"></td>
                                        </tr>
                                        <tr>
                                            <td class="px-4 py-3 whitespace-nowrap text-sm font-medium text-gray-500">Số điện thoại</td>
                                            <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900" x-text="formData.phone"></td>
                                        </tr>
                                        <tr>
                                            <td class="px-4 py-3 whitespace-nowrap text-sm font-medium text-gray-500">Công ty</td>
                                            <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900" x-text="formData.company"></td>
                                        </tr>
                                        <tr>
                                            <td class="px-4 py-3 whitespace-nowrap text-sm font-medium text-gray-500">Email</td>
                                            <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900" x-text="formData.email"></td>
                                        </tr>
                                    </tbody>
                                </table>
                                <table class="min-w-full divide-y divide-gray-200 mt-4">
                                    <tbody class="bg-white divide-y divide-gray-200">
                                        <tr>
                                            <td class="px-4 py-3 whitespace-nowrap font-medium text-gray-800" colspan="2">Thông tin Website</td>
                                        </tr> 
                                        <tr>
                                            <td class="px-4 py-3 whitespace-nowrap text-sm font-medium text-gray-500 w-1/3">Gói đăng ký</td>
                                            <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900" x-text="formData.package"></td>
                                        </tr>
                                        <tr>
                                            <td class="px-4 py-3 whitespace-nowrap text-sm font-medium text-gray-500 w-1/3">Mẫu website</td>
                                            <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900"><span x-text="$store.trialModal.templateId"></span> - <span x-text="$store.trialModal.templateName"></span></td>
                                        </tr>
                                        <tr>
                                            <td class="px-4 py-3 whitespace-nowrap text-sm font-medium text-gray-500">Tên miền</td>
                                            <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900">
                                                <span x-text="formData.subdomain"></span>.identifynation.com
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="px-4 py-3 whitespace-nowrap text-sm font-medium text-gray-500">Mục tiêu website</td>
                                            <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900" x-text="formData.purpose || 'Không có'"></td>
                                        </tr>
                                        <tr x-show="formData.details">
                                            <td class="px-4 py-3 text-sm font-medium text-gray-500">Mô tả thêm</td>
                                            <td class="px-4 py-3 text-sm text-gray-900 whitespace-pre-line" x-text="formData.details"></td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        <div class="bg-blue-50 p-4 rounded-lg border border-blue-100">
                            <p class="text-sm text-blue-700">
                                <span class="font-medium">Lưu ý:</span> 
                                <ul class="text-sm text-blue-700">
                                    <li>- Vui lòng kiểm tra kỹ thông tin trước khi gửi. Bạn có thể quay lại các bước trước để chỉnh sửa nếu cần.</li>
                                    <li>- Thông tin và Mật khẩu quản trị website sẽ được gửi qua email.</li>
                                </ul>
                            </p>
                        </div>
                    </div>
                </div>
            </form>

            <!-- Footer navigation -->
            <div class="flex items-center justify-between p-6 border-t border-gray-200 rounded-b">
                <x-ui.button 
                    @click="prevStep()" 
                    x-show="step > 1" 
                    variant="secondary" 
                    class="hover:cursor-pointer"
                    type="button"
                >
                    Quay lại
                </x-ui.button>
                <div x-show="step === 1" class="flex-grow"></div>
                <x-ui.button 
                    @click="nextStep()" 
                    x-show="step < 3" 
                    class="hover:cursor-pointer"
                    type="button"
                    ::disabled="isSubmitting"
                    ::class="{'opacity-50 cursor-not-allowed': isSubmitting}"
                >
                    <span>Tiếp theo</span>
                    <span x-show="isSubmitting" class="flex items-center">
                        <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Đang xử lý...
                    </span>
                </x-ui.button>
                <x-ui.button 
                    @click="submitForm()" 
                    x-show="step === 3" 
                    type="button" 
                    class="hover:cursor-pointer"
                    ::disabled="isSubmitting"
                    ::class="{'opacity-50 cursor-not-allowed': isSubmitting}"
                >
                    <span x-show="!isSubmitting">Tạo website ngay</span>
                    <span x-show="isSubmitting" class="flex items-center">
                        <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Đang tạo website...
                    </span>
                </x-ui.button>
            </div>
        </div>

        <!-- Success message -->
        <div x-show="formSubmitted" class="p-8 text-center">
            <div class="flex justify-center items-center w-16 h-16 mx-auto bg-emerald-100 rounded-full">
                <svg class="w-10 h-10 text-emerald-600" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path></svg>
            </div>
            <h3 class="text-2xl font-bold text-gray-900 mt-5">{{ __('messages.modal.success_title') }}</h3>
            <p class="text-gray-600 mt-2">{{ __('messages.modal.success_message') }}</p>
            <!-- Close button uses the store. -->
            <button @click="$store.trialModal.close()" class="mt-6 text-white bg-emerald-600 hover:bg-emerald-700 focus:ring-4 focus:outline-none focus:ring-emerald-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center">
                Đóng
            </button>
        </div>
    </div>
</div>
