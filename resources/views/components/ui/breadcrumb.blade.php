@if (!empty($items))
<nav class="flex" aria-label="Breadcrumb">
    <ol role="list" class="flex items-center space-x-2 md:space-x-4">
        @foreach ($items as $item)
            <li>
                <div class="flex items-center">
                    @if (!$loop->first)
                        <svg class="size-5 shrink-0 text-gray-400" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                            <path fill-rule="evenodd" d="M8.22 5.22a.75.75 0 0 1 1.06 0l4.25 4.25a.75.75 0 0 1 0 1.06l-4.25 4.25a.75.75 0 0 1-1.06-1.06L11.94 10 8.22 6.28a.75.75 0 0 1 0-1.06Z" clip-rule="evenodd" />
                        </svg>
                    @endif

                    <{{ $item['url'] ?? '' ? 'a' : 'span'}}
                        href="{{ $item['url'] ?? '#' }}"
                        class="text-sm font-medium
                               @if ($loop->first)
                                   text-gray-400 hover:text-gray-500
                               @else
                                   ml-2 md:ml-4 text-gray-500 hover:text-gray-700
                               @endif"
                        @if ($loop->last)
                            aria-current="page"
                        @endif
                    >
                        @if ($loop->first)
                            <svg class="size-5 shrink-0" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                <path fill-rule="evenodd" d="M9.293 2.293a1 1 0 0 1 1.414 0l7 7A1 1 0 0 1 17 11h-1v6a1 1 0 0 1-1 1h-2a1 1 0 0 1-1-1v-3a1 1 0 0 0-1-1H9a1 1 0 0 0-1 1v3a1 1 0 0 1-1 1H5a1 1 0 0 1-1-1v-6H3a1 1 0 0 1-.707-1.707l7-7Z" clip-rule="evenodd" />
                            </svg>
                            <span class="sr-only">{{ $item['text'] ?? '' }}</span>
                        @else
                            {{ $item['text'] ?? '' }}
                        @endif
                    </{{ $item['url'] ?? '' ? 'a' : 'span'}}>
                </div>
            </li>
        @endforeach
    </ol>
</nav>
@endif
