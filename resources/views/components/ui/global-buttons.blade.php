<div x-data="globalButtons()"
     x-init="init()"
     class="fixed bottom-6 right-6 z-40 flex flex-col gap-3">

    <!-- Go to Top Button -->
    <button x-show="showScrollTop"
            x-transition:enter="transition ease-out duration-300"
            x-transition:enter-start="opacity-0 transform translate-y-2"
            x-transition:enter-end="opacity-100 transform translate-y-0"
            x-transition:leave="transition ease-in duration-200"
            x-transition:leave-start="opacity-100 transform translate-y-0"
            x-transition:leave-end="opacity-0 transform translate-y-2"
            @click="scrollToTop()"
            data-track-button="go-to-top"
            aria-label="{{ __('messages.ui.go_to_top') }}"
            class="group relative w-12 h-12 bg-gray-700 hover:bg-gray-800 dark:bg-gray-600 dark:hover:bg-gray-700 text-white rounded-full
            shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-700 dark:focus:ring-gray-500" style="cursor: pointer;">
        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" aria-hidden="true">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18"></path>
        </svg>
        <span class="sr-only">{{ __('Lên đầu trang') }}</span>
        <span class="absolute right-full mr-3 px-2 py-1 text-sm text-white bg-gray-800 dark:bg-gray-900 rounded-md opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap pointer-events-none" role="tooltip">
            {{ __('messages.ui.go_to_top') }}
        </span>
    </button>

    {{-- <!-- Zalo Button -->
    <a href="{{ $zaloUrl }}"
       target="_blank"
       rel="noopener noreferrer"
       data-track-button="zalo"
       aria-label="{{ __('messages.ui.chat_zalo') }}"
       class="group relative w-12 h-12 bg-blue-500 hover:bg-blue-600 dark:bg-blue-600 dark:hover:bg-blue-700 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-blue-400">
        <div class="relative">
            <div class="absolute -inset-1 bg-white/20 rounded-full opacity-0 group-hover:opacity-100 group-hover:animate-ping"></div>
            <div class="absolute -inset-0.5 bg-white/10 rounded-full opacity-0 group-hover:opacity-100 group-hover:animate-pulse"></div>
            <svg xmlns="http://www.w3.org/2000/svg"  viewBox="0 0 48 48" width="32px" height="32px"><path fill="#2962ff" d="M15,36V6.827l-1.211-0.811C8.64,8.083,5,13.112,5,19v10c0,7.732,6.268,14,14,14h10	c4.722,0,8.883-2.348,11.417-5.931V36H15z"/><path fill="#eee" d="M29,5H19c-1.845,0-3.601,0.366-5.214,1.014C10.453,9.25,8,14.528,8,19	c0,6.771,0.936,10.735,3.712,14.607c0.216,0.301,0.357,0.653,0.376,1.022c0.043,0.835-0.129,2.365-1.634,3.742	c-0.162,0.148-0.059,0.419,0.16,0.428c0.942,0.041,2.843-0.014,4.797-0.877c0.557-0.246,1.191-0.203,1.729,0.083	C20.453,39.764,24.333,40,28,40c4.676,0,9.339-1.04,12.417-2.916C42.038,34.799,43,32.014,43,29V19C43,11.268,36.732,5,29,5z"/><path fill="#2962ff" d="M36.75,27C34.683,27,33,25.317,33,23.25s1.683-3.75,3.75-3.75s3.75,1.683,3.75,3.75	S38.817,27,36.75,27z M36.75,21c-1.24,0-2.25,1.01-2.25,2.25s1.01,2.25,2.25,2.25S39,24.49,39,23.25S37.99,21,36.75,21z"/><path fill="#2962ff" d="M31.5,27h-1c-0.276,0-0.5-0.224-0.5-0.5V18h1.5V27z"/><path fill="#2962ff" d="M27,19.75v0.519c-0.629-0.476-1.403-0.769-2.25-0.769c-2.067,0-3.75,1.683-3.75,3.75	S22.683,27,24.75,27c0.847,0,1.621-0.293,2.25-0.769V26.5c0,0.276,0.224,0.5,0.5,0.5h1v-7.25H27z M24.75,25.5	c-1.24,0-2.25-1.01-2.25-2.25S23.51,21,24.75,21S27,22.01,27,23.25S25.99,25.5,24.75,25.5z"/><path fill="#2962ff" d="M21.25,18h-8v1.5h5.321L13,26h0.026c-0.163,0.211-0.276,0.463-0.276,0.75V27h7.5	c0.276,0,0.5-0.224,0.5-0.5v-1h-5.321L21,19h-0.026c0.163-0.211,0.276-0.463,0.276-0.75V18z"/></svg>
        </div>
        <span class="sr-only">{{ __('Chat Zalo') }}</span>
        <span class="absolute right-full mr-3 px-2 py-1 text-sm text-white bg-gray-800 dark:bg-gray-900 rounded-md opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap pointer-events-none" role="tooltip">
            {{ __('messages.ui.chat_zalo') }}
        </span>
    </a>

    <!-- Messenger Button -->
    <a href="{{ $messengerUrl }}"
       target="_blank"
       rel="noopener noreferrer"
       data-track-button="messenger"
       aria-label="{{ __('messages.ui.chat_messenger') }}"
       class="group relative w-12 h-12 bg-gradient-to-br from-blue-500 via-blue-600 to-purple-600 hover:from-blue-600 hover:via-blue-700 hover:to-purple-700 dark:from-blue-600 dark:via-blue-700 dark:to-purple-700 dark:hover:from-blue-700 dark:hover:via-blue-800 dark:hover:to-purple-800 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-blue-400">
        <div class="relative">
            <div class="absolute -inset-1 bg-white/20 rounded-full opacity-0 group-hover:opacity-100 group-hover:animate-ping"></div>
            <div class="absolute -inset-0.5 bg-white/10 rounded-full opacity-0 group-hover:opacity-100 group-hover:animate-pulse"></div>
            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" aria-hidden="true">
                <path d="M12 0C5.373 0 0 4.975 0 11.111c0 3.497 1.745 6.616 4.472 8.652V24l4.086-2.242c1.09.301 2.246.464 3.442.464 6.627 0 12-4.975 12-11.111C24 4.975 18.627 0 12 0zm1.193 14.963l-3.056-3.259-5.963 3.259L10.733 8l3.13 3.259L19.752 8l-6.559 6.963z"/>
            </svg>
        </div>
        <span class="sr-only">{{ __('Chat Messenger') }}</span>
        <span class="absolute right-full mr-3 px-2 py-1 text-sm text-white bg-gray-800 dark:bg-gray-900 rounded-md opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap pointer-events-none" role="tooltip">
            {{ __('messages.ui.chat_messenger') }}
        </span>
    </a> --}}

    <!-- Chat Widget -->
    <div class="group relative w-14 h-14">
        <x-ui.chat-widget />
    </div>
</div>

<script>
function globalButtons() {
    return {
        showScrollTop: false,

        init() {
            this.handleScroll();
            window.addEventListener('scroll', () => this.handleScroll());
        },

        handleScroll() {
            this.showScrollTop = window.scrollY > 300;
        },

        scrollToTop() {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        }
    }
}
</script>
