<x-mail::message>
# {{ __('messages.emails.email_verified.title') }}

{{ __('messages.emails.email_verified.greeting', ['name' => $user->name]) }}

{{ __('messages.emails.email_verified.message') }}

**{{ __('messages.emails.email_verified.verified_time') }}** {{ $verifiedAt }}

{{ __('messages.emails.email_verified.next_steps') }}

<x-mail::button :url="route('user-dashboard')">
{{ __('messages.emails.email_verified.dashboard_button') }}
</x-mail::button>

{{ __('messages.emails.email_verified.support_note') }}

{{ __('messages.emails.email_verified.thanks') }},<br>
{{ config('app.name') }}
</x-mail::message>
