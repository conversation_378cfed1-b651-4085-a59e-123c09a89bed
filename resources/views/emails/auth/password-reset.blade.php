<x-mail::message>
# {{ __('messages.emails.password_reset.title') }}

{{ __('messages.emails.password_reset.greeting') }},

{{ __('messages.emails.password_reset.message') }}

<x-mail::button :url="$resetUrl">
{{ __('messages.emails.password_reset.action') }}
</x-mail::button>

{{ __('messages.emails.password_reset.expiry_notice', ['minutes' => $count]) }}

{{ __('messages.emails.password_reset.ignore_notice') }}

{{ __('messages.emails.password_reset.regards') }},<br>
{{ config('app.name') }}
</x-mail::message>
