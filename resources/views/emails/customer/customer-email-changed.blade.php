<x-mail::message>
# {{ __('messages.email.customer_email_changed_subject') }}

{{ __('messages.email.customer_email_changed_greeting', ['business_name' => $customer->business_name]) }}

{{ __('messages.email.customer_email_changed_message') }}

- **{{ __('messages.email.customer_email_changed_old_email') }}** {{ $oldEmail }}
- **{{ __('messages.email.customer_email_changed_new_email') }}** {{ $customer->email }}
- **{{ __('messages.email.customer_email_changed_time') }}** {{ $changeTime }}

{{ __('messages.email.customer_email_changed_verification_note') }}

{{ __('messages.email.customer_email_changed_security_note') }}

<x-mail::button :url="route('home')">
{{ __('messages.email.customer_email_changed_login_button') }}
</x-mail::button>

{{ __('messages.email.customer_email_changed_thanks') }},<br>
{{ config('app.name') }}
</x-mail::message>
