<x-mail::message>
# {{ __('messages.erc.rejected_subject') }}

{{ __('messages.erc.rejected_greeting', ['name' => $customer->business_name]) }}

{{ __('messages.erc.rejected_message') }}

## {{ __('messages.erc.rejection_details') }}

- **{{ __('messages.erc.rejected_date') }}** {{ $rejectedAt->format('d/m/Y H:i') }}
- **{{ __('messages.erc.rejection_reason_title') }}**

<x-mail::panel>
{{ $rejectionReason }}
</x-mail::panel>

## {{ __('messages.erc.resubmission_guide') }}

{{ __('messages.erc.resubmission_intro') }}

### {{ __('messages.erc.checklist_title') }}

{{ __('messages.erc.checklist_intro') }}

@foreach($checklist as $item)
- ✅ {{ $item }}
@endforeach

### {{ __('messages.erc.common_issues') }}

{{ __('messages.erc.common_issues_intro') }}

- **{{ __('messages.erc.issue_1_title') }}** {{ __('messages.erc.issue_1_desc') }}
- **{{ __('messages.erc.issue_2_title') }}** {{ __('messages.erc.issue_2_desc') }}
- **{{ __('messages.erc.issue_3_title') }}** {{ __('messages.erc.issue_3_desc') }}
- **{{ __('messages.erc.issue_4_title') }}** {{ __('messages.erc.issue_4_desc') }}

## {{ __('messages.erc.how_to_resubmit') }}

{{ __('messages.erc.resubmit_steps') }}

1. **{{ __('messages.erc.resubmit_step_1') }}**
2. **{{ __('messages.erc.resubmit_step_2') }}**
3. **{{ __('messages.erc.resubmit_step_3') }}**

<x-mail::button :url="$resubmitUrl">
{{ __('messages.erc.resubmit_button') }}
</x-mail::button>

## {{ __('messages.erc.need_help_title') }}

{{ __('messages.erc.help_message') }}

<x-mail::button :url="$supportUrl" color="secondary">
{{ __('messages.erc.contact_support_button') }}
</x-mail::button>

{{ __('messages.erc.resubmit_encouragement') }}

{{ __('messages.erc.support_team') }}  
{{ config('app.name') }}

---
<small>{{ __('messages.erc.rejected_footer') }}</small>
</x-mail::message>
