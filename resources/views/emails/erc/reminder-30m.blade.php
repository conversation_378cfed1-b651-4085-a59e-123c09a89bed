<x-mail::message>
# {{ __('messages.erc.reminder_30m_subject') }}

{{ __('messages.erc.reminder_30m_greeting', ['name' => $customer->business_name]) }}

{{ __('messages.erc.reminder_30m_message') }}

## {{ __('messages.erc.what_is_erc') }}

{{ __('messages.erc.erc_description') }}

## {{ __('messages.erc.why_verify') }}

{{ __('messages.erc.verification_benefits') }}

- ✅ {{ __('messages.erc.benefit_trust') }}
- ✅ {{ __('messages.erc.benefit_features') }}
- ✅ {{ __('messages.erc.benefit_support') }}
- ✅ {{ __('messages.erc.benefit_limits') }}

## {{ __('messages.erc.how_to_upload') }}

{{ __('messages.erc.upload_instructions') }}

1. {{ __('messages.erc.step_1') }}
2. {{ __('messages.erc.step_2') }}
3. {{ __('messages.erc.step_3') }}

**{{ __('messages.erc.file_requirements') }}**
- {{ __('messages.erc.format_requirement') }}
- {{ __('messages.erc.size_requirement') }}
- {{ __('messages.erc.quality_requirement') }}

<x-mail::button :url="$uploadUrl">
{{ __('messages.erc.upload_button') }}
</x-mail::button>

{{ __('messages.erc.need_help') }}

{{ __('messages.erc.regards') }}  
{{ config('app.name') }}

---
<small>{{ __('messages.erc.footer_note') }}</small>
</x-mail::message>
