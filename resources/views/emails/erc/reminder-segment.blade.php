<x-mail::message>
# {{ __('messages.erc.reminder_segment_subject') }}

{{ __('messages.erc.reminder_segment_greeting', ['name' => $customer->business_name]) }}

@if($segment->value === 'purchased')
{{ __('messages.erc.reminder_purchased_message') }}
@elseif($segment->value === 'demo')
{{ __('messages.erc.reminder_demo_message') }}
@else
{{ __('messages.erc.reminder_default_message', ['days' => $daysSinceRegistration]) }}
@endif

## {{ __('messages.erc.urgent_verification') }}

@if($segment->value === 'purchased')
{{ __('messages.erc.purchased_urgency') }}
@elseif($segment->value === 'demo')
{{ __('messages.erc.demo_urgency') }}
@else
{{ __('messages.erc.default_urgency') }}
@endif

## {{ __('messages.erc.verification_benefits_title') }}

{{ __('messages.erc.verification_benefits_intro') }}

- 🏢 {{ __('messages.erc.benefit_business_trust') }}
- 🚀 {{ __('messages.erc.benefit_priority_support') }}
- 📈 {{ __('messages.erc.benefit_advanced_features') }}
- 💼 {{ __('messages.erc.benefit_business_tools') }}

@if($segment->value === 'purchased')
## {{ __('messages.erc.purchased_exclusive') }}

{{ __('messages.erc.purchased_exclusive_message') }}

- 🎯 {{ __('messages.erc.purchased_benefit_1') }}
- 📊 {{ __('messages.erc.purchased_benefit_2') }}
- 🔒 {{ __('messages.erc.purchased_benefit_3') }}
@endif

## {{ __('messages.erc.quick_upload_guide') }}

{{ __('messages.erc.upload_is_easy') }}

1. **{{ __('messages.erc.quick_step_1') }}**
2. **{{ __('messages.erc.quick_step_2') }}**
3. **{{ __('messages.erc.quick_step_3') }}**

<x-mail::button :url="$uploadUrl">
{{ __('messages.erc.upload_now_button') }}
</x-mail::button>

{{ __('messages.erc.questions_contact') }}

{{ __('messages.erc.thanks') }}  
{{ config('app.name') }}

---
<small>{{ __('messages.erc.segment_footer', ['segment' => $segment->label()]) }}</small>
</x-mail::message>
