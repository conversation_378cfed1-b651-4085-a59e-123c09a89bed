<x-mail::message>
# 🎉 {{ __('messages.erc.verified_subject') }}

{{ __('messages.erc.verified_greeting', ['name' => $customer->business_name]) }}

{{ __('messages.erc.verified_congratulations') }}

## {{ __('messages.erc.verification_details') }}

- **{{ __('messages.erc.business_name') }}** {{ $customer->business_name }}
- **{{ __('messages.erc.tax_code') }}** {{ $customer->tax_code }}
- **{{ __('messages.erc.verified_date') }}** {{ $verifiedAt->format('d/m/Y H:i') }}
- **{{ __('messages.erc.status') }}** <span style="color: #10B981; font-weight: bold;">{{ __('messages.erc.status_verified') }}</span>

## {{ __('messages.erc.unlocked_features_title') }}

{{ __('messages.erc.features_unlocked_message') }}

@foreach($unlockedFeatures as $feature)
- 🚀 {{ $feature }}
@endforeach

## {{ __('messages.erc.next_steps_title') }}

{{ __('messages.erc.next_steps_intro') }}

1. **{{ __('messages.erc.next_step_1') }}** {{ __('messages.erc.next_step_1_desc') }}
2. **{{ __('messages.erc.next_step_2') }}** {{ __('messages.erc.next_step_2_desc') }}
3. **{{ __('messages.erc.next_step_3') }}** {{ __('messages.erc.next_step_3_desc') }}

<x-mail::panel>
**{{ __('messages.erc.important_security') }}**

{{ __('messages.erc.security_note') }}
</x-mail::panel>

## {{ __('messages.erc.business_benefits') }}

{{ __('messages.erc.business_benefits_intro') }}

- 💼 {{ __('messages.erc.business_benefit_1') }}
- 📊 {{ __('messages.erc.business_benefit_2') }}
- 🎯 {{ __('messages.erc.business_benefit_3') }}
- 🔒 {{ __('messages.erc.business_benefit_4') }}

<x-mail::button :url="$dashboardUrl">
{{ __('messages.erc.access_dashboard_button') }}
</x-mail::button>

{{ __('messages.erc.welcome_verified_customer') }}

{{ __('messages.erc.best_regards') }}  
{{ config('app.name') }}

---
<small>{{ __('messages.erc.verified_footer') }}</small>
</x-mail::message>
