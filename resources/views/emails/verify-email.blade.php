@component('mail::message')
{{-- Greeting --}}
# {{ __('email.verify.greeting') }}

{{-- Intro Lines --}}
{{ __('email.verify.intro') }}

{{-- Action Button --}}
@component('mail::button', ['url' => $url, 'color' => 'primary'])
{{ __('email.verify.button') }}
@endcomponent

{{-- Outro Lines --}}
{{ __('email.verify.outro') }}

{{-- Salutation --}}
{{ __('email.verify.regards') }},<br>
{{ config('app.name') }}

{{-- Subcopy --}}
@slot('subcopy')
@lang(
    'email.verify.trouble_clicking',
    [
        'actionText' => __('email.verify.button'),
    ]
) <span class="break-all">[{{ $url }}]({{ $url }})</span>
@endslot
@endcomponent
