<x-mail::message>
# {{ __('messages.email.welcome_subject', ['app_name' => config('app.name')]) }}

{{ __('messages.email.welcome_greeting', ['name' => $user->name]) }}

{{ __('messages.email.welcome_thanks', ['app_name' => config('app.name')]) }}

{{ __('messages.email.welcome_account_info') }}

- **{{ __('messages.email.welcome_login_email') }}** {{ $user->email }}
- **{{ __('messages.email.welcome_registration_date') }}** {{ $user->created_at->format('d/m/Y') }}

{{ __('messages.email.welcome_verify_instruction') }}

<x-mail::button :url="$verificationUrl">
    {{ __('messages.email.welcome_verify_button') }}
</x-mail::button>

{{ __('messages.email.welcome_non_action') }}

{{ __('messages.email.welcome_after_verification', ['app_name' => config('app.name')]) }}

{{ __('messages.email.welcome_support_contact') }}

{{ __('messages.email.welcome_closing', ['app_name' => config('app.name')]) }}

{{ __('messages.email.regards') }},
{{ config('app.name') }}
</x-mail::message>
