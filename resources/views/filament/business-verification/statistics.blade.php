<div class="space-y-6">
    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <!-- Total Verifications -->
        <div class="bg-white rounded-lg border p-4">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                        <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Total Verifications</p>
                    <p class="text-2xl font-semibold text-gray-900">{{ number_format($stats['total']) }}</p>
                </div>
            </div>
        </div>

        <!-- Pending Review -->
        <div class="bg-white rounded-lg border p-4">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
                        <svg class="w-4 h-4 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Pending Review</p>
                    <p class="text-2xl font-semibold text-yellow-600">{{ number_format($stats['pending_review']) }}</p>
                </div>
            </div>
        </div>

        <!-- Verified -->
        <div class="bg-white rounded-lg border p-4">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                        <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Verified</p>
                    <p class="text-2xl font-semibold text-green-600">{{ number_format($stats['verified']) }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Status Breakdown -->
    <div class="bg-white rounded-lg border p-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Status Breakdown</h3>
        <div class="grid grid-cols-2 md:grid-cols-5 gap-4">
            <div class="text-center">
                <p class="text-2xl font-semibold text-gray-500">{{ number_format($stats['unsubmitted']) }}</p>
                <p class="text-sm text-gray-600">Unsubmitted</p>
            </div>
            <div class="text-center">
                <p class="text-2xl font-semibold text-yellow-600">{{ number_format($stats['submitted']) }}</p>
                <p class="text-sm text-gray-600">Submitted</p>
            </div>
            <div class="text-center">
                <p class="text-2xl font-semibold text-blue-600">{{ number_format($stats['in_review']) }}</p>
                <p class="text-sm text-gray-600">In Review</p>
            </div>
            <div class="text-center">
                <p class="text-2xl font-semibold text-green-600">{{ number_format($stats['verified']) }}</p>
                <p class="text-sm text-gray-600">Verified</p>
            </div>
            <div class="text-center">
                <p class="text-2xl font-semibold text-red-600">{{ number_format($stats['rejected']) }}</p>
                <p class="text-sm text-gray-600">Rejected</p>
            </div>
        </div>
    </div>

    <!-- Reminder Statistics -->
    <div class="bg-white rounded-lg border p-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Reminder Statistics</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <span class="text-sm font-medium text-gray-600">Needs 30m Reminder</span>
                <span class="text-lg font-semibold text-gray-900">{{ number_format($stats['needs_30m_reminder']) }}</span>
            </div>
            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <span class="text-sm font-medium text-gray-600">Needs Segment Reminder</span>
                <span class="text-lg font-semibold text-gray-900">{{ number_format($stats['needs_segment_reminder']) }}</span>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="bg-white rounded-lg border p-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Quick Actions</h3>
        <div class="flex flex-wrap gap-3">
            <button onclick="processReminders()" class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                <svg class="mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                Process Reminders
            </button>
            <button onclick="refreshStats()" class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                <svg class="mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                </svg>
                Refresh Stats
            </button>
        </div>
    </div>
</div>

<script>
function processReminders() {
    if (confirm('Are you sure you want to process pending reminders?')) {
        // This would typically make an AJAX call to process reminders
        alert('Reminders processing initiated. Check the logs for results.');
    }
}

function refreshStats() {
    window.location.reload();
}
</script>
