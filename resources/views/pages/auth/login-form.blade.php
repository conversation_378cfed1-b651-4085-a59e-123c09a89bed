@extends('layouts.default')

@section('content')
<section class="py-12 md:py-16 bg-gray-50">
    <div class="container px-4 mx-auto">
        <div class="max-w-md mx-auto bg-white rounded-xl shadow-sm overflow-hidden">
            <div class="px-8 py-8">
                <div class="text-center mb-8">
                    <h1 class="text-2xl font-bold text-gray-900">{{ __('messages.auth.login.title') }}</h1>
                    <p class="mt-2 text-sm text-gray-600">{{ __('messages.auth.login.subtitle') }}</p>
                </div>

                <form action="{{ route('login.post') }}" method="POST" class="space-y-6">
                    @csrf
                    
                    <div>
                        <label for="email" class="block text-sm font-medium text-gray-700 mb-1">{{ __('messages.auth.login.email_label') }}</label>
                        <x-form.input 
                            type="email" 
                            name="email" 
                            id="email" 
                            :placeholder="__('messages.auth.login.email_placeholder')"
                            required
                        />
                    </div>

                    <div>
                        <div class="flex items-center justify-between mb-1">
                            <label for="password" class="block text-sm font-medium text-gray-700">{{ __('messages.auth.login.password_label') }}</label>
                            <a href="{{ route('password.request') }}" class="text-sm font-medium text-emerald-600 hover:text-emerald-500">
                                {{ __('messages.auth.login.forgot_password') }}
                            </a>
                        </div>
                        <x-form.input 
                            type="password" 
                            name="password" 
                            id="password" 
                            :placeholder="__('messages.auth.login.password_placeholder')"
                            required
                        />
                    </div>

                    <div class="flex items-center">
                        <x-form.checkbox 
                            name="remember" 
                            id="remember_me"
                            :label="__('messages.auth.login.remember_me')"
                            class="mr-2"
                        />
                    </div>

                    <button type="submit" 
                        class="w-full flex justify-center py-2.5 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-emerald-600 hover:bg-emerald-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-emerald-500 transition-colors duration-200"
                    >
                        {{ __('messages.auth.login.submit') }}
                    </button>
                </form>

                @if($errors->any())
                    <div class="mt-6 p-4 bg-red-50 rounded-md">
                        <div class="flex">
                            <div class="flex-shrink-0">
                                <svg class="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                                </svg>
                            </div>
                            <div class="ml-3">
                                <h3 class="text-sm font-medium text-red-800">{{ __('messages.auth.login.error_title') }}</h3>
                                <div class="mt-2 text-sm text-red-700">
                                    <ul class="list-disc pl-5 space-y-1">
                                        @foreach ($errors->all() as $error)
                                            <li>{{ $error }}</li>
                                        @endforeach
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                @endif

                <div class="mt-6">
                    <div class="relative">
                        <div class="absolute inset-0 flex items-center">
                            <div class="w-full border-t border-gray-300"></div>
                        </div>
                        <div class="relative flex justify-center text-sm">
                            <span class="px-2 bg-white text-gray-500">Hoặc</span>
                        </div>
                    </div>

                    <div class="mt-6 grid grid-cols-1 gap-3">
                        <a href="#" class="w-full inline-flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-emerald-500 transition-colors duration-200">
                            <span class="sr-only">Đăng nhập với Google</span>
                            <svg class="w-5 h-5" aria-hidden="true" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M12.48 10.92v3.28h7.84c-.24 1.84-.853 3.187-1.787 4.133-1.147 1.147-2.933 2.4-6.053 2.4-4.827 0-8.6-3.893-8.6-8.72s3.773-8.72 8.6-8.72c2.6 0 4.507 1.027 5.907 2.347l2.307-2.307C18.747 1.44 16.133 0 12.48 0 5.867 0 .307 5.387.307 12s5.56 12 12.173 12c3.573 0 6.267-1.173 8.373-3.36 2.16-2.16 2.84-5.213 2.84-7.667 0-.76-.053-1.467-.173-2.053H12.48z"/>
                            </svg>
                        </a>
                    </div>
                </div>
            </div>
            
            <div class="bg-gray-50 px-8 py-4 border-t border-gray-200">
                <p class="text-center text-sm text-gray-600">
                    Chưa có tài khoản?
                    <a href="/dang-ky" class="font-medium text-emerald-600 hover:text-emerald-500">
                        Đăng ký ngay
                    </a>
                </p>
            </div>
        </div>
    </div>
</section>
@endsection
