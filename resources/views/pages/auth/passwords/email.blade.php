@extends('layouts.default')

@section('content')
<section class="py-12 md:py-16 bg-gray-50">
    <div class="container px-4 mx-auto">
        <div class="max-w-md mx-auto bg-white rounded-xl shadow-sm overflow-hidden">
            <div class="px-8 py-8">
                <div class="text-center mb-8">
                    <h1 class="text-2xl font-bold text-gray-900">{{ __('messages.auth.password.forgot_title') }}</h1>
                    <p class="mt-2 text-sm text-gray-600">{{ __('messages.auth.password.forgot_description') }}</p>
                </div>

                @if (session('status'))
                    <div class="mb-4 text-sm font-medium text-green-600">
                        {{ session('status') }}
                    </div>
                @endif

                <form method="POST" action="{{ route('password.email') }}" class="space-y-6">
                    @csrf
                    
                    <div>
                        <label for="email" class="block text-sm font-medium text-gray-700 mb-1">{{ __('messages.auth.password.email_label') }}</label>
                        <x-form.input 
                            type="email" 
                            name="email" 
                            id="email" 
                            value="{{ old('email') }}"
                            placeholder="{{ __('messages.auth.password.email_placeholder') }}"
                            required
                            autofocus
                        />
                        @error('email')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <button type="submit" 
                            class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-emerald-600 hover:bg-emerald-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-emerald-500 transition-colors">
                            {{ __('messages.auth.password.send_reset_link') }}
                        </button>
                    </div>
                </form>
            </div>
            
            <div class="bg-gray-50 px-8 py-4 border-t border-gray-200">
                <p class="text-center text-sm text-gray-600">
                    <a href="{{ route('login') }}" class="font-medium text-emerald-600 hover:text-emerald-500">
                        {{ __('messages.auth.password.back_to_login') }}
                    </a>
                </p>
            </div>
        </div>
    </div>
</section>
@endsection
