@extends('layouts.default')

@section('content')
<section class="py-12 md:py-16 bg-gray-50">
    <div class="container px-4 mx-auto">
        <div class="max-w-md mx-auto bg-white rounded-xl shadow-sm overflow-hidden">
            <div class="px-8 py-8">
                <div class="text-center mb-8">
                    <h1 class="text-2xl font-bold text-gray-900">Đặt lại mật khẩu</h1>
                    <p class="mt-2 text-sm text-gray-600">Vui lòng nhập mật khẩu mới của bạn</p>
                </div>

                @if (session('status'))
                    <div class="mb-4 text-sm font-medium text-green-600">
                        {{ session('status') }}
                    </div>
                @endif

                <form method="POST" action="{{ route('password.update') }}" class="space-y-6">
                    @csrf
                    <input type="hidden" name="token" value="{{ $token }}">
                    
                    <div>
                        <label for="email" class="block text-sm font-medium text-gray-700 mb-1">Email</label>
                        <x-form.input 
                            type="email" 
                            name="email" 
                            id="email" 
                            value="{{ $email ?? old('email') }}"
                            required
                            readonly
                            class="bg-gray-100"
                        />
                        @error('email')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="password" class="block text-sm font-medium text-gray-700 mb-1">Mật khẩu mới</label>
                        <x-form.input 
                            type="password" 
                            name="password" 
                            id="password" 
                            placeholder="Nhập mật khẩu mới"
                            required
                            autofocus
                        />
                        @error('password')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="password_confirmation" class="block text-sm font-medium text-gray-700 mb-1">Xác nhận mật khẩu mới</label>
                        <x-form.input 
                            type="password" 
                            name="password_confirmation" 
                            id="password_confirmation" 
                            placeholder="Nhập lại mật khẩu mới"
                            required
                        />
                    </div>

                    <div>
                        <button type="submit" 
                            class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-emerald-600 hover:bg-emerald-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-emerald-500 transition-colors">
                            Đặt lại mật khẩu
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</section>
@endsection
