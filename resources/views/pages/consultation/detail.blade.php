@extends('layouts.default')

@section('content')
<div class="container mx-auto px-4 py-8">
    <div class="max-w-4xl mx-auto">
        <!-- Article Header -->
        <header class="mb-8">
            <h1 class="text-3xl md:text-4xl font-bold mb-4">{{ $post->title }}</h1>
            <div class="flex items-center text-gray-600 text-sm mb-4">
                <span class="mr-4">
                    <i class="far fa-calendar-alt mr-1"></i>
                    {{ $post->created_at->format('d/m/Y') }}
                </span>
                <span class="mr-4">
                    <i class="far fa-folder mr-1"></i>
                    <a href="{{ route('consultation.index', $post->category->slug) }}" class="hover:text-emerald-600 transition">
                        {{ $post->category->name }}
                    </a>
                </span>
            </div>
            @if ($post->featured_image_url)
            <img src="{{ $post->featured_image_url }}" 
                 alt="{{ $post->title }}" 
                 class="w-full h-auto rounded-lg mb-6">
            @else
            <div class="w-full h-48 bg-gray-100 flex items-center justify-center text-gray-400">
                <i class="fa-regular fa-image text-2xl"></i>
            </div>
            @endif
        </header>

        <!-- Article Content -->
        <article class="prose max-w-none mb-12">
            {!! $post->content !!}
        </article>

        <!-- Tags -->
        <div class="flex flex-wrap gap-2 mb-8">
            @foreach ($post->tags as $tag)
                <span class="bg-gray-100 px-3 py-1 rounded-full text-sm text-gray-700">
                    #{{ $tag->name }}
                </span>
            @endforeach
        </div>

        <!-- Author Box -->
        <div class="bg-gray-50 rounded-lg p-6 mb-12">
            <div class="flex items-center">
                <div class="w-20 h-20 overflow-hidden mr-4">
                    <div class="w-14 h-14 rounded-lg bg-gray-100 flex items-center justify-center text-gray-400">
                        <i class="fa-regular fa-image text-2xl"></i>
                    </div>
                </div>
                <div>
                    <h3 class="text-xl font-semibold">{{ $post->author->name }}</h3>
                    <p class="text-gray-600 mb-2">Chuyên gia phát triển web với hơn 5 năm kinh nghiệm trong lĩnh vực tối ưu hiệu suất và trải nghiệm người dùng.</p>
                    <div class="flex space-x-4">
                        <a href="#" target="_blank" class="text-gray-500 hover:text-emerald-600">
                            <i class="fab fa-facebook"></i>
                        </a>
                        <a href="#" target="_blank" class="text-gray-500 hover:text-emerald-500">
                            <i class="fab fa-twitter"></i>
                        </a>
                        <a href="#" target="_blank" class="text-gray-500 hover:text-emerald-700">
                            <i class="fas fa-globe"></i>
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Related Posts -->
        <div class="mb-12">
            <h2 class="text-2xl font-bold mb-6">Bài viết liên quan</h2>
            <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                @forelse($relatedPosts as $relatedPost)
                <div class="bg-white rounded-lg overflow-hidden shadow-md hover:shadow-lg transition-shadow">
                    <a href="{{ route('consultation.detail', ['category_slug' => $relatedPost->category->slug, 'detail_slug' => $relatedPost->slug]) }}">
                        @if ($relatedPost->featured_image_url)
                            <img src="{{ $relatedPost->featured_image_url }}" 
                                 alt="{{ $relatedPost->title }}"
                                 class="w-full h-48 object-cover">
                        @else
                            <div class="w-full h-48 bg-gray-100 flex items-center justify-center text-gray-400">
                                <i class="fa-regular fa-image text-2xl"></i>
                            </div>
                        @endif
                    </a>
                    <div class="p-4">
                        <h3 class="text-lg font-semibold mb-2">
                            <a href="{{ route('consultation.detail', ['category_slug' => $relatedPost->category->slug, 'detail_slug' => $relatedPost->slug]) }}" class="hover:text-emerald-600 transition">
                                {{ $relatedPost->title }}
                            </a>
                        </h3>
                        <p class="text-sm text-gray-500 mb-2">{{ $relatedPost->created_at->format('d/m/Y') }}</p>
                    </div>
                </div>
                @empty
                <div class="col-span-4 text-center py-8">
                    <p class="text-gray-500">Không có bài viết liên quan.</p>
                </div>
                @endforelse
            </div>
        </div>
    </div>
</div>

@push('styles')
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .prose {
            color: #374151;
            line-height: 1.75;
        }
        .prose h2 {
            font-size: 1.5rem;
            font-weight: 600;
            margin: 1.5em 0 1em;
        }
        .prose p {
            margin-bottom: 1.25em;
        }
        .prose img {
            border-radius: 0.5rem;
            margin: 1.5em 0;
            max-width: 100%;
            height: auto;
        }
    </style>
@endpush
@endsection