@extends('layouts.default')

@push('styles')
    @vite(['resources/css/pages/consultation.css'])
@endpush

@section('content')
    <main class="consultation">
        <!-- Hero Section (header giống home) -->
        <section class="relative py-16 md:py-24 bg-gradient-to-br from-emerald-50 via-white to-blue-50 mb-12">
            <div class="absolute inset-0 max-w-full overflow-hidden">
                <div class="absolute -right-20 -top-20 w-96 h-96 bg-emerald-600/5 rounded-full mix-blend-multiply"></div>
                <div class="absolute -left-20 -bottom-20 w-96 h-96 bg-blue-600/5 rounded-full mix-blend-multiply"></div>
                <div
                    class="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-emerald-400/5 rounded-full mix-blend-multiply">
                </div>
            </div>
            <div class="container mx-auto px-6 relative z-10">
                <div class="max-w-3xl mx-auto text-center space-y-6">
                    <span class="inline-block px-4 py-2 rounded-full bg-emerald-100 text-emerald-700 text-sm font-medium">
                        📚 Chuyên mục: {{ $category->name }}
                    </span>
                    <h1 class="text-4xl md:text-5xl font-extrabold text-gray-900 leading-tight">
                        {{ $category->name }}
                    </h1>
                    <p class="text-xl md:text-2xl text-gray-600 max-w-2xl mx-auto leading-relaxed">
                        {{ $category->description }}
                    </p>
                </div>
            </div>
        </section>

        <!-- Content Section -->
        <section class="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
            <div class="flex flex-col lg:flex-row gap-8">
                <!-- Main Content (Left Column) -->
                <main class="w-full lg:w-2/3">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                        @foreach ($posts as $post)
                            <article
                                class="group bg-white rounded-2xl shadow-md overflow-hidden mb-12 hover:shadow-xl transition-all duration-300 border border-gray-100 hover:-translate-y-1">
                                <!-- Featured Image with Overlay -->
                                <div class="relative h-64 overflow-hidden">
                                    <div
                                        class="absolute inset-0 bg-gradient-to-t from-black/40 to-transparent z-10 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                                    </div>
                                    @if ($post->featured_image_url)
                                        <img src="{{ $post->featured_image_url }}" alt="{{ $post->title }}"
                                            class="w-full h-full object-cover transition-transform duration-700 group-hover:scale-105">
                                    @else
                                        <div
                                            class="w-full h-full bg-gray-100 flex items-center justify-center text-gray-400">
                                            <i class="fa-regular fa-image text-2xl"></i>
                                        </div>
                                    @endif

                                    <!-- Category Badge -->
                                    <div class="absolute top-4 right-4 z-20">
                                        <span
                                            class="inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold bg-emerald-600 text-white">
                                            {{ $category->name }}
                                        </span>
                                    </div>
                                </div>

                                <!-- Article Content -->
                                <div class="p-6">
                                    <!-- Meta Info -->
                                    <div class="flex flex-wrap items-center text-sm text-gray-500 mb-3 gap-4">
                                        <div class="flex items-center">
                                            <svg class="w-4 h-4 mr-1.5 text-gray-400" fill="none" stroke="currentColor"
                                                viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"
                                                    d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z">
                                                </path>
                                            </svg>
                                            <span>{{ $post->created_at->format('d/m/Y') }}</span>
                                        </div>
                                        <div class="flex items-center">
                                            <svg class="w-4 h-4 mr-1.5 text-gray-400" fill="none" stroke="currentColor"
                                                viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"
                                                    d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                            </svg>
                                            <span>{{ $post->author->name ?? 'Admin' }}</span>
                                        </div>
                                    </div>

                                    <!-- Title -->
                                    <h2
                                        class="text-2xl font-bold text-gray-900 mb-3 leading-tight group-hover:text-emerald-600 transition-colors duration-300">
                                        <a href="{{ route('consultation.detail', ['category_slug' => $category->slug, 'detail_slug' => $post->slug]) }}"
                                            class="hover:underline decoration-2 decoration-emerald-500 underline-offset-4">
                                            {{ $post->title }}
                                        </a>
                                    </h2>

                                    <!-- Excerpt -->
                                    <div class="mb-4">
                                        <p class="text-gray-600 leading-relaxed line-clamp-3">
                                            {{ $post->summary }}
                                        </p>
                                    </div>

                                    <!-- Read More & Tags -->
                                    <div class="flex flex-wrap items-center justify-between pt-4 border-t border-gray-100">
                                        @if ($post->tags && $post->tags->count() > 0)
                                            <div class="flex flex-wrap gap-2 mb-3 sm:mb-0">
                                                @foreach ($post->tags->take(3) as $tag)
                                                    <a href="#"
                                                        class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-700 hover:bg-gray-200 transition-colors duration-200">
                                                        #{{ $tag->name }}
                                                    </a>
                                                @endforeach
                                                @if ($post->tags->count() > 3)
                                                    <span class="inline-flex items-center px-2 text-xs text-gray-500">
                                                        +{{ $post->tags->count() - 3 }}
                                                    </span>
                                                @endif
                                            </div>
                                        @endif
                                        <a href="{{ route('consultation.detail', ['category_slug' => $category->slug, 'detail_slug' => $post->slug]) }}"
                                            class="inline-flex items-center text-sm font-medium text-emerald-600 hover:text-emerald-700 group transition-colors duration-200">
                                            Xem chi tiết
                                            <svg class="w-4 h-4 ml-1 transform group-hover:translate-x-1 transition-transform duration-200"
                                                fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M14 5l7 7m0 0l-7 7m7-7H3"></path>
                                            </svg>
                                        </a>
                                    </div>
                                </div>
                            </article>
                        @endforeach
                    </div>

                    <!-- Pagination -->
                    <div class="md:my-8">
                        {{ $posts->links() }}
                    </div>
                </main>

                <!-- Sidebar (Right Column) -->
                <aside class="w-full lg:w-1/3 space-y-8">
                    <!-- Search Widget -->
                    <div class="bg-white p-6 rounded-lg shadow-md border border-gray-200">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4 pb-2 border-b border-gray-200">Tìm kiếm</h3>
                        <form class="relative">
                            <x-form.input name="keyword" placeholder="Từ khóa tìm kiếm..." />
                            <button type="submit"
                                class="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-emerald-600">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                                    xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                </svg>
                            </button>
                        </form>
                    </div>

                    <!-- Categories Widget -->
                    <div class="bg-white p-6 rounded-lg shadow-md border border-gray-200">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4 pb-2 border-b border-gray-200">Danh mục</h3>
                        <ul class="space-y-2">
                            @php
                                $categories = App\Models\PostCategory::withCount('posts')->get();
                            @endphp
                            @foreach ($categories as $category)
                                <li><a href="{{ route('consultation.index', ['category_slug' => $category->slug]) }}"
                                        class="flex justify-between items-center text-gray-700 hover:text-emerald-600 transition-colors">{{ $category->name }}
                                        <span
                                            class="bg-gray-100 text-gray-600 text-xs font-medium px-2.5 py-0.5 rounded-full">{{ $category->posts_count }}</span></a>
                                </li>
                            @endforeach
                        </ul>
                    </div>
                    <!-- Popular Posts Widget -->
                    <div class="bg-white p-6 rounded-lg shadow-md border border-gray-200">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4 pb-2 border-b border-gray-200">Bài viết phổ biến
                        </h3>
                        <div class="space-y-4">
                            @for ($i = 1; $i <= 3; $i++)
                                <div class="flex gap-3">
                                    <div class="flex-shrink-0">
                                        <img src="https://source.unsplash.com/random/100x100?popular,{{ $i }}"
                                            alt="Popular post {{ $i }}" class="w-16 h-16 object-cover rounded">
                                    </div>
                                    <div>
                                        <h4 class="font-medium text-gray-900 hover:text-emerald-600 transition-colors">
                                            <a href="#">Tiêu đề bài viết phổ biến {{ $i }}</a>
                                        </h4>
                                        <p class="text-sm text-gray-500">14/06/2023</p>
                                    </div>
                                </div>
                            @endfor
                        </div>
                    </div>
                    <!-- Tags Widget -->
                    <div class="bg-white p-6 rounded-lg shadow-md">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4 pb-2 border-b border-gray-200">Thẻ phổ biến</h3>
                        <div class="flex flex-wrap gap-2">
                            <a href="#"
                                class="inline-block px-3 py-1 bg-gray-100 text-gray-800 text-sm rounded-full hover:bg-emerald-100 transition-colors">#tưvấn</a>
                            <a href="#"
                                class="inline-block px-3 py-1 bg-gray-100 text-gray-800 text-sm rounded-full hover:bg-emerald-100 transition-colors">#hướngdẫn</a>
                            <a href="#"
                                class="inline-block px-3 py-1 bg-gray-100 text-gray-800 text-sm rounded-full hover:bg-emerald-100 transition-colors">#mẹovặt</a>
                            <a href="#"
                                class="inline-block px-3 py-1 bg-gray-100 text-gray-800 text-sm rounded-full hover:bg-emerald-100 transition-colors">#tàiliệu</a>
                            <a href="#"
                                class="inline-block px-3 py-1 bg-gray-100 text-gray-800 text-sm rounded-full hover:bg-emerald-100 transition-colors">#thôngtin</a>
                        </div>
                    </div>
                    <!-- Newsletter Widget -->
                    <div class="bg-blue-50 p-6 rounded-lg border border-blue-100">
                        <h3 class="text-lg font-semibold text-gray-900 mb-3">Đăng ký nhận tin</h3>
                        <p class="text-sm text-gray-600 mb-4">Đăng ký để nhận thông tin mới nhất từ chúng tôi qua email.
                        </p>
                        <form class="space-y-3">
                            <x-form.input name="email" placeholder="Địa chỉ email của bạn" />
                            <button type="submit"
                                class="w-full bg-emerald-600 text-white py-2 px-4 rounded-md hover:bg-emerald-700 transition-colors">Đăng
                                ký ngay</button>
                        </form>
                    </div>
                </aside>
            </div>
        </section>
    </main>
@endsection
