<!DOCTYPE html>
<html lang="vi" class="scroll-smooth">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Demo Template - {{ $template->name ?? '' }} | CSlant</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Be+Vietnam+Pro:wght@400;500;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
        integrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw=="
        crossorigin="anonymous" referrerpolicy="no-referrer" />
    @vite('resources/css/app.css')
    <style>
        html, body {
            height: 100%;
            margin: 0;
            padding: 0;
            overflow: hidden;
        }
        
        .app-container {
            height: 100vh;
            display: flex;
            flex-direction: column;
        }
        
        .responsive-btn {
            padding: 0.375rem 0.5rem;
            border-radius: 0.375rem;
            transition: all 0.2s;
        }
        
        .responsive-btn:hover {
            background-color: rgba(255, 255, 255, 0.1);
            cursor: pointer;
        }
        
        .responsive-btn.active {
            background-color: rgba(255, 255, 255, 0.2);
        }
        
        .responsive-btn.active i {
            color: white;
        }
    </style>
</head>
<body class="bg-white text-gray-800">
    <div class="app-container">
        <!-- Header -->
        <header class="bg-black shadow-sm w-full">
            <div class="flex flex-wrap items-center justify-between px-4 py-3 md:px-6 w-full">
                <a href="{{ url('/') }}" class="flex items-center min-w-0">
                    <img src="{{ asset('images/cslant-logo.png') }}" alt="CSlant Logo" class="h-6 md:h-8 mr-2">
                    <span class="text-white text-sm md:text-base font-medium truncate">Demo {{ $template->name ?? 'Mẫu Demo' }}</span>
                </a>
                <div class="flex flex-col sm:flex-row items-end sm:items-center gap-3 mt-2 sm:mt-0">
                    <div class="hidden md:flex items-center space-x-2 bg-gray-800 rounded-full px-3 py-1.5">
                        <button type="button" class="responsive-btn active" data-device="desktop" title="Desktop">
                            <i class="fas fa-desktop text-gray-300 hover:text-white"></i>
                        </button>
                        <button type="button" class="responsive-btn" data-device="tablet" title="Tablet">
                            <i class="fas fa-tablet-alt text-gray-300 hover:text-white"></i>
                        </button>
                        <button type="button" class="responsive-btn" data-device="mobile" title="Mobile">
                            <i class="fas fa-mobile-alt text-gray-300 hover:text-white"></i>
                        </button>
                    </div>
                    <a href="{{ route('pages.show', 'lien-he') }}" target="_blank" class="inline-flex items-center px-3 py-1.5 sm:px-4 sm:py-2 text-xs sm:text-sm bg-gradient-to-r from-emerald-600 to-emerald-700 text-white border border-emerald-500 rounded-full font-bold uppercase tracking-wider hover:from-emerald-500 hover:to-emerald-600 hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-emerald-400 transition-all duration-200">
                        <i class="fas fa-envelope mr-1 hidden sm:inline"></i> Liên hệ ngay
                    </a>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="flex-1 overflow-hidden">
            @if($template && $template->demo_url)
                <iframe 
                    src="{{ $template->demo_url }}" 
                    class="w-full h-full border-0"
                    allowfullscreen
                    loading="lazy">
                </iframe>
            @else
                <div class="flex items-center justify-center h-full bg-gray-100">
                    <div class="text-center">
                        <p class="text-gray-600 text-lg">{{ __('messages.website.template_not_found') }}</p>
                        <a href="{{ route('templates.index') }}" class="mt-4 inline-block text-blue-600 hover:underline">
                            {{ __('messages.website.back_to_templates') }}
                        </a>
                    </div>
                </div>
            @endif
        </main>
    </div>
    @vite('resources/js/app.js')
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const iframe = document.querySelector('iframe');
            const buttons = document.querySelectorAll('.responsive-btn');
            
            buttons.forEach(button => {
                button.addEventListener('click', function() {
                    // Remove active class from all buttons
                    buttons.forEach(btn => btn.classList.remove('active'));
                    // Add active class to clicked button
                    this.classList.add('active');
                    
                    // Set iframe width based on device
                    const device = this.getAttribute('data-device');
                    if (device === 'desktop') {
                        iframe.style.width = '100%';
                    } else if (device === 'tablet') {
                        iframe.style.width = '768px';
                    } else if (device === 'mobile') {
                        iframe.style.width = '375px';
                    }
                    
                    // Center the iframe
                    if (device !== 'desktop') {
                        iframe.style.margin = '0 auto';
                        iframe.style.display = 'block';
                    } else {
                        iframe.style.margin = '0';
                    }
                });
            });
        });
    </script>
</body>
</html>
