@extends('layouts.default')

@push('styles')
    @vite(['resources/css/pages/home.css'])
@endpush

@section('content')
    <main>
        <!-- Hero Section -->
        <section id="hero" class="relative py-24 md:py-32 bg-gradient-to-br from-emerald-50 via-white to-blue-50">
            <!-- Decorative elements -->
            <div class="absolute inset-0 max-w-full overflow-hidden">
                <div class="absolute -right-20 -top-20 w-96 h-96 bg-emerald-600/5 rounded-full mix-blend-multiply"></div>
                <div class="absolute -left-20 -bottom-20 w-96 h-96 bg-blue-600/5 rounded-full mix-blend-multiply"></div>
                <div
                    class="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-emerald-400/5 rounded-full mix-blend-multiply">
                </div>
            </div>

            <div class="container mx-auto px-6 relative z-10">
                <div class="max-w-4xl mx-auto text-center space-y-6">
                    <!-- Badge -->
                    <span class="inline-block px-4 py-2 rounded-full bg-emerald-100 text-emerald-700 text-sm font-medium">
                        {{ __('messages.home.hero_badge') }}
                    </span>

                    <!-- Main Heading -->
                    <h1 class="text-4xl md:text-6xl font-extrabold text-gray-900 leading-tight">
                        {{ __('messages.home.hero_title') }}
                    </h1>

                    <!-- Subheading -->
                    <h2 class="text-2xl md:text-3xl font-semibold text-emerald-700 mb-6 relative inline-block">
                        <span class="relative">
                            <span class="relative z-10">{{ __('messages.home.hero_subtitle') }}</span>
                            <span
                                class="absolute bottom-1 left-0 w-full h-3 bg-emerald-100/70 -z-0 transform -rotate-1"></span>
                        </span>
                    </h2>

                    <!-- Description -->
                    <p class="text-lg md:text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
                        {{ __('messages.home.hero_description') }}
                    </p>

                    <!-- CTA Buttons -->
                    <div class="flex flex-col sm:flex-row justify-center gap-4 mt-10">
                        <x-ui.button x-data @click="$store.modal.open()" variant="primary" size="xl"
                            class="px-8 py-4 text-lg font-semibold hover:cursor-pointer">
                            {{ __('messages.home.cta_quote') }}
                        </x-ui.button>

                        <x-ui.button href="tel:{{ siteSettings()->phone }}" variant="link" size="xl"
                            class="px-8 py-4 text-lg font-semibold border-2 border-emerald-600 text-emerald-600 hover:bg-emerald-50 hover:border-emerald-700 hover:text-emerald-700 hover:cursor-pointer">
                            <span class="flex items-center">
                                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                                    xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z">
                                    </path>
                                </svg>
                                <span>{{ siteSettings()->phone }}</span>
                            </span>
                        </x-ui.button>
                    </div>

                    <!-- Trust indicators -->
                    <div
                        class="mt-16 pt-8 border-t border-gray-100/50 flex flex-wrap justify-center items-center gap-6 text-sm text-gray-500">
                        <div class="flex items-center">
                            <div class="flex -space-x-2">
                                <img class="w-8 h-8 rounded-full border-2 border-white"
                                    src="https://randomuser.me/api/portraits/women/44.jpg" alt="Khách hàng">
                                <img class="w-8 h-8 rounded-full border-2 border-white"
                                    src="https://randomuser.me/api/portraits/men/32.jpg" alt="Khách hàng">
                                <img class="w-8 h-8 rounded-full border-2 border-white"
                                    src="https://randomuser.me/api/portraits/women/68.jpg" alt="Khách hàng">
                            </div>
                            <span class="ml-3">{{ __('messages.home.trusted_by', ['count' => 100]) }}</span>
                        </div>
                        <div class="h-4 w-px bg-gray-200"></div>
                        <div class="flex items-center">
                            <svg class="w-5 h-5 text-yellow-400 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                <path
                                    d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                            </svg>
                            <span>{{ __('messages.home.rated', ['stars' => 5, 'count' => 50]) }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Services Section -->
        <section id="services" class="py-16">
            <div class="container mx-auto px-6">
                <div class="text-center mb-12">
                    <h2 class="text-3xl md:text-4xl font-bold text-gray-800">{{ __('messages.home.services_title') }}</h2>
                    <p class="text-gray-500 mt-2">{{ __('messages.home.services_subtitle') }}.</p>
                </div>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
                    <!-- Gói Express Web -->
                    <div
                        class="group relative bg-white p-8 rounded-xl shadow-xl transform transition-all duration-300 hover:-translate-y-3 hover:shadow-2xl border border-gray-100 overflow-hidden">
                        <div
                            class="absolute -top-10 -right-10 w-32 h-32 bg-emerald-50 rounded-full opacity-0 group-hover:opacity-100 transition-all duration-300">
                        </div>
                        <div class="relative z-10">
                            <div
                                class="w-20 h-20 mx-auto mb-6 rounded-2xl bg-emerald-50 flex items-center justify-center text-4xl text-emerald-500 group-hover:bg-emerald-100 group-hover:scale-110 transition-all duration-300">
                                ⚡
                            </div>
                            <h3
                                class="text-2xl font-bold mb-4 text-gray-800 group-hover:text-emerald-600 transition-colors duration-300">
                                {{ __('messages.home.packages.express_web.title') }}</h3>
                            <p
                                class="text-gray-600 mb-6 leading-relaxed group-hover:text-gray-700 transition-colors duration-300">
                                {{ __('messages.home.packages.express_web.description') }}
                            </p>
                            <div class="mt-8 pt-6 border-t border-gray-100 flex items-center justify-between">
                                <p class="text-emerald-600 font-bold">{{ __('messages.home.starting_from', ['price' => '1.500.000']) }}</p>
                                <x-ui.button as="a" href="{{ route('pages.show', 'dich-vu/express-web') }}"
                                    variant="primary" size="xl"
                                    class="inline-flex items-center bg-gradient-to-r from-emerald-500 to-emerald-600 hover:from-emerald-600 hover:to-emerald-700 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-emerald-200">
                                    <span>{{ __('messages.home.learn_more') }}</span>
                                    <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                                        xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M14 5l7 7m0 0l-7 7m7-7H3"></path>
                                    </svg>
                                </x-ui.button>
                            </div>
                        </div>
                    </div>

                    <!-- Gói Pro Web - Featured -->
                    <div
                        class="group relative bg-gradient-to-br from-emerald-50 to-white p-8 rounded-xl shadow-xl transform transition-all duration-300 hover:-translate-y-3 hover:shadow-2xl border border-emerald-100 overflow-hidden">
                        <div
                            class="absolute top-0 right-0 px-4 py-1 bg-emerald-500 text-white text-sm font-medium rounded-bl-lg z-20">
                            {{ __('messages.home.popular_choice') }}</div>
                        <div
                            class="absolute -top-10 -right-10 w-32 h-32 bg-emerald-100 rounded-full opacity-0 group-hover:opacity-100 transition-all duration-300">
                        </div>
                        <div class="relative z-10">
                            <div
                                class="w-20 h-20 mx-auto mb-6 rounded-2xl bg-white shadow-md flex items-center justify-center text-4xl text-emerald-500 group-hover:bg-emerald-50 group-hover:scale-110 transition-all duration-300">
                                🚀
                            </div>
                            <h3
                                class="text-2xl font-bold mb-4 text-gray-800 group-hover:text-emerald-600 transition-colors duration-300">
                                {{ __('messages.home.packages.pro_web.title') }}</h3>
                            <p
                                class="text-gray-600 mb-6 leading-relaxed group-hover:text-gray-700 transition-colors duration-300">
                                {{ __('messages.home.packages.pro_web.description') }}
                            </p>
                            <div class="mt-8 pt-6 border-t border-emerald-100 flex items-center justify-between">
                                <p class="text-emerald-600 font-bold">{{ __('messages.home.starting_from', ['price' => '10.000.000']) }}</p>
                                <x-ui.button as="a" href="{{ route('pages.show', 'dich-vu/pro-web') }}"
                                    variant="primary" size="xl"
                                    class="inline-flex items-center bg-gradient-to-r from-emerald-500 to-emerald-600 hover:from-emerald-600 hover:to-emerald-700 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-emerald-200">
                                    <span>{{ __('messages.home.learn_more') }}</span>
                                    <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                                        xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M14 5l7 7m0 0l-7 7m7-7H3"></path>
                                    </svg>
                                </x-ui.button>
                            </div>
                        </div>
                    </div>

                    <!-- Gói E-Commerce -->
                    <div
                        class="group relative bg-white p-8 rounded-xl shadow-xl transform transition-all duration-300 hover:-translate-y-3 hover:shadow-2xl border border-gray-100 overflow-hidden">
                        <div
                            class="absolute -top-10 -right-10 w-32 h-32 bg-emerald-50 rounded-full opacity-0 group-hover:opacity-100 transition-all duration-300">
                        </div>
                        <div class="relative z-10">
                            <div
                                class="w-20 h-20 mx-auto mb-6 rounded-2xl bg-emerald-50 flex items-center justify-center text-4xl text-emerald-500 group-hover:bg-emerald-100 group-hover:scale-110 transition-all duration-300">
                                🛍️
                            </div>
                            <h3
                                class="text-2xl font-bold mb-4 text-gray-800 group-hover:text-emerald-600 transition-colors duration-300">
                                {{ __('messages.home.packages.e_commerce.title') }}</h3>
                            <p
                                class="text-gray-600 mb-6 leading-relaxed group-hover:text-gray-700 transition-colors duration-300">
                                {{ __('messages.home.packages.e_commerce.description') }}
                            </p>
                            <div class="mt-8 pt-6 border-t border-gray-100 flex items-center justify-between">
                                <p class="text-emerald-600 font-bold">{{ __('messages.home.starting_from', ['price' => '25.000.000']) }}</p>
                                <x-ui.button as="a" href="{{ route('pages.show', 'dich-vu/e-commerce') }}"
                                    variant="primary" size="xl"
                                    class="inline-flex items-center bg-gradient-to-r from-emerald-500 to-emerald-600 hover:from-emerald-600 hover:to-emerald-700 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-emerald-200">
                                    <span>{{ __('messages.home.learn_more') }}</span>
                                    <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                                        xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M14 5l7 7m0 0l-7 7m7-7H3"></path>
                                    </svg>
                                </x-ui.button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Second Row -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-5xl mx-auto">
                    <!-- Gói Website Care Plan -->
                    <div
                        class="group relative bg-white p-8 rounded-xl shadow-xl transform transition-all duration-300 hover:-translate-y-3 hover:shadow-2xl border border-gray-100 overflow-hidden">
                        <div
                            class="absolute -top-10 -right-10 w-32 h-32 bg-emerald-50 rounded-full opacity-0 group-hover:opacity-100 transition-all duration-300">
                        </div>
                        <div class="relative z-10">
                            <div
                                class="w-20 h-20 mx-auto mb-6 rounded-2xl bg-emerald-50 flex items-center justify-center text-4xl text-emerald-500 group-hover:bg-emerald-100 group-hover:scale-110 transition-all duration-300">
                                🛡️
                            </div>
                            <h3
                                class="text-2xl font-bold mb-4 text-gray-800 group-hover:text-emerald-600 transition-colors duration-300">
                                {{ __('messages.home.packages.website_care_plan.title') }}</h3>
                            <p
                                class="text-gray-600 mb-6 leading-relaxed group-hover:text-gray-700 transition-colors duration-300">
                                {{ __('messages.home.packages.website_care_plan.description') }}
                            </p>
                            <div class="mt-8 pt-6 border-t border-gray-100 flex items-center justify-between">
                                <p class="text-emerald-600 font-bold">{{ __('messages.home.starting_from_monthly', ['price' => '500.000']) }}</p>
                                <x-ui.button as="a" href="{{ route('pages.show', 'dich-vu/website-care-plan') }}"
                                    variant="primary" size="xl"
                                    class="inline-flex items-center bg-gradient-to-r from-emerald-500 to-emerald-600 hover:from-emerald-600 hover:to-emerald-700 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-emerald-200">
                                    <span>{{ __('messages.home.learn_more') }}</span>
                                    <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                                        xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M14 5l7 7m0 0l-7 7m7-7H3"></path>
                                    </svg>
                                </x-ui.button>
                            </div>
                        </div>
                    </div>

                    <!-- Gói Growth SEO -->
                    <div
                        class="group relative bg-white p-8 rounded-xl shadow-xl transform transition-all duration-300 hover:-translate-y-3 hover:shadow-2xl border border-gray-100 overflow-hidden">
                        <div
                            class="absolute -top-10 -right-10 w-32 h-32 bg-emerald-50 rounded-full opacity-0 group-hover:opacity-100 transition-all duration-300">
                        </div>
                        <div class="relative z-10">
                            <div
                                class="w-20 h-20 mx-auto mb-6 rounded-2xl bg-emerald-50 flex items-center justify-center text-4xl text-emerald-500 group-hover:bg-emerald-100 group-hover:scale-110 transition-all duration-300">
                                📈
                            </div>
                            <h3
                                class="text-2xl font-bold mb-4 text-gray-800 group-hover:text-emerald-600 transition-colors duration-300">
                                {{ __('messages.home.packages.growth_seo.title') }}</h3>
                            <p
                                class="text-gray-600 mb-6 leading-relaxed group-hover:text-gray-700 transition-colors duration-300">
                                {{ __('messages.home.packages.growth_seo.description') }}
                            </p>
                            <div class="mt-8 pt-6 border-t border-gray-100 flex items-center justify-between">
                                <p class="text-emerald-600 font-bold">{{ __('messages.home.starting_from_monthly', ['price' => '3.000.000']) }}</p>
                                <x-ui.button as="a" href="{{ route('pages.show', 'dich-vu/growth-seo') }}"
                                    variant="primary" size="xl"
                                    class="inline-flex items-center bg-gradient-to-r from-emerald-500 to-emerald-600 hover:from-emerald-600 hover:to-emerald-700 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-emerald-200">
                                    <span>{{ __('messages.home.learn_more') }}</span>
                                    <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                                        xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M14 5l7 7m0 0l-7 7m7-7H3"></path>
                                    </svg>
                                </x-ui.button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Featured Projects Section -->
        <section id="projects" class="bg-gray-50 py-16 swiper-container">
            <div class="container mx-auto px-6">
                <div class="flex flex-col md:flex-row md:justify-between md:items-center mb-12">
                    <div class="text-center md:text-left">
                        <h2 class="text-3xl md:text-4xl font-bold text-gray-800">{{ __('messages.home.featured_templates') }}</h2>
                        <p class="text-gray-500 mt-2">{{ __('messages.home.featured_description') }}</p>
                    </div>
                    <div class="mt-4 md:mt-0">
                        <a href="/mau-website-noi-bat"
                            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-emerald-700 bg-emerald-100 hover:bg-emerald-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-emerald-500">
                            {{ __('messages.home.view_all_templates') }}
                            <svg class="ml-2 -mr-1 w-4 h-4" fill="currentColor" viewBox="0 0 20 20"
                                xmlns="http://www.w3.org/2000/svg">
                                <path fill-rule="evenodd"
                                    d="M12.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-2.293-2.293a1 1 0 010-1.414z"
                                    clip-rule="evenodd"></path>
                            </svg>
                        </a>
                    </div>
                </div>
                <!-- Carousel Container -->
                <div class="relative">
                    <!-- Swiper -->
                    <div class="swiper featured-carousel pb-12">
                        <div class="swiper-wrapper">
                            @forelse($featuredTemplates as $template)
                                <x-card.template-card :template="$template" />
                            @empty
                                <div class="col-span-4 text-center py-8">
                                    <p class="text-gray-500">{{ __('messages.home.no_featured_templates') }}</p>
                                </div>
                            @endforelse
                        </div>
                        <!-- Pagination -->
                        <div class="swiper-pagination"></div>
                    </div>
                    <!-- Navigation buttons -->
                    <div class="swiper-button-next"></div>
                    <div class="swiper-button-prev"></div>
                </div>
            </div>
        </section>

        <!-- New Projects Section -->
        <section id="new-projects" class="py-16 swiper-container">
            <div class="container mx-auto px-6">
                <div class="flex flex-col md:flex-row md:justify-between md:items-center mb-12">
                    <div class="text-center md:text-left">
                        <h2 class="text-3xl md:text-4xl font-bold text-gray-800">{{ __('messages.home.new_templates') }}</h2>
                        <p class="text-gray-500 mt-2">{{ __('messages.home.new_description') }}</p>
                    </div>
                    <div class="mt-4 md:mt-0">
                        <a href="/mau-website-moi"
                            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-emerald-700 bg-emerald-100 hover:bg-emerald-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-emerald-500">
                            {{ __('messages.home.view_all_templates') }}
                            <svg class="ml-2 -mr-1 w-4 h-4" fill="currentColor" viewBox="0 0 20 20"
                                xmlns="http://www.w3.org/2000/svg">
                                <path fill-rule="evenodd"
                                    d="M12.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-2.293-2.293a1 1 0 010-1.414z"
                                    clip-rule="evenodd"></path>
                            </svg>
                        </a>
                    </div>
                </div>
                <!-- Carousel Container -->
                <div class="relative">
                    <!-- Swiper -->
                    <div class="swiper new-carousel pb-12">
                        <div class="swiper-wrapper">
                            @forelse($newTemplates as $template)
                                <x-card.template-card :template="$template" />
                            @empty
                                <div class="col-span-4 text-center py-8">
                                    <p class="text-gray-500">{{ __('messages.home.no_new_templates') }}</p>
                                </div>
                            @endforelse
                        </div>
                        <!-- Pagination -->
                        <div class="swiper-pagination"></div>
                    </div>
                    <!-- Navigation buttons -->
                    <div class="swiper-button-next"></div>
                    <div class="swiper-button-prev"></div>
                </div>
            </div>
        </section>

        <!-- Why Us Section -->
        <section id="why-us" class="py-16 bg-gray-50">
            <div class="container mx-auto px-6">
                <div class="text-center mb-12">
                    <h2 class="text-3xl md:text-4xl font-bold text-gray-800">{{ __('messages.home.why_choose_us') }}</h2>
                    <p class="text-gray-500 mt-2">{{ __('messages.home.why_choose_subtitle') }}</p>
                </div>
                <div class="max-w-6xl mx-auto grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <!-- Reason 1 -->
                    <div
                        class="bg-white p-8 rounded-xl shadow-md hover:shadow-xl transition-all duration-300 border border-gray-100 hover:border-emerald-100 group hover:-translate-y-1">
                        <div
                            class="w-12 h-12 bg-emerald-50 rounded-lg flex items-center justify-center mb-4 group-hover:bg-emerald-100 transition-colors duration-300">
                            <svg class="w-6 h-6 text-emerald-600" fill="none" stroke="currentColor"
                                viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z">
                                </path>
                            </svg>
                        </div>
                        <h3
                            class="font-bold text-xl text-gray-800 mb-3 group-hover:text-emerald-600 transition-colors duration-300">
                            {{ __('messages.home.competitive_price') }}</h3>
                        <p class="text-gray-600 leading-relaxed">{{ __('messages.home.competitive_price_desc') }}</p>
                    </div>
                    <!-- Reason 2 -->
                    <div
                        class="bg-white p-8 rounded-xl shadow-md hover:shadow-xl transition-all duration-300 border border-gray-100 hover:border-emerald-100 group hover:-translate-y-1">
                        <div
                            class="w-12 h-12 bg-emerald-50 rounded-lg flex items-center justify-center mb-4 group-hover:bg-emerald-100 transition-colors duration-300">
                            <svg class="w-6 h-6 text-emerald-600" fill="none" stroke="currentColor"
                                viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192l-3.536 3.536M21 12a9 9 0 11-18 0 9 9 0 0118 0zM12 15v-3m0 0V9m0 3H9m3 0h3">
                                </path>
                            </svg>
                        </div>
                        <h3
                            class="font-bold text-xl text-gray-800 mb-3 group-hover:text-emerald-600 transition-colors duration-300">
                            {{ __('messages.home.dedicated_support') }}</h3>
                        <p class="text-gray-600 leading-relaxed">{{ __('messages.home.dedicated_support_desc') }}</p>
                    </div>
                    <!-- Reason 3 -->
                    <div
                        class="bg-white p-8 rounded-xl shadow-md hover:shadow-xl transition-all duration-300 border border-gray-100 hover:border-emerald-100 group hover:-translate-y-1">
                        <div
                            class="w-12 h-12 bg-emerald-50 rounded-lg flex items-center justify-center mb-4 group-hover:bg-emerald-100 transition-colors duration-300">
                            <svg class="w-6 h-6 text-emerald-600" fill="none" stroke="currentColor"
                                viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9">
                                </path>
                            </svg>
                        </div>
                        <h3
                            class="font-bold text-xl text-gray-800 mb-3 group-hover:text-emerald-600 transition-colors duration-300">
                            {{ __('messages.home.seo_optimized') }}</h3>
                        <p class="text-gray-600 leading-relaxed">{{ __('messages.home.seo_optimized_desc') }}</p>
                    </div>
                    <!-- Reason 4 -->
                    <div
                        class="bg-white p-8 rounded-xl shadow-md hover:shadow-xl transition-all duration-300 border border-gray-100 hover:border-emerald-100 group hover:-translate-y-1">
                        <div
                            class="w-12 h-12 bg-emerald-50 rounded-lg flex items-center justify-center mb-4 group-hover:bg-emerald-100 transition-colors duration-300">
                            <svg class="w-6 h-6 text-emerald-600" fill="none" stroke="currentColor"
                                viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z">
                                </path>
                            </svg>
                        </div>
                        <h3
                            class="font-bold text-xl text-gray-800 mb-3 group-hover:text-emerald-600 transition-colors duration-300">
                            {{ __('messages.home.modern_design') }}</h3>
                        <p class="text-gray-600 leading-relaxed">{{ __('messages.home.modern_design_desc') }}</p>
                    </div>
                    <!-- Reason 5 -->
                    <div
                        class="bg-white p-8 rounded-xl shadow-md hover:shadow-xl transition-all duration-300 border border-gray-100 hover:border-emerald-100 group hover:-translate-y-1">
                        <div
                            class="w-12 h-12 bg-emerald-50 rounded-lg flex items-center justify-center mb-4 group-hover:bg-emerald-100 transition-colors duration-300">
                            <svg class="w-6 h-6 text-emerald-600" fill="none" stroke="currentColor"
                                viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                            </svg>
                        </div>
                        <h3
                            class="font-bold text-xl text-gray-800 mb-3 group-hover:text-emerald-600 transition-colors duration-300">
                            {{ __('messages.home.fast_secure') }}</h3>
                        <p class="text-gray-600 leading-relaxed">{{ __('messages.home.fast_secure_desc') }}</p>
                    </div>
                    <!-- Reason 6 -->
                    <div
                        class="bg-white p-8 rounded-xl shadow-md hover:shadow-xl transition-all duration-300 border border-gray-100 hover:border-emerald-100 group hover:-translate-y-1">
                        <div
                            class="w-12 h-12 bg-emerald-50 rounded-lg flex items-center justify-center mb-4 group-hover:bg-emerald-100 transition-colors duration-300">
                            <svg class="w-6 h-6 text-emerald-600" fill="none" stroke="currentColor"
                                viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z">
                                </path>
                            </svg>
                        </div>
                        <h3
                            class="font-bold text-xl text-gray-800 mb-3 group-hover:text-emerald-600 transition-colors duration-300">
                            {{ __('messages.home.transparent_consulting_title') }}</h3>
                        <p class="text-gray-600 leading-relaxed">{{ __('messages.home.transparent_consulting_desc') }}</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Testimonials Section -->
        <section id="testimonials"
            class="py-16 bg-white bg-[url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiB2aWV3Qm94PSIwIDAgMTAwIDEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48ZyBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPjxnIGZpbGw9IiNmOGZhZmEiIGZpbGwtb3BhY2l0eT0iMC4zIj48cmVjdCB3aWR0aD0iMTAiIGhlaWdodD0iMTAiIHg9IjAiIHk9IjAiIGZpbGw9IiNmOGZhZmEiLz48L2c+PC9nPjwvc3ZnPg==')]">
            <div class="container mx-auto px-6">
                <div class="text-center mb-12">
                    <h2 class="text-3xl md:text-4xl font-bold text-gray-800 mb-3">{{ __('messages.home.testimonials_title') }}</h2>
                    <p class="text-gray-500 max-w-2xl mx-auto">{{ __('messages.home.testimonials_subtitle') }}</p>
                </div>

                <!-- Testimonial Slider -->
                <div class="relative">
                    <div class="swiper testimonial-swiper pb-16 px-2">
                        <div class="swiper-wrapper">
                            <!-- Testimonial 1 -->
                            <div class="swiper-slide">
                                <div
                                    class="bg-white p-8 rounded-xl shadow-lg border border-gray-100 h-full transform transition-all duration-300 hover:shadow-xl hover:-translate-y-1">
                                    <div class="flex items-center mb-6">
                                        <img src="https://randomuser.me/api/portraits/women/44.jpg" alt="{{ __('messages.home.customer_alt') }}"
                                            class="w-14 h-14 rounded-full object-cover border-2 border-emerald-100">
                                        <div class="ml-4">
                                            <h4 class="font-bold text-gray-800">Chị Ngọc Anh</h4>
                                            <p class="text-emerald-600 text-sm">{{ __('messages.home.testimonial_owner') }}</p>
                                        </div>
                                        <div class="ml-auto text-yellow-400 text-2xl">{{ __('messages.home.star_rating') }}</div>
                                    </div>
                                    <p class="text-gray-600 leading-relaxed">"{{ __('messages.home.testimonial_text_1') }}"</p>
                                </div>
                            </div>

                            <!-- Testimonial 2 -->
                            <div class="swiper-slide">
                                <div
                                    class="bg-white p-8 rounded-xl shadow-lg border border-gray-100 h-full transform transition-all duration-300 hover:shadow-xl hover:-translate-y-1">
                                    <div class="flex items-center mb-6">
                                        <img src="https://randomuser.me/api/portraits/men/32.jpg" alt="{{ __('messages.home.customer_alt') }}"
                                            class="w-14 h-14 rounded-full object-cover border-2 border-emerald-100">
                                        <div class="ml-4">
                                            <h4 class="font-bold text-gray-800">Anh Minh Đức</h4>
                                            <p class="text-emerald-600 text-sm">{{ __('messages.home.testimonial_role_2') }}</p>
                                        </div>
                                        <div class="ml-auto text-yellow-400 text-2xl">{{ __('messages.home.star_rating') }}</div>
                                    </div>
                                    <p class="text-gray-600 leading-relaxed">"{{ __('messages.home.testimonial_text_2') }}"</p>
                                </div>
                            </div>

                            <!-- Testimonial 3 -->
                            <div class="swiper-slide">
                                <div
                                    class="bg-white p-8 rounded-xl shadow-lg border border-gray-100 h-full transform transition-all duration-300 hover:shadow-xl hover:-translate-y-1">
                                    <div class="flex items-center mb-6">
                                        <img src="https://randomuser.me/api/portraits/women/68.jpg" alt="{{ __('messages.home.customer_alt') }}"
                                            class="w-14 h-14 rounded-full object-cover border-2 border-emerald-100">
                                        <div class="ml-4">
                                            <h4 class="font-bold text-gray-800">Chị Hương Giang</h4>
                                            <p class="text-emerald-600 text-sm">{{ __('messages.home.testimonial_role_3') }}</p>
                                        </div>
                                        <div class="ml-auto text-yellow-400 text-2xl">{{ __('messages.home.star_rating') }}</div>
                                    </div>
                                    <p class="text-gray-600 leading-relaxed">"{{ __('messages.home.testimonial_text_3') }}"</p>
                                </div>
                            </div>

                            <!-- Testimonial 4 -->
                            <div class="swiper-slide">
                                <div
                                    class="bg-white p-8 rounded-xl shadow-lg border border-gray-100 h-full transform transition-all duration-300 hover:shadow-xl hover:-translate-y-1">
                                    <div class="flex items-center mb-6">
                                        <img src="https://randomuser.me/api/portraits/men/45.jpg" alt="{{ __('messages.home.customer_alt') }}"
                                            class="w-14 h-14 rounded-full object-cover border-2 border-emerald-100">
                                        <div class="ml-4">
                                            <h4 class="font-bold text-gray-800">Anh Tuấn Anh</h4>
                                            <p class="text-emerald-600 text-sm">{{ __('messages.home.testimonial_role_4') }}</p>
                                        </div>
                                        <div class="ml-auto text-yellow-400 text-2xl">{{ __('messages.home.star_rating') }}</div>
                                    </div>
                                    <p class="text-gray-600 leading-relaxed">"{{ __('messages.home.testimonial_text_4') }}"</p>
                                </div>
                            </div>

                            <!-- Testimonial 5 -->
                            <div class="swiper-slide">
                                <div
                                    class="bg-white p-8 rounded-xl shadow-lg border border-gray-100 h-full transform transition-all duration-300 hover:shadow-xl hover:-translate-y-1">
                                    <div class="flex items-center mb-6">
                                        <img src="https://randomuser.me/api/portraits/women/52.jpg" alt="{{ __('messages.home.customer_alt') }}"
                                            class="w-14 h-14 rounded-full object-cover border-2 border-emerald-100">
                                        <div class="ml-4">
                                            <h4 class="font-bold text-gray-800">Chị Thanh Hà</h4>
                                            <p class="text-emerald-600 text-sm">{{ __('messages.home.testimonial_role_5') }}</p>
                                        </div>
                                        <div class="ml-auto text-yellow-400 text-2xl">{{ __('messages.home.star_rating') }}</div>
                                    </div>
                                    <p class="text-gray-600 leading-relaxed">"{{ __('messages.home.testimonial_text_5') }}"</p>
                                </div>
                            </div>

                            <!-- Testimonial 6 -->
                            <div class="swiper-slide">
                                <div
                                    class="bg-white p-8 rounded-xl shadow-lg border border-gray-100 h-full transform transition-all duration-300 hover:shadow-xl hover:-translate-y-1">
                                    <div class="flex items-center mb-6">
                                        <img src="https://randomuser.me/api/portraits/men/67.jpg" alt="{{ __('messages.home.customer_alt') }}"
                                            class="w-14 h-14 rounded-full object-cover border-2 border-emerald-100">
                                        <div class="ml-4">
                                            <h4 class="font-bold text-gray-800">Anh Trung Kiên</h4>
                                            <p class="text-emerald-600 text-sm">{{ __('messages.home.testimonial_role_6') }}</p>
                                        </div>
                                        <div class="ml-auto text-yellow-400 text-2xl">{{ __('messages.home.star_rating') }}</div>
                                    </div>
                                    <p class="text-gray-600 leading-relaxed">"{{ __('messages.home.testimonial_text_6') }}"</p>
                                </div>
                            </div>
                        </div>
                        <!-- Add Pagination -->
                        <div class="swiper-pagination mt-8"></div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Blog Section (3 columns, mỗi cột 1 danh mục) -->
        <section id="blog" class="py-16 bg-gray-50">
            <div class="container mx-auto px-6">
                <div class="text-center mb-12">
                    <h2 class="text-3xl md:text-4xl font-bold text-gray-800">{{ __('messages.home.blog_title') }}</h2>
                    <p class="text-gray-500 mt-2">{{ __('messages.home.blog_subtitle') }}</p>
                </div>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                    @foreach($blogColumns as $column)
                        <div class="bg-white rounded-xl shadow-lg border border-gray-100 flex flex-col h-full">
                            <div class="p-6 pb-2 border-b border-gray-50">
                                <h3 class="text-xl font-bold text-emerald-700 mb-1">{{ $column['title'] }}</h3>
                                @if($column['category'] && $column['category']->description)
                                    <p class="text-gray-500 text-sm mb-2 line-clamp-2 overflow-hidden text-ellipsis min-h-[2.5rem] leading-snug">{{ $column['category']->description }}</p>
                                @endif
                            </div>
                            <div class="flex-1 flex flex-col justify-between">
                                <ul class="divide-y divide-gray-100">
                                    @forelse($column['posts'] as $post)
                                        <li class="p-5 hover:bg-emerald-50 transition group">
                                            <a href="{{ route('consultation.detail', ['category_slug' => $post->category->slug, 'detail_slug' => $post->slug]) }}" class="block">
                                                <div class="flex items-center gap-3 mb-1">
                                                    @if($post->featured_image_url)
                                                        <img src="{{ $post->featured_image_url }}" alt="{{ $post->title }}" class="w-14 h-14 rounded-lg object-cover border border-gray-100">
                                                    @else
                                                        <div class="w-14 h-14 rounded-lg bg-gray-100 flex items-center justify-center text-gray-400">
                                                            <i class="fa-regular fa-image text-2xl"></i>
                                                        </div>
                                                    @endif
                                                    <div class="flex-1">
                                                        <h4 class="font-semibold text-gray-800 group-hover:text-emerald-700 transition line-clamp-2 overflow-hidden text-ellipsis min-h-[2.5rem] leading-snug">{{ $post->title }}</h4>
                                                        <div class="text-xs text-gray-500 mt-1 flex items-center gap-2">
                                                            <span>{{ $post->published_at?->format('d/m/Y') }}</span>
                                                            <span class="hidden md:inline">•</span>
                                                            <span class="line-clamp-1">{{ $post->summary ? Str::limit($post->summary, 40) : '' }}</span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </a>
                                        </li>
                                    @empty
                                        <li class="p-5 text-gray-400 italic">{{ __('messages.home.no_posts_yet') }}</li>
                                    @endforelse
                                </ul>
                                <div class="p-6 pt-3 mt-auto text-center">
                                    <x-ui.button variant="secondary" href="{{ $column['view_more_url'] }}">{{ __('messages.navigation.view_more') }}</x-ui.button>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
        </section>

        <!-- Contact Section -->
        <section id="contact" class="bg-white py-16">
            <div class="container mx-auto px-6">
                <h2 class="text-3xl md:text-4xl font-bold text-gray-800 text-center mb-12">{{ __('messages.home.ready_title') }}
                </h2>

                <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                    <!-- Mission Section -->
                    <div class="pr-0 lg:pr-8">
                        <h3 class="text-2xl font-bold text-emerald-600 mb-4">{{ __('messages.home.mission_title') }}</h3>
                        <p class="text-gray-700 mb-8">{{ __('messages.home.mission_description') }}</p>

                        <div class="space-y-6">
                            <div class="flex items-start">
                                <div class="flex-shrink-0 bg-emerald-100 p-2 rounded-full">
                                    <i class="fas fa-bullseye text-emerald-600"></i>
                                </div>
                                <div class="ml-4">
                                    <h4 class="font-semibold text-gray-800">{{ __('messages.home.vision_title') }}</h4>
                                    <p class="text-gray-600 mt-1">{{ __('messages.home.vision_description') }}</p>
                                </div>
                            </div>

                            <div class="flex items-start">
                                <div class="flex-shrink-0 bg-emerald-100 p-2 rounded-full">
                                    <i class="fas fa-hand-holding-heart text-emerald-600"></i>
                                </div>
                                <div class="ml-4">
                                    <h4 class="font-semibold text-gray-800">{{ __('messages.home.core_values_title') }}</h4>
                                    <p class="text-gray-600 mt-1">{{ __('messages.home.core_values_description') }}
                                        tranh - Hỗ trợ 24/7.</p>
                                </div>
                            </div>

                            <div class="flex items-start">
                                <div class="flex-shrink-0 bg-emerald-100 p-2 rounded-full">
                                    <i class="fas fa-rocket text-emerald-600"></i>
                                </div>
                                <div class="ml-4">
                                    <h4 class="font-semibold text-gray-800">{{ __('messages.home.commitment_title') }}</h4>
                                    <p class="text-gray-600 mt-1">{{ __('messages.home.commitment_description') }}</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Contact Form -->
                    <div class="bg-white p-8 rounded-lg shadow-lg border border-gray-100">
                        <h3 class="text-2xl font-bold text-gray-800 mb-6">{{ __('messages.home.contact_form_title') }}</h3>
                        <p class="text-gray-600 mb-6">{{ __('messages.home.contact_form_subtitle') }}</p>

                        <form>
                            <div class="mb-4">
                                <label for="company" class="block text-sm font-medium text-gray-700 mb-1">{{ __('messages.home.contact_form_company') }}</label>
                                <input type="text" id="company" name="company"
                                    class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-all duration-200"
                                    placeholder="{{ __('messages.home.contact_form_company') }}">
                            </div>
                            <div class="mb-4">
                                <label for="name" class="block text-sm font-medium text-gray-700 mb-1">{{ __('messages.home.contact_form_name') }} <span
                                        class="text-red-500">*</span></label>
                                <input type="text" id="name" name="name" required
                                    class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-all duration-200"
                                    placeholder="{{ __('messages.home.contact_form_name') }}">
                            </div>
                            <div class="mb-4">
                                <label for="phone" class="block text-sm font-medium text-gray-700 mb-1">{{ __('messages.home.contact_form_phone') }} <span class="text-red-500">*</span></label>
                                <x-form.input name="phone" :placeholder="__('messages.home.contact_form_phone')" required />
                            </div>
                            <div class="mb-6">
                                <label for="message" class="block text-sm font-medium text-gray-700 mb-1">{{ __('messages.home.contact_form_message') }}</label>
                                <x-form.textarea name="message" :placeholder="__('messages.home.contact_form_message')" />
                            </div>
                            <div>
                                <button type="submit"
                                    class="w-full bg-emerald-600 text-white font-bold py-3 px-8 rounded-lg hover:bg-emerald-700 transition duration-300">
                                    {{ __('messages.home.contact_form_button') }}
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </section>
    </main>
@endsection
