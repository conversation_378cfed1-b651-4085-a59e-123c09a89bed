@php
use RalphJSmit\Laravel\SEO\Support\SEOData;

$SEOData = new SEOData(
    title: __('messages.express_web.seo.title'),
    description: __('messages.express_web.seo.description'),
);
@endphp

@extends('layouts.default')

@section('content')
<main class="py-16 bg-gray-50 min-h-screen">
    <div class="container mx-auto px-6">
        <!-- Introduction -->
        <div class="text-center mb-12">
            <h1 class="text-4xl md:text-5xl font-bold text-emerald-700 mb-4">{{ __('messages.express_web.hero.title') }}</h1>
            <p class="text-gray-500 max-w-2xl mx-auto">{{ __('messages.express_web.hero.description') }}</p>
        </div>
        
        <!-- Key Features -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
            <div class="bg-white rounded-xl shadow-md p-8 text-center flex flex-col items-center border border-gray-100">
                <div class="w-16 h-16 flex items-center justify-center rounded-full bg-emerald-50 mb-4">
                    <i class="fa fa-bolt text-3xl text-emerald-600"></i>
                </div>
                <h3 class="text-xl font-bold text-gray-800 mb-2">{{ __('messages.express_web.features.fast_deployment.title') }}</h3>
                <p class="text-gray-600">{{ __('messages.express_web.features.fast_deployment.description') }}</p>
            </div>
            <div class="bg-white rounded-xl shadow-md p-8 text-center flex flex-col items-center border border-gray-100">
                <div class="w-16 h-16 flex items-center justify-center rounded-full bg-emerald-50 mb-4">
                    <i class="fa fa-laptop-code text-3xl text-emerald-600"></i>
                </div>
                <h3 class="text-xl font-bold text-gray-800 mb-2">{{ __('messages.express_web.features.seo_responsive.title') }}</h3>
                <p class="text-gray-600">{{ __('messages.express_web.features.seo_responsive.description') }}</p>
            </div>
            <div class="bg-white rounded-xl shadow-md p-8 text-center flex flex-col items-center border border-gray-100">
                <div class="w-16 h-16 flex items-center justify-center rounded-full bg-emerald-50 mb-4">
                    <i class="fa fa-cogs text-3xl text-emerald-600"></i>
                </div>
                <h3 class="text-xl font-bold text-gray-800 mb-2">{{ __('messages.express_web.features.easy_management.title') }}</h3>
                <p class="text-gray-600">{{ __('messages.express_web.features.easy_management.description') }}</p>
            </div>
            <div class="bg-white rounded-xl shadow-md p-8 text-center flex flex-col items-center border border-gray-100">
                <div class="w-16 h-16 flex items-center justify-center rounded-full bg-emerald-50 mb-4">
                    <i class="fa fa-sitemap text-3xl text-emerald-600"></i>
                </div>
                <h3 class="text-xl font-bold text-gray-800 mb-2">{{ __('messages.express_web.features.complete_structure.title') }}</h3>
                <p class="text-gray-600">{{ __('messages.express_web.features.complete_structure.description') }}</p>
            </div>
            <div class="bg-white rounded-xl shadow-md p-8 text-center flex flex-col items-center border border-gray-100">
                <div class="w-16 h-16 flex items-center justify-center rounded-full bg-emerald-50 mb-4">
                    <i class="fa fa-star text-3xl text-emerald-600"></i>
                </div>
                <h3 class="text-xl font-bold text-gray-800 mb-2">Tính năng cần thiết</h3>
                <p class="text-gray-600">Gồm các tính năng cơ bản (form liên hệ, bản đồ, nút nhắn tin, gọi điện).</p>
            </div>
            <div class="bg-white rounded-xl shadow-md p-8 text-center flex flex-col items-center border border-gray-100">
                <div class="w-16 h-16 flex items-center justify-center rounded-full bg-emerald-50 mb-4">
                    <i class="fa fa-shield-alt text-3xl text-emerald-600"></i>
                </div>
                <h3 class="text-xl font-bold text-gray-800 mb-2">Bảo mật cao</h3>
                <p class="text-gray-600">Đảm bảo an toàn dữ liệu với chứng chỉ SSL và cập nhật bảo mật thường xuyên.</p>
            </div>
        </div>

        <!-- Quà tặng hấp dẫn -->
        <div class="relative bg-gradient-to-r from-emerald-500 to-teal-500 rounded-2xl p-8 md:p-10 text-white mb-16 overflow-hidden">
            <div class="relative z-10">
                <div class="flex flex-col md:flex-row items-center justify-between">
                    <div class="md:w-2/3 mb-6 md:mb-0">
                        <div class="flex items-center mb-3">
                            <i class="fa fa-gift text-2xl mr-3"></i>
                            <h2 class="text-2xl font-bold">QUÀ TẶNG HẤP DẪN</h2>
                        </div>
                        <p class="text-lg md:text-xl font-medium mb-2">Đăng ký ngay hôm nay nhận ngay:</p>
                        <ul class="space-y-2 mb-4">
                            <li class="flex items-start">
                                <i class="fa fa-check-circle mt-1 mr-2"></i>
                                <span>Miễn phí tên miền quốc tế (.com/.net) trị giá 280.000đ/năm</span>
                            </li>
                            <li class="flex items-start">
                                <i class="fa fa-check-circle mt-1 mr-2"></i>
                                <span>Hosting tốc độ cao băng thông không giới hạn năm đầu tiên</span>
                            </li>
                            <li class="flex items-start">
                                <i class="fa fa-check-circle mt-1 mr-2"></i>
                                <span>Hỗ trợ kỹ thuật 24/7 trong suốt quá trình sử dụng</span>
                            </li>
                        </ul>
                        <button x-data @click="$store.modal.open()" class="inline-block bg-white text-emerald-600 hover:bg-gray-100 hover:cursor-pointer font-bold py-3 px-6 rounded-full transition mt-2">
                            Đăng ký ngay <i class="fa fa-arrow-right ml-2"></i>
                        </button>
                    </div>
                    <div class="md:w-1/3 flex justify-center">
                        <div class="relative">
                            <div class="w-40 h-40 md:w-48 md:h-48 bg-white/20 rounded-full flex items-center justify-center">
                                <i class="fa fa-gift text-6xl md:text-7xl text-white/80"></i>
                            </div>
                            <div class="absolute -top-4 -right-4 w-16 h-16 bg-yellow-400 rounded-full flex items-center justify-center text-yellow-800 font-bold text-sm transform rotate-12">
                                FREE
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- Hiệu ứng nền -->
            <div class="absolute top-0 right-0 w-full h-full opacity-10">
                <div class="absolute top-1/2 right-0 transform translate-x-1/2 -translate-y-1/2 w-64 h-64 rounded-full bg-white"></div>
            </div>
        </div>

        <!-- Quy trình thực hiện -->
        <div class="mb-16">
            <h2 class="text-2xl font-bold text-emerald-700 mb-8 text-center">Quy trình triển khai Express Web</h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-10">
                <div class="bg-white rounded-xl shadow-md border border-gray-100 p-8 text-center flex flex-col items-center">
                    <div class="w-12 h-12 flex items-center justify-center rounded-full bg-emerald-50 mb-4">
                        <i class="fa fa-comments text-2xl text-emerald-600"></i>
                    </div>
                    <h3 class="font-bold text-gray-800 mb-1">1. Tư vấn & tiếp nhận yêu cầu</h3>
                    <p class="text-gray-600">Lắng nghe mục tiêu, tư vấn giải pháp phù hợp.</p>
                </div>
                <div class="bg-white rounded-xl shadow-md border border-gray-100 p-8 text-center flex flex-col items-center">
                    <div class="w-12 h-12 flex items-center justify-center rounded-full bg-emerald-50 mb-4">
                        <i class="fa fa-pencil-ruler text-2xl text-emerald-600"></i>
                    </div>
                    <h3 class="font-bold text-gray-800 mb-1">2. Lựa chọn mẫu giao diện</h3>
                    <p class="text-gray-600">Lựa chọn mẫu phù hợp, chỉnh sửa theo nhận diện thương hiệu.</p>
                </div>
                <div class="bg-white rounded-xl shadow-md border border-gray-100 p-8 text-center flex flex-col items-center">
                    <div class="w-12 h-12 flex items-center justify-center rounded-full bg-emerald-50 mb-4">
                        <i class="fa fa-code text-2xl text-emerald-600"></i>
                    </div>
                    <h3 class="font-bold text-gray-800 mb-1">3. Cài đặt nội dung & bàn giao</h3>
                    <p class="text-gray-600">Cài đặt nội dung, hướng dẫn sử dụng và bàn giao website.</p>
                </div>
            </div>
        </div>
        <!-- Bảng giá -->
        <div class="mb-16">
            <h2 class="text-2xl font-bold text-emerald-700 mb-8 text-center">{{ __('messages.express_web.pricing.title') }}</h2>
            <p class="text-center text-gray-500 mb-6">{{ __('messages.express_web.pricing.subtitle') }}</p>
            <div class="overflow-x-auto border border-gray-200 rounded-2xl">
                <table class="min-w-full bg-white text-sm md:text-base divide-y divide-gray-200">
                    <thead class="bg-emerald-100 text-emerald-700">
                        <tr>
                            <th class="py-4 px-4 font-bold text-left"></th>
                            <th class="py-4 px-4 font-bold text-center w-1/4">{{ __('messages.express_web.pricing.plans.basic.name') }}<br><span class="italic text-xs">{{ __('messages.express_web.pricing.plans.basic.description') }}</span></th>
                            <th class="py-4 px-4 font-bold text-center w-1/4">GÓI PLUS<br><span class="italic text-xs">Gói nâng cấp hoàn hảo dành cho khách hàng đang sử dụng gói basic</span></th>
                            <th class="py-4 px-4 font-bold text-center w-1/4">GÓI ULTIMATE<br><span class="italic text-xs">Giải pháp toàn diện khi đã hiểu về website và mong muốn làm chủ trang web.</span></th>
                        </tr>
                    </thead>
                    <tbody class="divide-y divide-gray-200">
                        <tr class="bg-emerald-50">
                            <td class="py-3 px-4 font-bold text-left">{{ __('messages.express_web.pricing.cost') }}</td>
                            <td class="py-3 px-4 font-bold text-center text-emerald-600 text-xl">{{ __('messages.express_web.pricing.plans.basic.price') }}</td>
                            <td class="py-3 px-4 font-bold text-center text-emerald-600 text-xl">3.000.000đ</td>
                            <td class="py-3 px-4 font-bold text-center text-emerald-600 text-xl">5.000.000đ</td>
                        </tr>
                        <tr class="bg-gray-50">
                            <td class="py-3 px-4 font-semibold text-left">Giao diện (Theme)</td>
                            <td class="py-3 px-4 text-center">Giao diện theo mẫu có sẵn</td>
                            <td class="py-3 px-4 text-center">Giao diện theo mẫu có sẵn</td>
                            <td class="py-3 px-4 text-center">Giao diện theo mẫu có sẵn</td>
                        </tr>
                        <tr>
                            <td class="py-3 px-4 font-semibold text-left">Thời gian bàn giao</td>
                            <td class="py-3 px-4 text-center">3 - 5 ngày</td>
                            <td class="py-3 px-4 text-center">5 - 7 ngày</td>
                            <td class="py-3 px-4 text-center">7 - 15 ngày</td>
                        </tr>
                        <tr><td colspan="4" class="py-2 px-4 text-emerald-700 font-bold text-center">NỘI DUNG & SẢN PHẨM</td></tr>
                        <tr class="bg-gray-50">
                            <td class="py-3 px-4 font-semibold text-left">Số trang con tạo thêm</td>
                            <td class="py-3 px-4 text-center">5 trang</td>
                            <td class="py-3 px-4 text-center">15 trang</td>
                            <td class="py-3 px-4 text-center font-bold">Không giới hạn</td>
                        </tr>
                        <tr>
                            <td class="py-3 px-4 font-semibold text-left">Số danh mục bài viết</td>
                            <td class="py-3 px-4 text-center">5 danh mục</td>
                            <td class="py-3 px-4 text-center">15 danh mục</td>
                            <td class="py-3 px-4 text-center font-bold">Không giới hạn</td>
                        </tr>
                        <tr class="bg-gray-50">
                            <td class="py-3 px-4 font-semibold text-left">Đăng tải bài viết</td>
                            <td class="py-3 px-4 text-center font-bold">Không giới hạn</td>
                            <td class="py-3 px-4 text-center font-bold">Không giới hạn</td>
                            <td class="py-3 px-4 text-center font-bold">Không giới hạn</td>
                        </tr>
                        <tr>
                            <td class="py-3 px-4 font-semibold text-left">Số sản phẩm (TMĐT)</td>
                            <td class="py-3 px-4 text-center">30 sản phẩm</td>
                            <td class="py-3 px-4 text-center">100 sản phẩm</td>
                            <td class="py-3 px-4 text-center font-bold">Không giới hạn</td>
                        </tr>
                        <tr><td colspan="4" class="py-2 px-4 text-emerald-700 font-bold text-center">TÍNH NĂNG & TỐI ƯU</td></tr>
                        <tr class="bg-gray-50">
                            <td class="py-3 px-4 font-semibold text-left">Bộ plugin CSlant Suite</td>
                            <td class="py-3 px-4 text-center font-bold">Bản quyền</td>
                            <td class="py-3 px-4 text-center font-bold">Bản quyền</td>
                            <td class="py-3 px-4 text-center font-bold">Bản quyền</td>
                        </tr>
                        <tr>
                            <td class="py-3 px-4 font-semibold text-left">Tối ưu SEO On-page</td>
                            <td class="py-3 px-4 text-center">✅ Cơ bản</td>
                            <td class="py-3 px-4 text-center">✅ Nâng cao</td>
                            <td class="py-3 px-4 text-center font-bold">✅ Toàn diện & Tư vấn từ khóa</td>
                        </tr>
                        <tr class="bg-gray-50">
                            <td class="py-3 px-4 font-semibold text-left">Tối ưu tốc độ</td>
                            <td class="py-3 px-4 text-center">✅ Cơ bản</td>
                            <td class="py-3 px-4 text-center">✅ Nâng cao</td>
                            <td class="py-3 px-4 text-center font-bold">✅ Chuyên sâu & Cấu hình CDN</td>
                        </tr>
                        <tr>
                            <td class="py-3 px-4 font-semibold text-left">{{ __('messages.express_web.pricing.responsive') }}</td>
                            <td class="py-3 px-4 text-center">{{ __('messages.express_web.pricing.included') }}</td>
                            <td class="py-3 px-4 text-center">✅ Có</td>
                            <td class="py-3 px-4 text-center">✅ Có</td>
                        </tr>
                        <tr class="bg-gray-50">
                            <td class="py-3 px-4 font-semibold text-left">Bảo mật</td>
                            <td class="py-3 px-4 text-center">✅ Cơ bản</td>
                            <td class="py-3 px-4 text-center font-bold">✅ Tường lửa & Quét mã độc</td>
                            <td class="py-3 px-4 text-center font-bold">✅ Bảo mật đa lớp & Giám sát</td>
                        </tr>
                        <tr>
                            <td class="py-3 px-4 font-semibold text-left">Cổng thanh toán Online</td>
                            <td class="py-3 px-4 text-center">1 Cổng (Chuyển khoản)</td>
                            <td class="py-3 px-4 text-center">3 Cổng (Chuyển khoản, Ví điện tử, QR Code)</td>
                            <td class="py-3 px-4 text-center font-bold">Không giới hạn</td>
                        </tr>
                        <tr><td colspan="4" class="py-2 px-4 text-emerald-700 font-bold text-center">HỖ TRỢ & BẢO HÀNH</td></tr>
                        <tr class="bg-gray-50">
                            <td class="py-3 px-4 font-semibold text-left">Hướng dẫn quản trị</td>
                            <td class="py-3 px-4 text-center">Tài liệu hướng dẫn</td>
                            <td class="py-3 px-4 text-center">Tài liệu hướng dẫn + <b>1 buổi training online</b></td>
                            <td class="py-3 px-4 text-center">Tài liệu hướng dẫn + <b>3 buổi training online</b></td>
                        </tr>
                        <tr>
                            <td class="py-3 px-4 font-semibold text-left">Hỗ trợ kỹ thuật</td>
                            <td class="py-3 px-4 text-center">Giờ hành chính</td>
                            <td class="py-3 px-4 text-center">Giờ hành chính</td>
                            <td class="py-3 px-4 text-center font-bold">Hỗ trợ 24/7</td>
                        </tr>
                        <tr><td colspan="4" class="py-2 px-4 text-emerald-700 font-bold text-center">CHI PHÍ DUY TRÌ</td></tr>
                        <tr class="bg-gray-50">
                            <td class="py-3 px-4 font-semibold text-left">Phí duy trì hosting & domain</td>
                            <td class="py-3 px-4 text-center">Miễn phí năm đầu: <br>- Hosting Gói A2 <br>- Domain (.com/.net)</td>
                            <td class="py-3 px-4 text-center">Miễn phí năm đầu: <br>- Hosting Gói A2 <br>- Domain (.com/.net)</td>
                            <td class="py-3 px-4 text-center">Miễn phí năm đầu: <br>- Hosting Gói A2 <br>- Domain (.com/.net)</td>
                        </tr>
                        <tr class="bg-white">
                            <td></td>
                            <td class="py-4 px-4 text-center">
                                <button x-data @click="$store.modal.open()" class="inline-block bg-emerald-600 text-white px-6 py-2 rounded-full font-bold shadow hover:bg-emerald-700 hover:cursor-pointer transition">ĐĂNG KÝ NGAY</button>
                            </td>
                            <td class="py-4 px-4 text-center">
                                <button x-data @click="$store.modal.open()" class="inline-block bg-emerald-600 text-white px-6 py-2 rounded-full font-bold shadow hover:bg-emerald-700 hover:cursor-pointer transition">ĐĂNG KÝ NGAY</button>
                            </td>
                            <td class="py-4 px-4 text-center font-bold">
                                <button x-data @click="$store.modal.open()" class="inline-block bg-yellow-400 text-yellow-900 px-6 py-2 rounded-full font-bold shadow hover:bg-yellow-500 hover:cursor-pointer transition">CHỌN GÓI TỐT NHẤT</button>
                                <br><span class="block mt-2 text-xs font-semibold">✨ BEST VALUE ✨</span>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Câu hỏi thường gặp -->
        <div class="mb-16">
            <h2 class="text-2xl font-bold text-emerald-700 mb-8 text-center">{{ __('messages.express_web.faq.title') }}</h2>
            <div class="max-w-3xl mx-auto">
                <div class="space-y-6">
                    <div class="bg-white rounded-xl shadow-md border border-gray-100 p-6">
                        <div class="flex items-start">
                            <div class="w-10 h-10 flex items-center justify-center rounded-full bg-emerald-50 mr-4 mt-1">
                                <i class="fa fa-question text-emerald-600 text-2xl"></i>
                            </div>
                            <div>
                                <h3 class="text-lg font-bold text-gray-800 mb-1">Express Web phù hợp với ai?</h3>
                                <p class="text-gray-600">Phù hợp cho doanh nghiệp nhỏ, startup, cá nhân cần website nhanh, chi phí hợp lý.</p>
                            </div>
                        </div>
                    </div>
                    <div class="bg-white rounded-xl shadow-md border border-gray-100 p-6">
                        <div class="flex items-start">
                            <div class="w-10 h-10 flex items-center justify-center rounded-full bg-emerald-50 mr-4 mt-1">
                                <i class="fa fa-question text-emerald-600 text-2xl"></i>
                            </div>
                            <div>
                                <h3 class="text-lg font-bold text-gray-800 mb-1">Có thể nâng cấp thêm tính năng không?</h3>
                                <p class="text-gray-600">Hoàn toàn có thể nâng cấp về sau theo nhu cầu phát triển của bạn.</p>
                            </div>
                        </div>
                    </div>
                    <div class="bg-white rounded-xl shadow-md border border-gray-100 p-6">
                        <div class="flex items-start">
                            <div class="w-10 h-10 flex items-center justify-center rounded-full bg-emerald-50 mr-4 mt-1">
                                <i class="fa fa-question text-emerald-600 text-2xl"></i>
                            </div>
                            <div>
                                <h3 class="text-lg font-bold text-gray-800 mb-1">CSlant có hỗ trợ bảo trì không?</h3>
                                <p class="text-gray-600">Chúng tôi có các gói bảo trì, hỗ trợ kỹ thuật và nâng cấp website theo yêu cầu.</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="text-center mt-12">
                    <button x-data @click="$store.modal.open()" class="inline-block bg-emerald-600 text-white px-8 py-3 rounded-full font-bold shadow-lg hover:bg-emerald-700 hover:cursor-pointer transition">Gửi câu hỏi cho CSlant</button>
                </div>
            </div>
        </div>
    </div>
</main>
@endsection
