@extends('layouts.default')

@section('content')
    <main class="py-16 bg-gray-50 min-h-screen">
        <div class="container mx-auto px-6">
            <div class="text-center mb-12">
                <h1 class="text-4xl md:text-5xl font-bold text-emerald-700 mb-4">{{ __('messages.landing.contact_title') }}</h1>
                <p class="text-gray-500 max-w-2xl mx-auto">{{ __('messages.landing.contact_description') }}</p>
            </div>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-12 items-start">
                <!-- Thông tin liên hệ -->
                <div>
                    <div class="bg-white p-8 rounded-lg shadow-lg mb-8">
                        <h2 class="text-2xl font-bold text-gray-800 mb-4 flex items-center"><i class="fa fa-phone-alt text-emerald-600 mr-2"></i> {{ __('messages.landing.contact_info') }}</h2>
                        <ul class="text-gray-700 space-y-4">
                            <li><i class="fa fa-map-marker-alt text-emerald-600 mr-2"></i> Địa chỉ: {{ siteSettings()->address }}</li>
                            <li><i class="fa fa-envelope text-emerald-600 mr-2"></i> Email: <a href="mailto:{{ siteSettings()->email }}" class="text-emerald-600 hover:underline">{{ siteSettings()->email }}</a></li>
                            <li><i class="fa fa-phone text-emerald-600 mr-2"></i> Hotline: <a href="tel:{{ siteSettings()->phone }}" class="text-emerald-600 font-semibold hover:underline">{{ siteSettings()->phone }}</a></li>
                            <li><i class="fa fa-globe text-emerald-600 mr-2"></i> Website: <a href="https://cslant.net" class="text-emerald-600 hover:underline">cslant.net</a></li>
                        </ul>
                    </div>
                    <div class="rounded-lg overflow-hidden shadow-lg">
                        <iframe src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d979.8308878470816!2d106.69129656960465!3d10.786512416744648!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x31752ffd194e31f9%3A0xba0f4c492c3eb55!2sCSlant%20Solutions!5e0!3m2!1sen!2s!4v1750926921048!5m2!1sen!2s" width="100%" height="260" style="border:0;" allowfullscreen="" loading="lazy" referrerpolicy="no-referrer-when-downgrade"></iframe>
                    </div>
                </div>
                <!-- Form liên hệ -->
                <div class="bg-white p-8 rounded-lg shadow-lg">
                    <h2 class="text-2xl font-bold text-gray-800 mb-6">{{ __('messages.landing.send_request') }}</h2>
                    <p class="text-gray-600 mb-6">{{ __('messages.landing.contact_form_subtitle') }}</p>
                    <form>
                        <div class="mb-4">
                            <label for="name" class="block text-gray-700 font-medium mb-2">{{ __('messages.modal.name_label') }}</label>
                            <input type="text" id="name" name="name" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-emerald-500" required>
                        </div>
                        <div class="mb-4">
                            <label for="phone" class="block text-gray-700 font-medium mb-2">{{ __('messages.modal.phone_label') }}</label>
                            <input type="tel" id="phone" name="phone" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-emerald-500" required>
                        </div>
                        <div class="mb-4">
                            <label for="email" class="block text-gray-700 font-medium mb-2">{{ __('messages.modal.email_label') }}</label>
                            <input type="email" id="email" name="email" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-emerald-500" required>
                        </div>
                        <div class="mb-6">
                            <label for="message" class="block text-gray-700 font-medium mb-2">{{ __('messages.landing.message_content') }}</label>
                            <textarea id="message" name="message" rows="4" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-emerald-500" placeholder="{{ __('messages.landing.message_placeholder') }}"></textarea>
                        </div>
                        <div>
                            <button type="submit" class="w-full bg-emerald-600 text-white font-bold py-3 px-8 rounded-lg hover:bg-emerald-700 transition duration-300">
                                {{ __('messages.landing.send_request') }}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </main>
@endsection
