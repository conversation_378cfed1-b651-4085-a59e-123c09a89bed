@extends('layouts.default')

@section('content')
<main class="py-16 bg-gray-50 min-h-screen">
    <div class="container mx-auto px-6">
        <div class="text-center mb-12">
            <h1 class="text-4xl md:text-5xl font-bold text-emerald-700 mb-4">{{ __('messages.landing.process_title') }}</h1>
            <p class="text-gray-500 max-w-2xl mx-auto">{{ __('messages.landing.process_description') }}</p>
        </div>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-10">
            <!-- Bước 1 -->
            <div class="bg-white rounded-xl shadow-md hover:shadow-xl transition-all duration-300 border border-gray-100 p-8 text-center flex flex-col items-center">
                <div class="w-16 h-16 flex items-center justify-center rounded-full bg-emerald-50 mb-4">
                    <i class="fa fa-comments text-3xl text-emerald-600"></i>
                </div>
                <h3 class="text-xl font-bold text-gray-800 mb-2">{{ __('messages.landing.step1_title') }}</h3>
                <p class="text-gray-600">{{ __('messages.landing.step1_desc') }}</p>
            </div>
            <!-- Bước 2 -->
            <div class="bg-white rounded-xl shadow-md hover:shadow-xl transition-all duration-300 border border-gray-100 p-8 text-center flex flex-col items-center">
                <div class="w-16 h-16 flex items-center justify-center rounded-full bg-emerald-50 mb-4">
                    <i class="fa fa-search text-3xl text-emerald-600"></i>
                </div>
                <h3 class="text-xl font-bold text-gray-800 mb-2">{{ __('messages.landing.step2_title') }}</h3>
                <p class="text-gray-600">{{ __('messages.landing.step2_desc') }}</p>
            </div>
            <!-- Bước 3 -->
            <div class="bg-white rounded-xl shadow-md hover:shadow-xl transition-all duration-300 border border-gray-100 p-8 text-center flex flex-col items-center">
                <div class="w-16 h-16 flex items-center justify-center rounded-full bg-emerald-50 mb-4">
                    <i class="fa fa-pencil-ruler text-3xl text-emerald-600"></i>
                </div>
                <h3 class="text-xl font-bold text-gray-800 mb-2">{{ __('messages.landing.step3_title') }}</h3>
                <p class="text-gray-600">{{ __('messages.landing.step3_desc') }}</p>
            </div>
            <!-- Bước 4 -->
            <div class="bg-white rounded-xl shadow-md hover:shadow-xl transition-all duration-300 border border-gray-100 p-8 text-center flex flex-col items-center">
                <div class="w-16 h-16 flex items-center justify-center rounded-full bg-emerald-50 mb-4">
                    <i class="fa fa-code text-3xl text-emerald-600"></i>
                </div>
                <h3 class="text-xl font-bold text-gray-800 mb-2">{{ __('messages.landing.step4_title') }}</h3>
                <p class="text-gray-600">{{ __('messages.landing.step4_desc') }}</p>
            </div>
            <!-- Bước 5 -->
            <div class="bg-white rounded-xl shadow-md hover:shadow-xl transition-all duration-300 border border-gray-100 p-8 text-center flex flex-col items-center">
                <div class="w-16 h-16 flex items-center justify-center rounded-full bg-emerald-50 mb-4">
                    <i class="fa fa-clipboard-check text-3xl text-emerald-600"></i>
                </div>
                <h3 class="text-xl font-bold text-gray-800 mb-2">{{ __('messages.landing.step5_title') }}</h3>
                <p class="text-gray-600">{{ __('messages.landing.step5_desc') }}</p>
            </div>
            <!-- Bước 6 -->
            <div class="bg-white rounded-xl shadow-md hover:shadow-xl transition-all duration-300 border border-gray-100 p-8 text-center flex flex-col items-center">
                <div class="w-16 h-16 flex items-center justify-center rounded-full bg-emerald-50 mb-4">
                    <i class="fa fa-handshake text-3xl text-emerald-600"></i>
                </div>
                <h3 class="text-xl font-bold text-gray-800 mb-2">6. Bàn giao & Bảo trì</h3>
                <p class="text-gray-600">Bàn giao sản phẩm, hướng dẫn sử dụng và hỗ trợ bảo trì, nâng cấp lâu dài.</p>
            </div>
        </div>
        <div class="text-center mt-16">
            <a href="/lien-he" class="inline-block bg-emerald-600 text-white px-8 py-3 rounded-full font-bold shadow-lg hover:bg-emerald-700 transition">Liên hệ tư vấn ngay</a>
        </div>
    </div>
</main>
@endsection
