@extends('layouts.default')

@section('content')
<main> 
    <!-- Core Values Section -->
    <section class="py-16 bg-gray-50">
        <div class="container mx-auto px-6">
            <div class="text-center mb-16">
                <h2 class="text-3xl md:text-4xl font-bold text-gray-800 mb-4">{{ __('messages.landing.core_values') }}</h2>
                <p class="text-gray-500 max-w-2xl mx-auto">{{ __('messages.landing.core_values_description') }}</p>
            </div>
            <div class="grid md:grid-cols-3 gap-8">
                <div class="bg-emerald-50 p-8 rounded-xl shadow-md hover:shadow-xl transition-all duration-300 border border-gray-100 group">
                    <div class="w-12 h-12 bg-white rounded-lg flex items-center justify-center mb-4 mx-auto">
                        <svg class="w-6 h-6 text-emerald-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01"></path>
                        </svg>
                    </div>
                    <h4 class="text-xl font-bold text-gray-800 mb-2 text-center">{{ __('messages.landing.value_dedication') }}</h4>
                    <p class="text-gray-600 text-center">{{ __('messages.landing.value_dedication_desc') }}</p>
                </div>
                <div class="bg-emerald-50 p-8 rounded-xl shadow-md hover:shadow-xl transition-all duration-300 border border-gray-100 group">
                    <div class="w-12 h-12 bg-white rounded-lg flex items-center justify-center mb-4 mx-auto">
                        <svg class="w-6 h-6 text-emerald-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 17v-2a4 4 0 014-4h2m4 4v2a4 4 0 01-4 4H7a4 4 0 01-4-4v-2a4 4 0 014-4h2"></path>
                        </svg>
                    </div>
                    <h4 class="text-xl font-bold text-gray-800 mb-2 text-center">{{ __('messages.landing.value_quality_full') }}</h4>
                    <p class="text-gray-600 text-center">{{ __('messages.landing.value_quality_full_desc') }}</p>
                </div>
                <div class="bg-emerald-50 p-8 rounded-xl shadow-md hover:shadow-xl transition-all duration-300 border border-gray-100 group">
                    <div class="w-12 h-12 bg-white rounded-lg flex items-center justify-center mb-4 mx-auto">
                        <svg class="w-6 h-6 text-emerald-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3"></path>
                        </svg>
                    </div>
                    <h4 class="text-xl font-bold text-gray-800 mb-2 text-center">{{ __('messages.landing.value_creativity') }}</h4>
                    <p class="text-gray-600 text-center">{{ __('messages.landing.value_creativity_desc') }}</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Team Section -->
    <section class="py-16 bg-gray-50">
        <div class="container mx-auto px-6">
            <div class="text-center mb-16">
                <h2 class="text-3xl md:text-4xl font-bold text-gray-800 mb-4">{{ __('messages.landing.team_title') }}</h2>
                <p class="text-gray-500 max-w-2xl mx-auto">{{ __('messages.landing.team_description') }}</p>
            </div>
            <div class="grid md:grid-cols-3 gap-10">
                <div class="bg-white rounded-xl shadow-lg p-8 text-center hover:shadow-emerald-200 transition-shadow duration-300">
                    <img src="https://randomuser.me/api/portraits/men/32.jpg" alt="{{ __('messages.landing.team_member_1_name') }}" class="w-24 h-24 rounded-full mx-auto mb-4 border-4 border-emerald-100">
                    <h4 class="font-bold text-xl text-gray-800 mb-1">{{ __('messages.landing.team_member_1_name') }}</h4>
                    <p class="text-emerald-600 mb-2">{{ __('messages.landing.team_member_1_position') }}</p>
                    <p class="text-gray-600 text-sm">{{ __('messages.landing.team_member_1_desc') }}</p>
                </div>
                <div class="bg-white rounded-xl shadow-lg p-8 text-center hover:shadow-emerald-200 transition-shadow duration-300">
                    <img src="https://randomuser.me/api/portraits/women/44.jpg" alt="{{ __('messages.landing.team_member_2_name') }}" class="w-24 h-24 rounded-full mx-auto mb-4 border-4 border-emerald-100">
                    <h4 class="font-bold text-xl text-gray-800 mb-1">{{ __('messages.landing.team_member_2_name') }}</h4>
                    <p class="text-emerald-600 mb-2">{{ __('messages.landing.team_member_2_position') }}</p>
                    <p class="text-gray-600 text-sm">{{ __('messages.landing.team_member_2_desc') }}</p>
                </div>
                <div class="bg-white rounded-xl shadow-lg p-8 text-center hover:shadow-emerald-200 transition-shadow duration-300">
                    <img src="https://randomuser.me/api/portraits/men/65.jpg" alt="{{ __('messages.landing.team_member_3_name') }}" class="w-24 h-24 rounded-full mx-auto mb-4 border-4 border-emerald-100">
                    <h4 class="font-bold text-xl text-gray-800 mb-1">{{ __('messages.landing.team_member_3_name') }}</h4>
                    <p class="text-emerald-600 mb-2">{{ __('messages.landing.team_member_3_position') }}</p>
                    <p class="text-gray-600 text-sm">{{ __('messages.landing.team_member_3_desc') }}</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Commitment Section -->
    <section class="py-16 bg-white">
        <div class="container mx-auto px-6">
            <div class="text-center mb-12">
                <h2 class="text-3xl md:text-4xl font-bold text-gray-800 mb-4">{{ __('messages.landing.commitment_title') }}</h2>
                <div class="w-20 h-1 bg-emerald-500 mx-auto"></div>
            </div>
            <div class="grid md:grid-cols-2 gap-12">
                <div class="flex items-start">
                    <div class="bg-emerald-100 p-4 rounded-lg mr-6">
                        <svg class="w-8 h-8 text-emerald-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                    </div>
                    <div>
                        <h4 class="font-bold text-lg text-gray-800 mb-1">{{ __('messages.landing.commitment_1_title') }}</h4>
                        <p class="text-gray-600">{{ __('messages.landing.commitment_1_desc') }}</p>
                    </div>
                </div>
                <div class="flex items-start">
                    <div class="bg-emerald-100 p-4 rounded-lg mr-6">
                        <svg class="w-8 h-8 text-emerald-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3"></path>
                        </svg>
                    </div>
                    <div>
                        <h4 class="font-bold text-lg text-gray-800 mb-1">{{ __('messages.landing.commitment_2_title') }}</h4>
                        <p class="text-gray-600">{{ __('messages.landing.commitment_2_desc') }}</p>
                    </div>
                </div>
                <div class="flex items-start">
                    <div class="bg-emerald-100 p-4 rounded-lg mr-6">
                        <svg class="w-8 h-8 text-emerald-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 17v-2a4 4 0 014-4h2m4 4v2a4 4 0 01-4 4H7a4 4 0 01-4-4v-2a4 4 0 014-4h2"></path>
                        </svg>
                    </div>
                    <div>
                        <h4 class="font-bold text-lg text-gray-800 mb-1">{{ __('messages.landing.commitment_3_title') }}</h4>
                        <p class="text-gray-600">{{ __('messages.landing.commitment_3_desc') }}</p>
                    </div>
                </div>
                <div class="flex items-start">
                    <div class="bg-emerald-100 p-4 rounded-lg mr-6">
                        <svg class="w-8 h-8 text-emerald-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01"></path>
                        </svg>
                    </div>
                    <div>
                        <h4 class="font-bold text-lg text-gray-800 mb-1">{{ __('messages.landing.commitment_4_title') }}</h4>
                        <p class="text-gray-600">{{ __('messages.landing.commitment_4_desc') }}</p>
                    </div>
                </div>
            </div>
        </div>
    </section>
</main>
@endsection 
