@extends('layouts.default')

@section('content')
<main class="container mx-auto px-6 py-12">
    <!-- Breadcrumb -->
    @php
        $breadcrums = [
            [
                'text' => __('messages.navigation.home'),
                'url' => route('home')
            ],
            [
                'text' => __('messages.navigation.templates'),
            ],
        ];
    @endphp
    <div class="mb-6">
        <x-ui.breadcrumb :items="$breadcrums" />
    </div>

    <!-- Advertisement Banner -->
    <section class="mb-12 w-full">
        <div class="rounded-xl overflow-hidden">
            <img src="https://img.freepik.com/free-vector/pagina-de-inicio-abstracta-dibujada-mano_52683-107317.jpg" alt="{{ __('messages.website.advertisement') }}" class="w-full h-48 object-cover">
        </div>
    </section>

    <div class="flex flex-col lg:flex-row gap-8">
        <!-- Left Column - Filters -->
        <aside class="w-full lg:w-1/4">
            <div class="bg-white p-8 rounded-xl shadow-lg border border-gray-100 sticky top-24 h-[calc(100vh-8rem)] overflow-y-auto">
                <h2 class="text-2xl font-extrabold text-emerald-700 mb-8 pb-3 border-b border-gray-100">{{ __('messages.website.filters') }}</h2>
                
                <!-- Search -->
                <div class="mb-8">
                    <label for="search-template" class="block text-sm font-semibold text-gray-700 mb-3 uppercase tracking-wider text-emerald-700">{{ __('messages.website.search') }}</label>
                    <div class="relative">
                        <x-form.input name="search" placeholder="{{ __('messages.website.search_placeholder') }}" />
                        <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                            <i class="fa-solid fa-search text-gray-400"></i>
                        </div>
                    </div>
                </div>

                <!-- Sort By -->
                <div class="mb-8">
                    <label for="sort-by" class="block text-sm font-semibold text-gray-700 mb-3 uppercase tracking-wider text-emerald-700">{{ __('messages.website.sort_by') }}</label>
                    <x-form.select name="sort_by" :options="['newest' => __('messages.website.newest'), 'popular' => __('messages.website.popular')]" />
                </div>

                <!-- Feature Filter -->
                <div class="mb-8">
                    <label class="block text-sm font-semibold text-gray-700 mb-4 uppercase tracking-wider text-emerald-700">Tính năng</label>
                    <div class="space-y-2">
                        <x-form.checkbox name="features[]" value="cart" id="chk-cart" label="Giỏ hàng" />
                        <x-form.checkbox name="features[]" value="booking" id="chk-booking" label="Đặt lịch hẹn" />
                        <x-form.checkbox name="features[]" value="booking_online" id="chk-booking_online" label="Booking Online" />
                        <x-form.checkbox name="features[]" value="contact_form" id="chk-contact_form" label="Form liên hệ" />
                    </div>
                </div>

                <!-- Categories Section -->
                <div class="pt-6 border-t-2 border-gray-100">
                    <h3 class="text-lg font-bold text-emerald-700 mb-4">Danh mục</h3>
                    <div class="space-y-2">
                        @foreach($categories as $category)
                        @php
                            $isActive = request()->route('category_slug') === $category->slug;
                        @endphp
                        <a href="{{ route('templates.category', $category->slug) }}" class="flex justify-between items-center p-3 rounded-lg transition-colors duration-200 {{ $isActive ? 'bg-emerald-50 text-emerald-700 font-medium' : 'text-gray-700 hover:bg-emerald-50 hover:text-emerald-700' }}">
                            <span>{{ $category->name }}</span>
                            <span class="text-xs {{ $isActive ? 'bg-emerald-100 text-emerald-800' : 'bg-gray-100 text-gray-600' }} px-2 py-1 rounded-full">{{ $category->templates_count }}</span>
                        </a>
                        @endforeach
                    </div>
                </div>
            </div>
        </aside>

        <!-- Right Column - Template Grid -->
        <div class="flex-1">
            <!-- Template Grid -->
            <div id="template-grid" class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-2 xl:grid-cols-3 gap-8">
                @foreach($templates as $template)
                <x-card.template-card :template="$template" />
                @endforeach
            </div>

            <!-- Pagination -->
            <div id="pagination" class="mt-12">
                {{ $templates->links() }}
            </div>
        </div>
    </div>
</main>
@endsection
