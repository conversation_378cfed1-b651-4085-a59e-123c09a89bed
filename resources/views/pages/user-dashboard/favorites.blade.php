@php
/**
 * @var \Illuminate\Database\Eloquent\Collection<int, \App\Models\Template> $favoriteTemplates
 */
@endphp
@extends('layouts.default')

@section('title', __('messages.template.my_favorites'))

@section('content')
<div class="min-h-screen bg-gray-50 py-8">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="mb-8">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-extrabold text-emerald-600 tracking-tight">{{ __('messages.template.my_favorites') }}</h1>
                    <p class="mt-2 text-gray-600">{{ __('messages.template.favorites_description') }}</p>
                </div>
                <a href="{{ route('user-dashboard') }}"
                   class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"/>
                    </svg>
                    {{ __('messages.common.back_to_dashboard') }}
                </a>
            </div>
        </div>

        @if($favoriteTemplates->count() > 0)
            <!-- Templates Grid -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                @foreach($favoriteTemplates as $template)
                    <x-card.template-card-v2
                        :template="$template"
                        :show-favorite-button="true"
                        :show-category="true"
                        :show-description="true"
                        :show-actions="true"
                    />
                @endforeach
            </div>

            <!-- Pagination -->
            <div class="mt-8">
                {{ $favoriteTemplates->links() }}
            </div>
        @else
            <!-- Empty State -->
            <div class="text-center py-12">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"/>
                </svg>
                <h3 class="mt-2 text-sm font-medium text-gray-900">{{ __('messages.template.no_favorites') }}</h3>
                <p class="mt-1 text-sm text-gray-500">{{ __('messages.template.no_favorites_description') }}</p>
                <div class="mt-6">
                    <a href="{{ route('templates.index') }}" 
                       class="inline-flex items-center gap-2 bg-gradient-to-r from-emerald-500 to-emerald-700 text-white px-5 py-2 rounded-lg shadow hover:from-emerald-600 hover:to-emerald-800 transition font-semibold">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                        </svg>
                        {{ __('messages.template.browse_templates') }}
                    </a>
                </div>
            </div>
        @endif
    </div>
</div>

@endsection
