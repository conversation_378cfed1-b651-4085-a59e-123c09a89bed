@extends('layouts.default')

@section('content')
    <div class="container mx-auto py-8">
        <h1 class="text-3xl font-extrabold mb-8 text-center text-emerald-600 tracking-tight">{{ __('messages.pages.user_details') }}</h1>
        <div x-data="{ tab: 'demo' }">
            <div class="mb-6 border-b border-gray-200 flex justify-center">
                <nav class="flex space-x-2 bg-gray-100 rounded-lg p-1 mb-1" aria-label="Tabs">
                    <button @click="tab = 'demo'"
                        :class="tab === 'demo' ? 'bg-emerald-600 text-white shadow' :
                            'text-emerald-600 hover:text-emerald-700'"
                        class="px-6 py-2 rounded-lg font-semibold focus:outline-none transition-all duration-200">{{ __('messages.pages.demo_pages') }}</button>
                    <button @click="tab = 'account'"
                        :class="tab === 'account' ? 'bg-emerald-600 text-white shadow' :
                            'text-emerald-600 hover:text-emerald-700'"
                        class="px-6 py-2 rounded-lg font-semibold focus:outline-none transition-all duration-200">{{ __('messages.pages.account_details') }}</button>
                    <button @click="tab = 'company'"
                        :class="tab === 'company' ? 'bg-emerald-600 text-white shadow' :
                            'text-emerald-600 hover:text-emerald-700'"
                        class="px-6 py-2 rounded-lg font-semibold focus:outline-none transition-all duration-200">{{ __('messages.pages.company_details') }}</button>
                </nav>
            </div>
            <!-- Demo Pages Tab -->
            <div x-show="tab === 'demo'" x-transition>
                <div class="flex justify-end mb-4">
                    <button
                        class="flex items-center gap-2 bg-gradient-to-r from-emerald-500 to-emerald-700 text-white px-5 py-2 rounded-lg shadow hover:from-emerald-600 hover:to-emerald-800 transition font-semibold">
                        <svg xmlns='http://www.w3.org/2000/svg' class='h-5 w-5' fill='none' viewBox='0 0 24 24'
                            stroke='currentColor'>
                            <path stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M12 4v16m8-8H4' />
                        </svg>
                        {{ __('messages.pages.add_demo_page') }}
                    </button>
                </div>
                <div class="overflow-x-auto rounded-lg shadow">
                    <table class="min-w-full bg-white border border-gray-200 rounded-lg">
                        <thead class="bg-emerald-50">
                            <tr>
                                <th class="px-6 py-3 border-b text-left text-xs font-bold text-emerald-700 uppercase">{{ __('messages.pages.id') }}</th>
                                <th class="px-6 py-3 border-b text-left text-xs font-bold text-emerald-700 uppercase">{{ __('messages.pages.page_name') }}</th>
                                <th class="px-6 py-3 border-b text-left text-xs font-bold text-emerald-700 uppercase">{{ __('messages.pages.path') }}</th>
                                <th class="px-6 py-3 border-b text-left text-xs font-bold text-emerald-700 uppercase">{{ __('messages.pages.actions') }}</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- Sample data -->
                            <tr class="hover:bg-emerald-50 transition">
                                <td class="px-6 py-4 border-b">1</td>
                                <td class="px-6 py-4 border-b font-semibold text-emerald-700">Demo Page 1</td>
                                <td class="px-6 py-4 border-b text-emerald-600">/demo-page-1</td>
                                <td class="px-6 py-4 border-b space-x-2">
                                    <button
                                        class="inline-flex items-center text-emerald-600 hover:text-emerald-800 font-medium transition"><svg
                                            xmlns='http://www.w3.org/2000/svg' class='h-5 w-5 mr-1' fill='none'
                                            viewBox='0 0 24 24' stroke='currentColor'>
                                            <path stroke-linecap='round' stroke-linejoin='round' stroke-width='2'
                                                d='M15 12H9m12 0A9 9 0 11 3 12a9 9 0 0118 0z' />
                                        </svg>Xem</button>
                                    <button
                                        class="inline-flex items-center text-yellow-600 hover:text-yellow-800 font-medium transition"><svg
                                            xmlns='http://www.w3.org/2000/svg' class='h-5 w-5 mr-1' fill='none'
                                            viewBox='0 0 24 24' stroke='currentColor'>
                                            <path stroke-linecap='round' stroke-linejoin='round' stroke-width='2'
                                                d='M15.232 5.232l3.536 3.536M9 13h3l8-8a2.828 2.828 0 00-4-4l-8 8v3zm0 0v3a2 2 0 002 2h3' />
                                        </svg>Sửa</button>
                                    <button
                                        class="inline-flex items-center text-red-600 hover:text-red-800 font-medium transition"><svg
                                            xmlns='http://www.w3.org/2000/svg' class='h-5 w-5 mr-1' fill='none'
                                            viewBox='0 0 24 24' stroke='currentColor'>
                                            <path stroke-linecap='round' stroke-linejoin='round' stroke-width='2'
                                                d='M6 18L18 6M6 6l12 12' />
                                        </svg>Xóa</button>
                                </td>
                            </tr>
                            <tr class="hover:bg-emerald-50 transition">
                                <td class="px-6 py-4 border-b">2</td>
                                <td class="px-6 py-4 border-b font-semibold text-emerald-700">Demo Page 2</td>
                                <td class="px-6 py-4 border-b text-emerald-600">/demo-page-2</td>
                                <td class="px-6 py-4 border-b space-x-2">
                                    <button
                                        class="inline-flex items-center text-emerald-600 hover:text-emerald-800 font-medium transition"><svg
                                            xmlns='http://www.w3.org/2000/svg' class='h-5 w-5 mr-1' fill='none'
                                            viewBox='0 0 24 24' stroke='currentColor'>
                                            <path stroke-linecap='round' stroke-linejoin='round' stroke-width='2'
                                                d='M15 12H9m12 0A9 9 0 11 3 12a9 9 0 0118 0z' />
                                        </svg>Xem</button>
                                    <button
                                        class="inline-flex items-center text-yellow-600 hover:text-yellow-800 font-medium transition"><svg
                                            xmlns='http://www.w3.org/2000/svg' class='h-5 w-5 mr-1' fill='none'
                                            viewBox='0 0 24 24' stroke='currentColor'>
                                            <path stroke-linecap='round' stroke-linejoin='round' stroke-width='2'
                                                d='M15.232 5.232l3.536 3.536M9 13h3l8-8a2.828 2.828 0 00-4-4l-8 8v3zm0 0v3a2 2 0 002 2h3' />
                                        </svg>Sửa</button>
                                    <button
                                        class="inline-flex items-center text-red-600 hover:text-red-800 font-medium transition"><svg
                                            xmlns='http://www.w3.org/2000/svg' class='h-5 w-5 mr-1' fill='none'
                                            viewBox='0 0 24 24' stroke='currentColor'>
                                            <path stroke-linecap='round' stroke-linejoin='round' stroke-width='2'
                                                d='M6 18L18 6M6 6l12 12' />
                                        </svg>Xóa</button>
                                </td>
                            </tr>
                            <!-- End sample data -->
                        </tbody>
                    </table>
                </div>
            </div>
            <!-- Account Details Tab -->
            <div x-show="tab === 'account'" x-transition>
                <!-- Email Verification Status -->
                <x-notification.email-verification-status :user="$user" type="user" />

                <form method="POST" action="{{ route('user-dashboard.update') }}"
                    class="max-w-3xl mx-auto bg-white p-8 rounded-xl shadow-lg mt-8 border border-emerald-200">
                    @csrf
                    @method('POST')
                    @if (session('success'))
                        <div
                            class="mb-4 p-3 rounded bg-emerald-100 text-emerald-700 border border-emerald-300 text-center">
                            {{ session('success') }}
                        </div>
                    @endif
                    @if ($errors->any())
                        <div class="mb-4 p-3 rounded bg-red-100 text-red-700 border border-red-300">
                            <ul class="list-disc pl-5">
                                @foreach ($errors->all() as $error)
                                    <li>{{ $error }}</li>
                                @endforeach
                            </ul>
                        </div>
                    @endif
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-emerald-700 font-bold mb-2" for="name">Tên</label>
                            <div class="relative">
                                <input
                                    class="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-emerald-600 transition text-emerald-700"
                                    id="name" name="name" type="text"
                                    value="{{ old('name', $user->name ?? '') }}" maxlength="255" required>
                                <span class="absolute right-3 top-2.5 text-emerald-400">
                                    <svg xmlns='http://www.w3.org/2000/svg' class='h-5 w-5' fill='none'
                                        viewBox='0 0 24 24' stroke='currentColor'>
                                        <path stroke-linecap='round' stroke-linejoin='round' stroke-width='2'
                                            d='M5.121 17.804A13.937 13.937 0 0112 15c2.5 0 4.847.655 6.879 1.804M15 11a3 3 0 11-6 0 3 3 0 016 0z' />
                                    </svg>
                                </span>
                            </div>
                            @error('name')
                                <p class="text-red-600 text-xs mt-1">{{ $message }}</p>
                            @enderror
                        </div>
                        <div>
                            <label class="block text-emerald-700 font-bold mb-2" for="email">Email</label>
                            <div class="relative">
                                <input
                                    class="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-emerald-600 transition text-emerald-700"
                                    id="email" name="email" type="email"
                                    value="{{ old('email', $user->email ?? '') }}" required
                                    pattern="^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$" maxlength="255"
                                    title="Vui lòng nhập đúng định dạng email">
                                @error('email')
                                    <p class="text-red-600 text-xs mt-1">{{ $message }}</p>
                                @enderror
                                <span class="absolute right-3 top-2.5 text-emerald-400">
                                    <svg xmlns='http://www.w3.org/2000/svg' class='h-5 w-5' fill='none'
                                        viewBox='0 0 24 24' stroke='currentColor'>
                                        <path stroke-linecap='round' stroke-linejoin='round' stroke-width='2'
                                            d='M16 12H8m8 0a8 8 0 11-16 0 8 8 0 0116 0z' />
                                    </svg>
                                </span>
                            </div>
                        </div>
                        <div>
                            <label class="block text-emerald-700 font-bold mb-2" for="password">Mật khẩu mới</label>
                            <div class="relative">
                                <input
                                    class="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-emerald-600 transition text-emerald-700"
                                    id="password" name="password" type="password" placeholder="Nhập mật khẩu mới"
                                    autocomplete="new-password" minlength="6">
                                @error('password')
                                    <p class="text-red-600 text-xs mt-1">{{ $message }}</p>
                                @enderror
                                @error('password_confirmation')
                                    <p class="text-red-600 text-xs mt-1">{{ $message }}</p>
                                @enderror
                                <span class="absolute right-3 top-2.5 text-emerald-400">
                                    <svg xmlns='http://www.w3.org/2000/svg' class='h-5 w-5' fill='none'
                                        viewBox='0 0 24 24' stroke='currentColor'>
                                        <path stroke-linecap='round' stroke-linejoin='round' stroke-width='2'
                                            d='M12 11c0-1.104.896-2 2-2s2 .896 2 2-.896 2-2 2-2-.896-2-2zm0 0v2m0 4h.01' />
                                    </svg>
                                </span>
                            </div>
                        </div>
                        <div>
                            <label class="block text-emerald-700 font-bold mb-2" for="password_confirmation">Xác nhận mật
                                khẩu mới</label>
                            <div class="relative">
                                <input
                                    class="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-emerald-600 transition text-emerald-700"
                                    id="password_confirmation" name="password_confirmation" type="password"
                                    placeholder="Nhập lại mật khẩu mới" autocomplete="new-password">
                                <span class="absolute right-3 top-2.5 text-emerald-400">
                                    <svg xmlns='http://www.w3.org/2000/svg' class='h-5 w-5' fill='none'
                                        viewBox='0 0 24 24' stroke='currentColor'>
                                        <path stroke-linecap='round' stroke-linejoin='round' stroke-width='2'
                                            d='M12 11c0-1.104.896-2 2-2s2 .896 2 2-.896 2-2 2-2-.896-2-2zm0 0v2m0 4h.01' />
                                    </svg>
                                </span>
                            </div>
                        </div>
                    </div>
                    <div class="flex justify-end mt-6">
                        <button type="submit"
                            class="bg-gradient-to-r from-emerald-500 to-emerald-700 text-white px-6 py-2 rounded-lg font-bold shadow hover:from-emerald-600 hover:to-emerald-800 transition">Cập
                            nhật</button>
                    </div>
                    <script>
                        function validatePasswordConfirmation() {
                            const password = document.getElementById('password').value;
                            const confirm = document.getElementById('password_confirmation').value;
                            if (password !== confirm) {
                                alert('Mật khẩu xác nhận không khớp!');
                                return false;
                            }
                            return true;
                        }
                        // Attach validation to form submit
                        document.addEventListener('DOMContentLoaded', function() {
                            const form = document.querySelector('form');
                            if (form) {
                                form.onsubmit = validatePasswordConfirmation;
                            }
                        });
                    </script>
                </form>
            </div>
            <!-- Company Details Tab (custom fields) -->
            <div x-show="tab === 'company'" x-transition>
                <!-- Email Verification Status -->
                <x-notification.email-verification-status :customer="$company" type="company" :showAsAlert="true" />

                <form method="POST" action="{{ route('user-dashboard.update-company') }}"
                    class="max-w-3xl mx-auto bg-white p-8 rounded-xl shadow-lg my-8 border border-emerald-200">
                    @csrf

                    @if (session('success'))
                        <div
                            class="mb-4 p-3 rounded bg-emerald-100 text-emerald-700 border border-emerald-300 text-center">
                            {{ session('success') }}
                        </div>
                    @endif
                    @if (session('info'))
                        <div
                            class="mb-4 p-3 rounded bg-blue-100 text-blue-700 border border-blue-300 text-center">
                            {{ session('info') }}
                        </div>
                    @endif
                    @if ($errors->any())
                        <div class="mb-4 p-3 rounded bg-red-100 text-red-700 border border-red-300">
                            <ul class="list-disc pl-5">
                                @foreach ($errors->all() as $error)
                                    <li>{{ $error }}</li>
                                @endforeach
                            </ul>
                        </div>
                    @endif
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-emerald-700 font-bold mb-2" for="company_name">Tên công ty</label>
                            <input
                                class="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-emerald-600 transition text-emerald-700"
                                id="company_name" name="company_name" type="text"
                                value="{{ old('company_name', $company->business_name ?? '') }}" maxlength="255"
                                required>
                            @error('company_name')
                                <p class="text-red-600 text-xs mt-1">{{ $message }}</p>
                            @enderror
                        </div>
                        <div>
                            <label class="block text-emerald-700 font-bold mb-2" for="company_tax">Mã số thuế</label>
                            <input
                                class="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-emerald-600 transition text-emerald-700"
                                id="company_tax" name="company_tax" type="text"
                                value="{{ old('company_tax', $company->tax_code ?? '') }}" maxlength="100">
                            @error('company_tax')
                                <p class="text-red-600 text-xs mt-1">{{ $message }}</p>
                            @enderror
                        </div>
                        <div>
                            <label class="block text-emerald-700 font-bold mb-2" for="company_contact">Người liên
                                hệ</label>
                            <input
                                class="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-emerald-600 transition text-emerald-700"
                                id="company_contact" name="company_contact" type="text"
                                value="{{ old('company_contact', $company->contact_person ?? '') }}" maxlength="255">
                            @error('company_contact')
                                <p class="text-red-600 text-xs mt-1">{{ $message }}</p>
                            @enderror
                        </div>
                        <div>
                            <label class="block text-emerald-700 font-bold mb-2" for="company_phone">Số điện thoại</label>
                            <input
                                class="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-emerald-600 transition text-emerald-700"
                                id="company_phone" name="company_phone" type="text"
                                value="{{ old('company_phone', $company->phone ?? '') }}" pattern="^\+?[0-9 .-]{8,20}$"
                                maxlength="50" title="Vui lòng nhập đúng định dạng số điện thoại">
                            @error('company_phone')
                                <p class="text-red-600 text-xs mt-1">{{ $message }}</p>
                            @enderror
                        </div>
                        <div>
                            <label class="block text-emerald-700 font-bold mb-2" for="company_email">Địa chỉ email</label>
                            <input
                                class="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-emerald-600 transition text-emerald-700"
                                id="company_email" name="company_email" type="email"
                                value="{{ old('company_email', $company->email ?? '') }}"
                                pattern="^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$" maxlength="255"
                                title="Vui lòng nhập đúng định dạng email">
                            @error('company_email')
                                <p class="text-red-600 text-xs mt-1">{{ $message }}</p>
                            @enderror

                            {{-- Email verification status for company --}}
                            @if($company && $company->email && !$company->hasVerifiedEmail())
                                <div class="mt-2 p-2 bg-red-50 border border-red-200 rounded-lg">
                                    <div class="flex items-center">
                                        <svg class="w-4 h-4 text-yellow-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
                                        </svg>
                                        <div class="flex-1">
                                            <p class="text-xs font-medium text-yellow-800">
                                                {{ __('messages.email.company_email_unverified') }}
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            @endif
                            <script>
                                // Validate email and phone for Account Details
                                document.addEventListener('DOMContentLoaded', function() {
                                    const accForm = document.querySelector('form[action="{{ route('user-dashboard.update') }}"]');
                                    if (accForm) {
                                        accForm.addEventListener('submit', function(e) {
                                            const email = accForm.querySelector('#email');
                                            const emailPattern = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
                                            if (email && !emailPattern.test(email.value)) {
                                                alert('Vui lòng nhập đúng định dạng email.');
                                                email.focus();
                                                e.preventDefault();
                                                return false;
                                            }
                                            return true;
                                        });
                                    }
                                    const companyForm = document.querySelector(
                                        'form[action="{{ route('user-dashboard.update-company') }}"]');
                                    if (companyForm) {
                                        companyForm.addEventListener('submit', function(e) {
                                            const email = companyForm.querySelector('#company_email');
                                            const phone = companyForm.querySelector('#company_phone');
                                            const emailPattern = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
                                            const phonePattern = /^\+?[0-9 .-]{8,20}$/;
                                            if (email && email.value && !emailPattern.test(email.value)) {
                                                alert('Vui lòng nhập đúng định dạng email.');
                                                email.focus();
                                                e.preventDefault();
                                                return false;
                                            }
                                            if (phone && phone.value && !phonePattern.test(phone.value)) {
                                                alert('Vui lòng nhập đúng định dạng số điện thoại.');
                                                phone.focus();
                                                e.preventDefault();
                                                return false;
                                            }
                                            return true;
                                        });
                                    }
                                });
                            </script>
                        </div>
                        <div>
                            <label class="block text-emerald-700 font-bold mb-2" for="company_address">Địa chỉ công
                                ty</label>
                            <input
                                class="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-emerald-600 transition text-emerald-700"
                                id="company_address" name="company_address" type="text"
                                value="{{ old('company_address', $company->address ?? '') }}" maxlength="255">
                            @error('company_address')
                                <p class="text-red-600 text-xs mt-1">{{ $message }}</p>
                            @enderror
                        </div>
                        <div>
                            <label class="block text-emerald-700 font-bold mb-2" for="company_city">Thành phố</label>
                            <input
                                class="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-emerald-600 transition text-emerald-700"
                                id="company_city" name="company_city" type="text"
                                value="{{ old('company_city', $company->city ?? '') }}" maxlength="100">
                            @error('company_city')
                                <p class="text-red-600 text-xs mt-1">{{ $message }}</p>
                            @enderror
                        </div>
                        <div>
                            <label class="block text-emerald-700 font-bold mb-2" for="company_country">Quốc gia</label>
                            <input
                                class="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-emerald-600 transition text-emerald-700"
                                id="company_country" name="company_country" type="text"
                                value="{{ old('company_country', $company->country ?? '') }}" maxlength="100">
                            @error('company_country')
                                <p class="text-red-600 text-xs mt-1">{{ $message }}</p>
                            @enderror
                        </div>
                        <div class="md:col-span-2">
                            <label class="block text-emerald-700 font-bold mb-2" for="company_website">Website công
                                ty</label>
                            <input
                                class="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-emerald-600 transition text-emerald-700"
                                id="company_website" name="company_website" type="text"
                                value="{{ old('company_website', $company->website ?? '') }}" maxlength="255">
                            @error('company_website')
                                <p class="text-red-600 text-xs mt-1">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>
                    <div class="flex justify-end mt-6">
                        <button type="submit"
                            class="bg-gradient-to-r from-emerald-500 to-emerald-700 text-white px-6 py-2 rounded-lg font-bold shadow hover:from-emerald-600 hover:to-emerald-800 transition">Cập
                            nhật</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
@endsection
