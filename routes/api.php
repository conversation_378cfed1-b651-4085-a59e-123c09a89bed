<?php

use App\Http\Controllers\Api\TrialWebsiteApiController;
use App\Http\Controllers\LeadController;
use App\Http\Controllers\WebsiteController;
use Illuminate\Support\Facades\Route;

Route::post('/lead', [LeadController::class, 'store']);
Route::post('/website', [WebsiteController::class, 'register']);
Route::get('/check-email', [TrialWebsiteApiController::class, 'checkEmail']);
Route::get('/check-subdomain', [TrialWebsiteApiController::class, 'checkSubdomain']);

// Business Verification API routes
Route::prefix('business-verification')->group(function () {
    require __DIR__ . '/api/business-verification.php';
});
