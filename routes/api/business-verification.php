<?php

use App\Http\Controllers\BusinessVerificationController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Business Verification API Routes
|--------------------------------------------------------------------------
|
| Here are the API routes for business verification functionality.
| These routes are prefixed with 'api/business-verification' and
| require authentication.
|
*/

Route::middleware(['auth:sanctum'])->group(function () {
    // Get verification status
    Route::get('/status', [BusinessVerificationController::class, 'getStatus'])
        ->name('api.business-verification.status');
    
    // Upload ERC file
    Route::post('/upload', [BusinessVerificationController::class, 'upload'])
        ->name('api.business-verification.upload');
    
    // Download ERC file (for customer to view their own file)
    Route::get('/download', [BusinessVerificationController::class, 'download'])
        ->name('api.business-verification.download');
    
    // Delete uploaded file
    Route::delete('/file', [BusinessVerificationController::class, 'deleteFile'])
        ->name('api.business-verification.delete');
    
    // Get verification history
    Route::get('/history', [BusinessVerificationController::class, 'getHistory'])
        ->name('api.business-verification.history');
});

// Admin API routes (protected by admin middleware)
Route::middleware(['auth:sanctum', 'admin'])->prefix('admin')->group(function () {
    // Get all verifications with filters
    Route::get('/', [BusinessVerificationController::class, 'index'])
        ->name('api.business-verification.admin.index');
    
    // Get specific verification details
    Route::get('/{verification}', [BusinessVerificationController::class, 'show'])
        ->name('api.business-verification.admin.show');
    
    // Update verification status (approve/reject)
    Route::patch('/{verification}/status', [BusinessVerificationController::class, 'updateStatus'])
        ->name('api.business-verification.admin.update-status');
    
    // Download ERC file (admin can download any file)
    Route::get('/{verification}/download', [BusinessVerificationController::class, 'adminDownload'])
        ->name('api.business-verification.admin.download');
    
    // Get statistics
    Route::get('/statistics/overview', [BusinessVerificationController::class, 'getStatistics'])
        ->name('api.business-verification.admin.statistics');
    
    // Process pending reminders manually
    Route::post('/reminders/process', [BusinessVerificationController::class, 'processReminders'])
        ->name('api.business-verification.admin.process-reminders');
});
