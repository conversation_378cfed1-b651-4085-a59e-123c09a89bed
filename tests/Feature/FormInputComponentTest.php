<?php

it('renders an input with default props', function () {
    $view = $this->blade('<x-form.input name="username" />');
    $view->assertSee('type="text"', false);
    $view->assertSee('name="username"', false);
    $view->assertSee('id="input-username"', false);
});

it('renders an input with custom type, id and value', function () {
    $view = $this->blade('<x-form.input name="email" type="email" id="custom-id" value="<EMAIL>" />');
    $view->assertSee('type="email"', false);
    $view->assertSee('id="custom-id"', false);
    $view->assertSee('name="email"', false);
    $view->assertSee('value="<EMAIL>"', false);
});

it('applies error classes when validation fails', function () {
    $view = $this->withViewErrors(['username' => 'Required.'])
        ->blade('<x-form.input name="username" />');
    $view->assertSee('border-red-500', false);
});
