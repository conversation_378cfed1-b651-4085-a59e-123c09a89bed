<?php

it('renders a button with default props', function () {
    $view = $this->blade('<x-ui.button>Click me</x-ui.button>');
    $view->assertSee('Click me');
    $view->assertSee('button', false);
    $view->assertDontSee('href=');
    $view->assertSee('inline-flex'); // base class
});

it('renders an <a> tag when href is provided', function () {
    $view = $this->blade('<x-ui.button href="/foo">Link</x-ui.button>');
    $view->assertSee('href="/foo"', false);
    $view->assertSee('Link');
    $view->assertSee('<a', false);
});

it('applies variant and size classes', function () {
    $view = $this->blade('<x-ui.button variant="danger" size="lg">Delete</x-ui.button>');
    $view->assertSee('bg-red-600');
    $view->assertSee('px-6 py-3');
    $view->assertSee('Delete');
});

it('falls back to default classes for unknown variant/size', function () {
    $view = $this->blade('<x-ui.button variant="unknown" size="unknown">Fallback</x-ui.button>');
    $view->assertSee('bg-emerald-600'); // default variant
    $view->assertSee('px-4 py-2'); // default size
    $view->assertSee('Fallback');
});
