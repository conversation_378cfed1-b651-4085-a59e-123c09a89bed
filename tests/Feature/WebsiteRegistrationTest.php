<?php

namespace Tests\Feature;

use App\Models\Role;
use App\Models\User;
use App\Models\Website;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class WebsiteRegistrationTest extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function it_can_register_a_new_website_with_user()
    {
        $response = $this->postJson('/api/website', [
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'phone' => '1234567890',
            'company' => 'Test Company',
            'purpose' => 'Testing',
            'subdomain' => 'test-subdomain',
            'package' => 'basic',
            'details' => 'Test details',
        ]);

        $response->assertStatus(201)
            ->assertJson([
                'success' => true,
                'message' => 'Website created successfully',
            ]);

        // Assert user was created
        $this->assertDatabaseHas('users', [
            'name' => 'Test User',
            'email' => '<EMAIL>',
        ]);

        // Assert website was created with correct data
        $this->assertDatabaseHas('websites', [
            'name' => 'Test User',
            'phone' => '1234567890',
            'company' => 'Test Company',
            'purpose' => 'Testing',
            'domain' => 'test-subdomain.identifynation.com',
            'package' => 'basic',
            'detail' => 'Test details',
            'status' => 'pending',
        ]);

        // Assert user has customer role
        $user = User::where('email', '<EMAIL>')->first();
        $this->assertTrue($user->hasRole(Role::CUSTOMER));
    }

    /** @test */
    public function it_requires_required_fields()
    {
        $response = $this->postJson('/api/website', []);

        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'name',
                'email',
                'phone',
                'company',
                'purpose',
                'subdomain',
                'package',
                'details',
            ]);
    }

    /** @test */
    public function it_requires_a_valid_email()
    {
        $response = $this->postJson('/api/website', [
            'email' => 'not-an-email',
        ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['email']);
    }

    /** @test */
    public function it_requires_a_unique_email()
    {
        User::factory()->create(['email' => '<EMAIL>']);

        $response = $this->postJson('/api/website', [
            'email' => '<EMAIL>',
        ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['email']);
    }
}
