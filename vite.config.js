import { defineConfig } from 'vite';
import laravel from 'laravel-vite-plugin';
import tailwindcss from '@tailwindcss/vite';
import { glob } from 'glob';

// Get all CSS and JS files in the pages directory
const pagesCssFiles = glob.sync('resources/css/pages/**/*.css');
const pagesJsFiles = glob.sync('resources/js/pages/**/*.js');

export default defineConfig({
    plugins: [
        laravel({
            input: [
                'resources/css/app.css',
                'resources/js/app.js',
                ...pagesCssFiles,
                ...pagesJsFiles
            ],
            refresh: true,
        }),
        tailwindcss(),
    ],
});
